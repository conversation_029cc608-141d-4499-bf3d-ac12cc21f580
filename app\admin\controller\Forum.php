<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Forum extends Common
{
    public function index()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["p.title|p.content|u.first_name|u.last_name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('Forum_posts')->alias("p")
            ->field("p.*, u.first_name, u.last_name")
            ->where($where)
            ->leftJoin('User u', 'u.id = p.user_id')
            ->order("p.create_time desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    //审核帖子
    public function examine()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['id']){
                $this->error("参数错误！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_posts")->strict(false)->save($data);
            if ($s) {
                // 给用户发送审核通知
                $post = Db::name("Forum_posts")->where("id", $data['id'])->find();
                $review_status = $post['status']==1 ? "Review approved" : "Review failed（{$data['fail_reason']}）";
                Db::name("User_message")->insertGetId([
                    "user_id" => $post['user_id'],
                    "sender_id" => session("adminId"),
                    "content" => "The post '".$post['title']."' review results：".$review_status,
                    "main_id" => $data['id'],
                    "type" => 7,
                    "create_time" => date("Y-m-d H:i:s")
                ]);

                $this->success('审核成功！', "/admin/forum");
            } else {
                $this->error("审核失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Forum_posts")->where("id", $id)->find();

            //标签
            $where = [
                "post_id" => $id,
                "type" => 0
            ];
            $selectedTags = Db::name("Forum_posts_tags")->where($where)->order("sort asc")->column("tag_name");

            return view("", [
                "getone" => $getone,
                "selectedTags" => $selectedTags?implode(", ", $selectedTags):"",
            ]);
        }
    }

    public function del()
    {
        $id = input('id');

        $s = Db::name("Forum_posts")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    public function reply()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["p.content|u.first_name|u.last_name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('Forum_reply')->alias("p")
            ->field("p.*, u.first_name, u.last_name")
            ->where($where)
            ->leftJoin('User u', 'u.id = p.user_id')
            ->order("p.create_time desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    //审核回复
    public function examine_reply()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['id']){
                $this->error("参数错误！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_reply")->strict(false)->save($data);
            if ($s) {
                // 给用户发送审核通知
                $post_title = Db::name("Forum_posts")->where("id", $data['post_id'])->value("title");
                $reply = Db::name("Forum_reply")->where("id", $data['id'])->find();
                $review_status = $reply['status']==1 ? "Review approved" : "Review failed（{$data['fail_reason']}）";
                Db::name("User_message")->insertGetId([
                    "user_id" => $reply['user_id'],
                    "sender_id" => session("adminId"),
                    "content" => "The post '".$post_title."' comment review results：".$review_status,
                    "main_id" => $data['post_id'],
                    "type" => 7,
                    "create_time" => date("Y-m-d H:i:s")
                ]);

                $this->success('审核成功！', "/admin/forum/reply");
            } else {
                $this->error("审核失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Forum_reply")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_reply()
    {
        $id = input('id');

        $s = Db::name("Forum_reply")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    public function topic()
    {
        $List = Db::name('Forum_topic')
        ->order("sort asc")
        ->paginate([
            'query'     =>  request()->param(), //url额外参数
            'list_rows' => 20, //每页数量
        ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add_topic()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            if(!empty(Db::name("Forum_topic")->where("name", $data['name'])->find())) {
                $this->error("分类已存在！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_topic")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit_topic()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            $where = [
                ["id", "<>", $data['id']],
                ["name", "=", $data['name']]
            ];
            if(!empty(Db::name("Forum_topic")->where($where)->find())) {
                $this->error("分类已存在！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_topic")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Forum_topic")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_topic()
    {
        $id = input('id');
        $s = Db::name("Forum_topic")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    public function tags()
    {
        $List = Db::name('Forum_tags')
        ->order("sort asc")
        ->paginate([
            'query'     =>  request()->param(), //url额外参数
            'list_rows' => 20, //每页数量
        ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add_tags()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            if(!empty(Db::name("Forum_tags")->where("name", $data['name'])->find())) {
                $this->error("分类已存在！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_tags")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit_tags()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            $where = [
                ["id", "<>", $data['id']],
                ["name", "=", $data['name']]
            ];
            if(!empty(Db::name("Forum_tags")->where($where)->find())) {
                $this->error("分类已存在！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_tags")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Forum_tags")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_tags()
    {
        $id = input('id');
        $s = Db::name("Forum_tags")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    //@提及列表
    public function mention() {
        $mentions = Db::name("User_message")
            ->whereIn("type", [4,5])
            ->select()
            ->each(function ($item) {
                $item['sender'] = Db::name("User")->where("id", $item['sender_id'])->value("CONCAT(first_name, ' ', last_name) as name");
                $item['user'] = Db::name("User")->where("id", $item['user_id'])->value("CONCAT(first_name, ' ', last_name) as name");

                return $item;
            });

        return view("", [
            "List" => $mentions,
        ]);
    }

}
