<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<style>
    .services-row .bor_cen thead td{
        height: 15px;
        line-height: 15px;
    }
    .services-row .bor_cen td{
        padding: 7px;
    }
    .services-row .bor_cen .mid_01, .services-row .bor_cen{
        background-color: #f5f9ff;
        width: 80%;
        margin: 0 auto;
    }
    .services-row .compile, .services-row .delete-c, .services-row .main-content, .services-row .basic, .services-row .preview{
        margin-bottom: 0px;
    }
    .bor_cen, .bor_cen tbody tr.services-row{
        border:none;
    }
    tbody>tr.services-row:hover {
        background: none;
    }
</style>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">订单列表</a></p>
                <a href="{:url('add')}" class="add-button">创建订单</a>

                <div class="search-container">
                    <form method="get" action="{:url('index')}">
                        <input
                            type="text"
                            name="keyword"
                            placeholder="订单编号或邮箱"
                            value="{$params.keyword ?? ''}"
                            class="search-input"
                        >

                        <select name="order_status" class="search-input">
                            <option value="0" {if !isset($params.order_status) || $params.order_status == 0}selected{/if}>进行中</option>
                            <option value="1" {if !isset($params.order_status) || $params.order_status == 1}selected{/if}>已完成</option>
                            <option value="2" {if !isset($params.order_status) || $params.order_status == 2}selected{/if}>已取消</option>
                        </select>

                        <button type="submit" class="search-button">搜索</button>
                    </form>
                </div>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_t">订单编号</td>
                            <td class="mid_t">Email</td>
                            <td class="mid_t">Organization</td>
                            <td class="mid_t">产品</td>
                            <td class="mid_t">服务</td>
                            <td class="mid_one">订单状态</td>
                            <td class="mid_t">创建时间</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>

                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr class="mid_02 toggle-row" data-target="services-{$vo.id}">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_t">{$vo.order_no}</td>
                            <td class="mid_t">{$vo.email}</td>
                            <td class="mid_t">{$vo.organization}</td>
                            <td class="mid_t">{$vo.product_name}</td>
                            <td class="mid_t">{$vo.service_name}</td>
                            <td class="mid_one">{$vo.order_status}</td>
                            <td class="mid_t">{$vo.create_time}</td>
                            <td class="mid_s">
                                <a href="{:url('del', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit', ['id'=>$vo['id']])}" class="basic">修改</a>
                            </td>
                        </tr>

                        {notempty name="vo.services"}
                        <tr class="services-row">
                            <td colspan="8" style="border:none;">
                                <table class="bor_cen services" id="services-{$vo.id}">
                                    <thead>
                                        <tr class="mid_01">
                                            <td class="mid_one"></td>
                                            <td class="mid_t">订单编号</td>
                                            <td class="mid_t">服务</td>
                                            <td class="mid_t">创建时间</td>
                                            <td class="mid_s">操作</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {volist name="vo.services" id="v" key="k1"}
                                        <tr class="mid_02" data-target="services-{$v.id}" data-id="{$v.id}">
                                            <td class="mid_one">{$k1}</td>
                                            <td class="mid_t">{$v.order_no}</td>
                                            <td class="mid_t">{$v.service_name}</td>
                                            <td class="mid_t">{$v.create_time}</td>
                                            <td class="mid_s">
                                                <a href="{:url('del_service', ['id'=>$v['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                <div class="button edit-orderservice basic" style="cursor: pointer;">修改</div>
                                            </td>
                                        </tr>
                                    {/volist}
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        {/notempty}
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        //修改后续服务弹窗
        $(".edit-orderservice").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改后续服务", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_service?id="+pid,
                shadeClose: true,
                area: ['674px', '500px'],
            });
        });
    </script>

</body>
</html>