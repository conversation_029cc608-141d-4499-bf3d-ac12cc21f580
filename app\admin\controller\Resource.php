<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Resource extends Common
{
    public function index()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('Resource')
            ->where($where)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            if($_FILES['file']['name']) {
                $file = $this->uploadFile(request()->file("file"), "resource");
                $data['file'] = $file['url'];
                $data['file_name'] = $file['original_name'];
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Resource")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            //分类
            $category = Db::name("Resource_category")->column("id, name");

            return view("", [
                "category" => $category
            ]);
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            if($_FILES['file']['name']) {
                $file = $this->uploadFile(request()->file("file"), 'resource');
                $data['file'] = $file['url'];
                $data['file_name'] = $file['original_name'];
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Resource")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Resource")->where("id", $id)->find();

            //分类
            $category = Db::name("Resource_category")->column("id, name");

            return view("", [
                "category" => $category,
                "getone" => $getone,
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Resource")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }


    public function category()
    {
        $List = Db::name('Resource_category')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add_category()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Resource_category")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit_category()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Resource_category")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Resource_category")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_category()
    {
        $id = input('id');
        $s = Db::name("Resource_category")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

}
