<?php
// 这是系统自动生成的公共文件

function seoFriendlyUrl($string) {
    // 转换为小写
    $string = mb_strtolower($string, 'UTF-8');

    // 替换空格和非字母数字字符为连字符
    $string = preg_replace('/[^\p{L}\p{N}]+/u', '-', $string);

    // 去除首尾的连字符
    $string = trim($string, '-');

    return $string;
}


//生成订单
function generateOrderNo()
{
    // 获取当前时间戳
    $timestamp = time();
    // 生成随机数
    $random = mt_rand(1000, 9999);
    // 组合成订单号
    $orderNo = date('YmdHis', $timestamp) . $random;

    return $orderNo;
    // 示例结果：202305271430159876
}