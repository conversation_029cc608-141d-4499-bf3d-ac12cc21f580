<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改模板
                <a href="{:url('template')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit_template')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>发件账号：</label>
                    <select name="account_id" class="l_xiang">
                        {volist name="accounts" id="vo"}
                            <option value="{$vo.id}" {if $getone.account_id == $vo.id}selected{/if}>{$vo.email}</option>
                        {/volist}
                    </select>
                    <span class="must-input">*</span>
                </div>
                <div class="class_con">
                    <label>模板标识：</label>
                    <input type="text" name="identifier" value="{$getone.identifier}" readonly class="input-readonly" />
                    <span class="must-input">*</span>
                </div>
                <div class="class_con">
                    <label>模板说明：</label>
                    <textarea name="description">{$getone.description}</textarea>
                </div>
                <div class="class_con">
                    <label>邮件主题：</label>
                    <input type="text" name="subject" value="{$getone.subject}" />
                    <span class="must-input">*</span>
                </div>
                <div class="class_con editor-container">
                    <label>邮件内容：<span class="must-input">*</span></label>
                    <textarea name="content" class="tiny-editor">{$getone.content}</textarea>
                </div>
                <div class="class_con">
                    <label>可用变量说明：</label>
                    <textarea name="variables" placeholder="例如: {{username}} - 用户名">{$getone.variables}</textarea>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('template')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>