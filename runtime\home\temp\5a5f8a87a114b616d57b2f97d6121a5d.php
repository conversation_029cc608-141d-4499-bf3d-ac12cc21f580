<?php /*a:4:{s:68:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\user\order_detail.html";i:1753155345;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1753070097;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1753067529;s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1753068496;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Order - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>
<style>
  .grid-cols-<?php echo htmlentities((string) count($product_progress)); ?> {
    grid-template-columns: repeat(<?php echo htmlentities((string) count($product_progress)); ?>, minmax(0, 1fr));
  }
</style>
<body>
        <section
        class="pb-5 md:pb-20 relative bg-[url(/static/home/<USER>/bg.jpg)] min-h-lvh">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(/static/home/<USER>/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(/static/home/<USER>/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>
        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0 relative" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full md:relative md:z-0">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            <?php echo htmlentities((string) $v['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 bg-size-[1rem] group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>

                    <?php if($basic['forum_status']==1): ?>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8 md:relative md:z-20">
                <div class="md:flex md:items-center">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-size-[1.3rem] cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="/search" method="get"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[18.75rem] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                name="q"
                                value="<?php echo isset($keyword) ? htmlentities((string) $keyword) : ''; ?>"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-size-[1.3rem] z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-[1.2rem]">
                        </div>
                        <?php if($user_message_count>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) $user_message_count); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                                md:min-w-[25rem] md:-left-[100%]" id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-tl-xl rounded-tr-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[System]</strong><span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; if(count($user_icommunity)>0 && $basic['letter_status']==1): ?>
                        <div class="bg-white shadow-2xl rounded-bl-xl rounded-br-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">iCommunity</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_icommunity) || $user_icommunity instanceof \think\Collection || $user_icommunity instanceof \think\Paginator): $i = 0; $__LIST__ = $user_icommunity;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/private-message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[<?php echo htmlentities((string) $vo['first_name']); ?> <?php echo htmlentities((string) $vo['last_name']); ?>]</strong>
                                            <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/private-message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[0.8125rem] bg-[0.75rem_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-size-[1.1rem] md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[1.3rem] cursor-pointer"
                        id="menu_btn"></button>
                </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="bg-white p-4 rounded-xl border border-[#dae9ff] md:p-8 md:pt-[2.5rem] md:pb-[6.25rem]">
                <!-- 返回导航 -->
                <div class="mb-3 md:mb-[2.5rem]">
                    <a href="/user/order/" class="text-base flex items-center gap-2 md:text-3xl md:gap-x-5">
                        <img src="/static/home/<USER>/icons/changjiantou-zuoshang1.png" alt="" class="w-[1rem] md:w-[1.7rem]">
                        <strong>
                            Order details
                        </strong>
                    </a>
                </div>
                <!-- 产品服务tab -->
                <div class="product-service w-full overflow-x-auto bg-[#f8fdff] mb-5 md:mb-[2.5rem]">
                    <div class="flex flex-nowrap product-list min-w-max text-sm arrow-container md:text-xl">
                        <!-- 产品 -->
                        <!-- 没有开始浅蓝色notstarted，进行中使用proceed橙色，已完成使用completed绿色-->
                        <div class="arrow-step <?php echo htmlentities((string) $product['progress_status']); ?>"><?php echo htmlentities((string) $product['name']); ?></div>

                        <!-- 服务 -->
                        <?php if(is_array($services) || $services instanceof \think\Collection || $services instanceof \think\Paginator): $i = 0; $__LIST__ = $services;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;if(!empty($vo['lists'])): ?>
                            <!-- 没有开始浅蓝色service-notstarted，服务启用service-proceed，服务完成service-completed-->
                            <div class="arrow-step service-<?php echo htmlentities((string) $vo['lists']['progress_status']); ?>"><?php echo htmlentities((string) $vo['name']); ?></div>
                        <?php else: ?>
                            <!-- 没有订购服务 -->
                            <div class="arrow-step"><?php echo htmlentities((string) $vo['name']); ?></div>
                        <?php endif; ?>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </div>
                </div>

                <!-- 内容容器 -->
                <div class="product-content">
                    <!-- 产品 -->
                    <div class="product-content-item">
                        <!-- 产品详情，左图右文 -->
                        <div class="product-details border-b border-[#dae9ff] pb-3 md:flex md:gap-x-[2.5rem]">
                            <div class="product-details-img w-[12.5rem] h-[10rem] bg-[#fafbff] border border-[#e0eaff] flex items-center justify-center mb-5 rounded-xl md:w-[18.75rem] md:h-[16.25rem]">
                                <figure>
                                    <img src="<?php echo htmlentities((string) $product['image_detail']); ?>" alt="<?php echo htmlentities((string) $product['name']); ?>" class="object-cover max-w-[80%] mx-auto " />
                                    <figcaption class="sr-only">product</figcaption>
                                </figure>
                            </div>
                            <div class="">
                                <div class="order-list-item-header-left text-xs mb-5 md:flex md:items-center md:text-[1rem]">
                                    <div class="mb-1 md:mb-0 md:mr-5 ">
                                        <span>Order Date: <?php echo date('j F Y', strtotime($order['create_time'])); ?></span>
                                    </div>
                                    <?php if($order['tracking_no']): ?>
                                    <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                        <span>Tracking number:
                                            <strong class="copy-text"><?php echo htmlentities((string) $order['tracking_no']); ?></strong>
                                        </span>
                                        <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                            <img src="/static/home/<USER>/icons/fuzhi.png" class="object-cover w-[1.0625rem] h-[1.0625rem]" alt="" />
                                        </span>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="md:mb-5">
                                    <h3 class="text-xl mb-3 font-bold md:text-3xl md:mb-5">
                                        <?php echo isset($product['current_progress']['name']) ? htmlentities((string) $product['current_progress']['name']) : ''; ?>
                                    </h3>
                                    <div class="w-full overflow-x-auto">
                                        <div class="product-list min-w-max relative pt-3 md:min-w-2xl md:pt-5.5">
                                            <!-- 进度条 -->
                                            <div class="absolute left-0 w-full h-[.3125rem] border border-[#dae9ff] rounded-3xl top-0 md:h-[.625rem]">
                                                <span class="absolute top-0 left-0 h-full bg-[#ec8613] rounded-3xl" style="width: calc(<?php echo htmlentities((string) $product['width_percentage']); ?> / <?php echo htmlentities((string) count($product_progress)); ?> * 100%)"></span>
                                            </div>
                                            <div class="grid grid-cols-<?php echo htmlentities((string) count($product_progress)); ?> flex-nowrap gap-x-3 text-sm md:text-base text-center">
                                                <?php if(is_array($product_progress) || $product_progress instanceof \think\Collection || $product_progress instanceof \think\Paginator): $i = 0; $__LIST__ = $product_progress;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;
                                                    $is_current = false;
                                                    $is_past = false;

                                                    // 先检查 $product['current_progress'] 是否存在且非 null
                                                    if (!empty($product['current_progress'])) {
                                                        $is_current = ($vo['id'] == $product['current_progress']['id']);
                                                        $current_index = array_search($product['current_progress'], $product_progress);
                                                        $current_index = $current_index !== false ? $current_index : PHP_INT_MAX;
                                                        $is_past = (array_search($vo, $product_progress) < $current_index);
                                                    }

                                                    $text_color = $is_current || $is_past ? 'text-[#111]' : 'text-[#999]';
                                                 ?>
                                                <span class="<?php echo htmlentities((string) $text_color); ?>"><?php echo htmlentities((string) $vo['name']); ?></span>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-sm md:text-xl md:mt-[2.5rem]">
                                        <?php echo isset($product['current_progress']['content']) ? htmlentities((string) $product['current_progress']['content']) : ''; ?>
                                    </div>

                                    <?php if($order['remark']): ?>
                                    <div class="mt-2 text-sm flex gap-x-2 bg-[#f8fdff] text-[#999] py-1 px-1.5 md:text-xl md:py-3.5 md:items-center md:px-3 md:mt-3 items-start">
                                        <img src="/static/home/<USER>/icons/tis.png" alt="" class="w-[1rem] mt-0.5 md:mt-0 flex-shrink-0">
                                        <p>
                                            <span class="text-[#ec8613]">Remark:</span> <?php echo htmlentities((string) $order['remark']); ?>
                                        </p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- 文件区域 -->
                        <?php if(!(empty($product_files) || (($product_files instanceof \think\Collection || $product_files instanceof \think\Paginator ) && $product_files->isEmpty()))): ?>
                        <div class="mt-[1.875rem]">
                            <h3 class="text-xl mb-3 font-bold md:text-[1.5625rem] md:mb-7">Technical documents</h3>
                            <div class="flex items-start gap-x-2 text-sm text-[#f01111] py-1 px-1.5 bg-[#f8fdff] mb-4 md:text-xl md:py-3.5 md:items-center md:px-3">
                                <img src="/static/home/<USER>/icons/tishi.png" class="w-[1rem] mt-0.5 md:mt-0" alt="">
                                Note: All materials are valid for 7 days from the date of upload.
                            </div>
                            <div class="">
                                <h4 class="text-lg text-[#155290] mb-2.5 md:text-xl md:mb-3">Download the file</h4>
                                <div class="text-sm download-list md:text-xl">
                                    <ul>
                                        <?php if(is_array($product_files) || $product_files instanceof \think\Collection || $product_files instanceof \think\Paginator): $i = 0; $__LIST__ = $product_files;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;if($vo['file']): ?>
                                        <li data-id="<?php echo htmlentities((string) $vo['id']); ?>" data-type="0" data-main-id="<?php echo htmlentities((string) $product['id']); ?>" class="bg-[#f8fdff] border border-[#dae9ff]">
                                            <div class="flex items-center h-full gap-x-3 md:gap-x-2">
                                                <span class="line-clamp-1"><?php echo htmlentities((string) $vo['file_name']); ?></span>
                                                <a href="<?php echo htmlentities((string) $vo['file']); ?>" download="<?php echo htmlentities((string) $vo['file_name']); ?>" class="w-[2.1875rem] download-link">
                                                    <img src="/static/home/<USER>/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-[1.1rem]">
                                                </a>
                                            </div>
                                        </li>
                                        <?php else: ?>
                                        <li data-id="<?php echo htmlentities((string) $vo['id']); ?>" data-type="0" data-main-id="<?php echo htmlentities((string) $product['id']); ?>" class="bg-[#f8fdff] border border-[#dae9ff]">
                                            <div class="flex flex-col">
                                                <a href="<?php echo htmlentities((string) $vo['url']); ?>" target="_blank" rel="noopener noreferrer" class="text-[#155797] underline download-link"><?php echo htmlentities((string) $vo['url']); ?></a>
                                                <p>
                                                    <?php echo htmlentities((string) $vo['url_describe']); ?>
                                                </p>
                                            </div>
                                        </li>
                                        <?php endif; ?>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- 服务 -->
                    <?php if(is_array($services) || $services instanceof \think\Collection || $services instanceof \think\Paginator): $i = 0; $__LIST__ = $services;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;if(!empty($vo['lists'])): ?>
                        <!--已订购该服务-->
                        <div class="product-content-item" style="display: none;">
                            <div class="product-details border-b border-[#dae9ff] pb-3 md:flex md:flex-col md:gap-x-[2.5rem]">
                                <div class="order-list-item-header-left text-xs mb-5 md:flex md:items-center md:text-[1rem]">
                                    <div class="mb-1 md:mb-0 md:mr-5 ">
                                        <span>Order Date: <?php echo date('j F Y', strtotime($vo['lists']['order']['create_time'])); ?></span>
                                    </div>
                                    <?php if($vo['lists']['order']['tracking_no']): ?>
                                    <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                        <span>Tracking number:
                                            <strong class="copy-text"><?php echo htmlentities((string) $vo['lists']['order']['tracking_no']); ?></strong>
                                        </span>
                                        <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                            <img src="/static/home/<USER>/icons/fuzhi.png" class="object-cover w-[1.0625rem] h-[1.0625rem]" alt="" />
                                        </span>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="md:mb-5">
                                    <h3 class="text-xl mb-3 font-bold md:text-3xl md:mb-5">
                                        <?php echo isset($vo['lists']['current_progress']['name']) ? htmlentities((string) $vo['lists']['current_progress']['name']) : ''; ?>
                                    </h3>
                                    <div class="w-full overflow-x-auto">
                                        <div class="product-list min-w-max relative pt-3 md:max-w-3xl md:pt-5.5">
                                            <!-- 进度条 -->
                                            <div class="absolute left-0 w-full h-[.3125rem] border border-[#dae9ff] rounded-3xl top-0 md:h-[.625rem]">
                                                <span class="absolute top-0 left-0 h-full w-<?php echo htmlentities((string) $vo['lists']['width_percentage']); ?>/<?php echo htmlentities((string) count($vo['lists']['progress'])); ?> bg-[#ec8613] rounded-3xl"></span>
                                            </div>
                                            <div class="grid grid-cols-<?php echo htmlentities((string) count($vo['lists']['progress'])); ?> flex-nowrap gap-x-3 text-sm md:text-base text-center">
                                                <?php if(is_array($vo['lists']['progress']) || $vo['lists']['progress'] instanceof \think\Collection || $vo['lists']['progress'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['lists']['progress'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;
                                                    $is_current = false;
                                                    $is_past = false;

                                                    if (!empty($vo['lists']['current_progress'])) {
                                                        $is_current = ($v['id'] == $vo['lists']['current_progress']['id']);
                                                        $current_index = array_search($vo['lists']['current_progress'], $vo['lists']['progress']);
                                                        $current_index = $current_index !== false ? $current_index : PHP_INT_MAX;
                                                        $is_past = (array_search($v, $vo['lists']['progress']) < $current_index);
                                                    }

                                                    $text_color = $is_current || $is_past ? 'text-[#111]' : 'text-[#999]';
                                                 ?>
                                                <span class="<?php echo htmlentities((string) $text_color); ?>"><?php echo htmlentities((string) $v['name']); ?></span>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-sm md:text-xl md:mt-[2.5rem]">
                                        <?php echo isset($vo['lists']['current_progress']['content']) ? htmlentities((string) $vo['lists']['current_progress']['content']) : ''; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- 文件区域 -->
                            <div class="mt-[1.875rem]">
                                <h3 class="text-xl mb-3 font-bold md:text-[1.5625rem] md:mb-7">Technical documents</h3>
                                <div class="flex items-start gap-x-2 text-sm text-[#f01111] py-1 px-1.5 bg-[#f8fdff] mb-4 md:text-xl md:py-3.5 md:items-center md:px-3">
                                    <img src="/static/home/<USER>/icons/tishi.png" class="w-[1rem] mt-0.5 md:mt-0" alt="">
                                    Note: All materials are valid for 7 days from the date of upload.
                                </div>
                                <!-- 文件上传 -->
                                <div class="flex gap-x-2 flex-col mb-2.5 file-upload-container md:mb-5">
                                    <form id="upload-form" enctype="multipart/form-data">
                                        <input type="hidden" name="order_id" value="<?php echo htmlentities((string) $vo['lists']['order']['id']); ?>" />

                                        <div class="flex flex-col mb-2.5 md:flex-row md:gap-x-5">
                                            <div class="mb-2.5 md:mb-0">
                                                <input type="file" class="hidden file-input" id="upload-file-{{uniqueID}}"
                                                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.zip,.rar" multiple>
                                                <label for="upload-file-{{uniqueID}}"
                                                    class="btn-primary border rounded-md border-[#155797] px-5 py-2 block text-center text-base text-[#155797] cursor-pointer md:px-10 md:rounded-xl">Upload files</label>
                                            </div>
                                            <div class="flex items-start gap-x-2 text-[#999] text-sm md:items-center md:text-xl">
                                                <img src="/static/home/<USER>/icons/tishi1.png" alt="" class="w-[.875rem] mt-0.5 md:mt-0">
                                                <span>
                                                    Please upload the DEL Selection Feedback file
                                                </span>
                                            </div>
                                        </div>
                                        <!-- 显示上传的文件名称和提交，取消按钮 -->
                                        <div class="file-upload-actions mb-2.5" style="display: none;">
                                            <div class="file-name-display text-sm mb-2 flex flex-col gap-2.5 text-[#999]">
                                                <p></p>
                                            </div>
                                            <div class="action-buttons text-sm">
                                                <button type="button" class="submit-btn bg-[#155797] cursor-pointer">submit</button>
                                                <button type="button" class="cancel-btn bg-[#f08411] cursor-pointer">cancel</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="flex flex-col gap-y-5">
                                    <!--上传资料-->
                                    <?php if(!(empty($vo['lists']['user_files']) || (($vo['lists']['user_files'] instanceof \think\Collection || $vo['lists']['user_files'] instanceof \think\Paginator ) && $vo['lists']['user_files']->isEmpty()))): ?>
                                    <div class="">
                                        <div class="flex gap-x-5 items-center mb-2.5 md:text-xl md:mb-3">
                                            <h4 class="text-lg text-[#f08411]">Upload files</h4>
                                            <span class="text-[#999] text-sm md:text-xl">
                                                <?php if($vo['lists']['last_userfile_time']): ?>
                                                <?php echo date('F d, Y, h:i A', strtotime($vo['lists']['last_userfile_time'])); ?>
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                        <div class="text-sm download-list md:text-xl">
                                            <ul>
                                                <?php if(is_array($vo['lists']['user_files']) || $vo['lists']['user_files'] instanceof \think\Collection || $vo['lists']['user_files'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['lists']['user_files'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$userfile): $mod = ($i % 2 );++$i;?>
                                                <li class="bg-[#f8fdff] border border-[#dae9ff]">
                                                    <div class="flex items-center h-full gap-x-3 md:gap-x-2">
                                                        <span class="line-clamp-1"><?php echo htmlentities((string) $userfile['file_name']); ?></span>
                                                        <a href="<?php echo htmlentities((string) $userfile['file']); ?>" download="<?php echo htmlentities((string) $userfile['file_name']); ?>" class="w-[2.1875rem]">
                                                            <img src="/static/home/<USER>/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-[1.1rem]">
                                                        </a>
                                                    </div>
                                                </li>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!--保密文件-->
                                    <?php if(!(empty($vo['lists']['service_files']) || (($vo['lists']['service_files'] instanceof \think\Collection || $vo['lists']['service_files'] instanceof \think\Paginator ) && $vo['lists']['service_files']->isEmpty()))): ?>
                                    <div class="">
                                        <div class="flex gap-x-5 items-center mb-2.5 md:mb-3">
                                            <h4 class="text-lg text-[#155290] md:text-xl">Download the file</h4>
                                            <span class="text-[#999] text-sm md:text-xl">
                                                <?php if($vo['lists']['last_servicefile_time']): ?>
                                                <?php echo date('F d, Y, h:i A', strtotime($vo['lists']['last_servicefile_time'])); ?>
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                        <div class="text-sm download-list md:text-xl">
                                            <ul>
                                                <?php if(is_array($vo['lists']['service_files']) || $vo['lists']['service_files'] instanceof \think\Collection || $vo['lists']['service_files'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['lists']['service_files'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$servicefile): $mod = ($i % 2 );++$i;if($servicefile['file']): ?>
                                                <li data-id="<?php echo htmlentities((string) $servicefile['id']); ?>" data-type="1" data-main-id="<?php echo htmlentities((string) $vo['id']); ?>" class="bg-[#f8fdff] border border-[#dae9ff]">
                                                    <div class="flex items-center h-full gap-x-3 md:gap-x-2">
                                                        <span class="line-clamp-1"><?php echo htmlentities((string) $servicefile['file_name']); ?></span>
                                                        <a href="<?php echo htmlentities((string) $servicefile['file']); ?>" download="<?php echo htmlentities((string) $servicefile['file_name']); ?>" class="w-[2.1875rem] download-link">
                                                            <img src="/static/home/<USER>/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-[1.1rem]">
                                                        </a>
                                                    </div>
                                                </li>
                                                <?php else: ?>
                                                <li data-id="<?php echo htmlentities((string) $servicefile['id']); ?>" data-type="1" data-main-id="<?php echo htmlentities((string) $vo['id']); ?>" class="bg-[#f8fdff] border border-[#dae9ff]">
                                                    <div class="flex flex-col">
                                                        <a href="<?php echo htmlentities((string) $servicefile['url']); ?>" target="_blank" rel="noopener noreferrer" class="text-[#155797] underline download-link"><?php echo htmlentities((string) $servicefile['url']); ?></a>
                                                        <p>
                                                            <?php echo htmlentities((string) $servicefile['url_describe']); ?>
                                                        </p>
                                                    </div>
                                                </li>
                                                <?php endif; ?>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <!--未订购该服务-->
                        <div class="product-content-item" style="display: none;">
                            <div class="pb-5">
                                <h3 class="text-lg text-[#111111] mb-3 md:text-xl">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </h3>
                                <!-- 内容 -->
                                <div class="text-[#666] text-base mb-5">
                                    <?php echo $vo['description']; ?>
                                </div>
                                <!-- 按钮 -->
                                <a class="quote-btn flex min-w-36 justify-around items-center border border-[#f08411] gap-2 text-[#f08411] text-sm py-2.5 cursor-pointer rounded-md relative md:w-[260px] md:h-[60px] md:text-xl md:rounded-lg"
                                    href="<?php echo session('userId') ? '/quote/logged?service_id='. $vo['id'] : 'javascript:;'; ?>"
                                    onclick="return <?php echo session('userId') ? 'true' : 'showQuotePopup(\'service_id=' . $vo['id'] . '\')'; ?>">
                                    <span> Quote </span>
                                    <img src="/static/home/<USER>/icons/gouwuche-2.png" alt="" class="w-4 right-7 absolute md:w-[1.25rem]">
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </div>
            </div>
        </div>
    </section>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-[10rem]" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="/">
            Open DEL
        </a>
        <a href="/about">
            About
        </a>
        <a href="/contact">
            Contact Us
        </a>
        <a href="/privacy/terms">
            Terms of Service
        </a>
        <a href="/privacy/">
            Privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] p-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-20 md:h-20">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="" _target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl border-[#dfe7ff] border">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 w-[1.3rem] inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-14 md:h-14 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-[1.3rem]" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" onclick="window.location.href = '/quote?' + localStorage.getItem('quoteParams'); return false;" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>
<script src="/static/home/<USER>/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup(params) {
        // 存储参数到本地存储或全局变量
        localStorage.setItem('quoteParams', params);

        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>


    <script>
        // 获取所有tab和内容元素
        const tabs = document.querySelectorAll('.arrow-step');
        const contents = document.querySelectorAll('.product-content-item');

        // 为每个tab添加点击事件
        tabs.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                // 移除所有tab的active类
                tabs.forEach(t => t.classList.remove('active'));
                // 为当前点击的tab添加active类
                tab.classList.add('active');

                // 隐藏所有内容
                contents.forEach(c => c.style.display = 'none');
                // 显示对应下标的内容
                contents[index].style.display = 'block';
            });
        });

        // 默认激活第一个tab
        if (tabs.length > 0) {
            tabs[0].click();
        }

        let uploadCounter = 0;
        document.querySelectorAll('.file-upload-container').forEach(container => {
            const id = `upload-file-${++uploadCounter}`;
            const input = container.querySelector('input[type="file"]');
            const label = container.querySelector('label');

            // 绑定唯一ID
            input.id = id;
            label.htmlFor = id;

            const fileInput = container.querySelector('.file-input');
            const actionsPanel = container.querySelector('.file-upload-actions');
            const fileNameDisplay = container.querySelector('.file-name-display');

            // 文件选择事件
            fileInput.addEventListener('change', function (e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    const fileList = files.map(file =>
                        `<p>${file.name}</p>`
                    ).join('');
                    fileNameDisplay.innerHTML = fileList;
                    actionsPanel.style.display = 'block';
                }
            });

            // 取消按钮事件
            container.querySelector('.cancel-btn').addEventListener('click', () => {
                fileInput.value = '';
                fileNameDisplay.innerHTML = '';
                actionsPanel.style.display = 'none';
            });
        });

        function copyText(buttonElement) {
            // 获取最近的copy-text元素
            const strongElement = buttonElement.closest('.flex').querySelector('strong.copy-text');

            if (!strongElement) {
                console.error('找不到要复制的文本元素');
                return;
            }

            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = strongElement.textContent;
            document.body.appendChild(textarea);

            // 选中并复制文本
            textarea.select();
            document.execCommand('copy');

            // 移除临时元素
            document.body.removeChild(textarea);

            // 可选：显示复制成功的提示
            layer.msg('Copy successfully ', {
                time: 2000,
                icon: 1
            });
        }

        // 上传用户资料，给所有提交按钮添加事件监听（或使用事件委托）
        document.querySelectorAll('.submit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 关键点：通过 this.closest('form') 找到最近的父级表单
                const form = this.closest('form');
                const orderIdInput = form.querySelector('input[name="order_id"]');
                const fileInput = form.querySelector('input[type="file"]');

                const formData = new FormData(form); // 自动包含表单所有字段

                // 手动添加文件（因为 multiple 文件需要通过循环添加）
                Array.from(fileInput.files).forEach(file => {
                    formData.append('files[]', file);
                });

                // 显示加载状态
                this.disabled = true;
                this.textContent = 'uploading...';

                fetch('/upload/userfile', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        // 全部上传成功
                        layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                            location.href = data.url;
                        });
                    } else if (data.code === 2) {
                        // 部分上传成功
                        let errorMsg = data.msg + "<br>";

                        // 显示每个失败文件的错误信息
                        data.failed.forEach(file => {
                            errorMsg += `${file.name}: ${file.error}<br>`;
                        });

                        // 显示成功上传的文件数量
                        if (data.success && data.success.length > 0) {
                            errorMsg += `<br>成功上传 ${data.success.length} 个文件`;
                        }

                        layer.msg(errorMsg, {
                            icon: 2,
                            time: 5000, // 显示5秒
                            anim: 6 // 抖动效果
                        });

                        // 3秒后跳转
                        setTimeout(() => {
                            location.href = data.url;
                        }, 3000);
                    } else {
                        // 完全失败
                        layer.msg('Error: ' + data.msg, { icon: 2 });
                    }
                })
                .catch(error => {
                    layer.msg('上传出错：' + error.message, { icon: 2 });
                })
                .finally(() => {
                    this.disabled = false;
                    this.textContent = 'submit';
                });
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            // 捕获下载链接点击事件
            $('.download-link').on('click', function(e) {
                var li = $(this).closest('li');
                $.ajax({
                    url: '/download/log', // ThinkPHP路由地址
                    type: 'POST',
                    data: {
                        id: li.data('id'),
                        type: li.data('type'),
                        main_id: li.data('main-id')
                    },
                    success: function(response) {
                        // console.log('下载日志记录成功');
                    },
                    error: function(xhr, status, error) {
                        // console.error('下载日志记录失败:', error);
                    }
                });
            });
        });
    </script>
</body>

</html>