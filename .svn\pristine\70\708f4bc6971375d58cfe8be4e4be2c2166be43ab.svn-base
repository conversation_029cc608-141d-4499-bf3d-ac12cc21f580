<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<style>
    .maps .search-container{
        max-width: inherit;
    }
</style>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">留言咨询列表</a></p>

                <div class="search-container">
                    <form method="get" action="{:url('feedback')}">
                        <input type="text" name="create_time" id="create_time" value="{$params.create_time ?? ''}" placeholder="留言时间" class="search-input" />
                        <input
                            type="text"
                            name="keyword"
                            placeholder="name/phone/email/company"
                            value="{$params.keyword ?? ''}"
                            class="search-input"
                        >
                        <select name="reply_status" class="search-input">
                            <option value="" {if !isset($params.reply_status) || $params.reply_status === ''}selected{/if}>
                                回复状态（全部）
                            </option>
                            <option value="0" {if isset($params.reply_status) && $params.reply_status == 0}selected{/if}>
                                未回复
                            </option>
                            <option value="1" {if isset($params.reply_status) && $params.reply_status == 1}selected{/if}>
                                已回复
                            </option>
                        </select>
                        <button type="submit" class="search-button">搜索</button>
                    </form>
                </div>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">Name</td>
                            <td class="mid_one">Phone</td>
                            <td class="mid_one">Email</td>
                            <td class="mid_one">Company</td>
                            <td class="mid_one">Feedback message</td>
                            <td class="mid_one">IP</td>
                            <td class="mid_one">创建时间</td>
                            <td class="mid_one">回复时间</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one">{$vo.name}</td>
                            <td class="mid_one">{$vo.phone}</td>
                            <td class="mid_one">{$vo.email}</td>
                            <td class="mid_one">{$vo.company}</td>
                            <td class="mid_one">{$vo.feedback}</td>
                            <td class="mid_one">{$vo.ip}</td>
                            <td class="mid_one">{$vo.create_time}</td>
                            <td class="mid_one">{$vo.reply_time}</td>
                            <td class="mid_s">
                                {if $vo.reply_status==0}
                                <a href="{:url('reply', ['id'=>$vo['id']])}" class="basic">回复</a>
                                {/if}
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        laydate.render({
            elem: '#create_time',  // 绑定元素
            type: 'date',     // 选择日期
            format: 'yyyy-MM-dd'  // 自定义格式：年-月-日
        });
    </script>
</body>
</html>