<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="cnt-basic-list cnt-basic-layer change-files order-info" style="display: block;">
        <form action="{:url('edit_file')}" method="post" enctype="multipart/form-data" class="layer-form" autocomplete="off">
            <input type="hidden" name="id" value="{$getone.id}" />

            <div class="cnt-basic-item">
                {if $order.parent_id == 0}
                <input type="hidden" name="order_id" value="{$getone.order_id}" />
                <div class="cnt-basic-i class_con">
                    <label>选择主体：</label>
                    <span class="selecttype type {if $getone.type==0}active{/if}" data-type="0">{$order.product_name}</span>
                    {if $order.service_id}
                    <span class="selecttype type {if $getone.type==1}active{/if}" data-type="1">{$order.service_name}</span>
                    {/if}
                    <input type="hidden" name="type" value="{$getone.type}" />
                </div>
                {else }
                <div class="cnt-basic-i class_con">
                    <label>选择子订单：</label>
                    <select name="order_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="order_subs" id="vo"}
                        <option value="{$vo.id}" {if $getone.order_id==$vo.id}selected{/if}>{$vo.order_no} / {$vo.service_name}</option>
                        {/volist}
                    </select>
                    <input type="hidden" name="type" value="1" />
                </div>
                {/if}

                <div class="cnt-basic-i class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type {if $getone.url!=''}active{/if}" data-value="url">网址</span>
                    <span class="selecttype up_type {if $getone.file!=''}active{/if}" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="{$getone.url!=''?'url':'file'}" />
                </div>

                <div class="url" {if $getone.url!=''}style="display:block;{else }style="display:none;"{/if}">
                    <div class="cnt-basic-i class_con">
                        <label>网址：</label>
                        <input type="text" name="url" value="{$getone.url}" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="cnt-basic-i class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" value="{$getone.url_describe}" />
                    </div>
                </div>

                <div class="file" {if $getone.file!=''}style="display:block;{else }style="display:none;"{/if}">
                    <div class="cnt-basic-i class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="cnt-basic-i class_con">
                        <span class="input-tips">（建议PDF，不超过30M）</span>
                    </div>
                    {if condition="$getone.file"}
                    <div class="cnt-basic-i class_con">
                        <a href="{$getone.file}" target="_blank" class="url-address">{$getone.file_name}</a>
                        <input type="hidden" name="file_path" value="{$getone.file}" />
                    </div>
                    {/if}
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
            </div>
        </form>
    </div>

    {include file="common:foot_layer"}

    <script>
    $(document).ready(function() {
        // 主体选择
        $('.type').click(function() {
            $('.type').removeClass('active');
            $(this).addClass('active');
            $('input[name="type"]').val($(this).data('type'));
        });

        // 类型选择
        $('.up_type').click(function() {
            $('.up_type').removeClass('active');
            $(this).addClass('active');
            var upType = $(this).data('value');
            $('input[name="up_type"]').val(upType);

            // 显示/隐藏对应的输入区域
            if(upType === 'url') {
                $('.url').show();
                $('.file').hide();
            } else {
                $('.url').hide();
                $('.file').show();
            }
        });
    });
    </script>

</body>
</html>