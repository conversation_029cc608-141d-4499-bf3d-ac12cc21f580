<?php /*a:3:{s:62:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\order\edit.html";i:1753155536;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1752808589;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改订单</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改订单
                <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box order-info">
            <form action="<?php echo url('edit'); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="<?php echo htmlentities((string) $getone['id']); ?>" />
                <input type="hidden" name="user_id" value="<?php echo htmlentities((string) $getone['user_id']); ?>" />

                <div class="cnt-basic">
                    <div class="cnt-title">
	                    订单基本信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="email" value="<?php echo htmlentities((string) $order_info['email']); ?>" readonly class="input-readonly" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Country/Region：</label>
                                <select name="country" class="l_xiang">
                                    <option value="">
                                        Please select your country/region
                                    </option>
                                    <?php if(is_array($country) || $country instanceof \think\Collection || $country instanceof \think\Paginator): $i = 0; $__LIST__ = $country;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <option value="<?php echo htmlentities((string) $vo['en_name']); ?>" <?php if($order_info['country'] == $vo['en_name']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['en_name']); ?> <?php echo htmlentities((string) $vo['cn_name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="first_name" value="<?php echo htmlentities((string) $order_info['first_name']); ?>" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>City：</label>
                                <input type="text" name="city" value="<?php echo htmlentities((string) $order_info['city']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单金额：</label>
                                <input type="text" name="money" value="<?php echo htmlentities((string) $getone['money']); ?>" style="width: 300px;" />
                                <select name="money_unit" class="l_xiang" style="width: 72px;background: url(/static/admin/images/icon_04.png) 67px 12px no-repeat;margin-left: 4px;">
                                    <option value="USD" <?php if($getone['money_unit']=='USD'): ?>selected<?php endif; ?>>USD</option>
                                    <option value="RMB" <?php if($getone['money_unit']=='RMB'): ?>selected<?php endif; ?>>RMB</option>
                                    <option value="EUR" <?php if($getone['money_unit']=='EUR'): ?>selected<?php endif; ?>>EUR</option>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>产品：</label>
                                <select name="product_id" id="product" class="l_xiang">
                                    <option value="">请选择</option>
                                    <?php if(is_array($product) || $product instanceof \think\Collection || $product instanceof \think\Paginator): $i = 0; $__LIST__ = $product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['product_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>产品进度：</label>
                                <select name="product_progress_id" id="product_progress_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    <?php if(is_array($product_progress) || $product_progress instanceof \think\Collection || $product_progress instanceof \think\Paginator): $i = 0; $__LIST__ = $product_progress;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['product_progress_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>项目号：</label>
                                <input type="text" name="project_no" value="<?php echo htmlentities((string) $getone['project_no']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>备注：</label>
                                <textarea name="remark"><?php echo htmlentities((string) $getone['remark']); ?></textarea>
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>Title：</label>
                                <input type="text" name="title" value="<?php echo htmlentities((string) $order_info['title']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="phone" value="<?php echo htmlentities((string) $order_info['phone']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Organization：</label>
                                <input type="text" name="organization" value="<?php echo htmlentities((string) $order_info['organization']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="last_name" value="<?php echo htmlentities((string) $order_info['last_name']); ?>" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单状态：</label>
                                <select name="order_status" class="l_xiang">
                                    <option value="0" <?php if($getone['order_status']==0): ?>selected<?php endif; ?>>进行中</option>
                                    <option value="1" <?php if($getone['order_status']==1): ?>selected<?php endif; ?>>已完成</option>
                                    <option value="2" <?php if($getone['order_status']==2): ?>selected<?php endif; ?>>已取消</option>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>服务：</label>
                                <select name="service_id" id="service_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    <?php if(is_array($service) || $service instanceof \think\Collection || $service instanceof \think\Paginator): $i = 0; $__LIST__ = $service;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['service_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>服务进度：</label>
                                <select name="service_progress_id" id="service_progress_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    <?php if(is_array($service_progress) || $service_progress instanceof \think\Collection || $service_progress instanceof \think\Paginator): $i = 0; $__LIST__ = $service_progress;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['service_progress_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单编号：</label>
                                <input type="text" name="order_no" value="<?php echo htmlentities((string) $getone['order_no']); ?>" readonly class="input-readonly" />
                            </div>
                        </div>
                    </div>

                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-title">
	                    邮寄信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>物流单号：</label>
                                <input type="text" name="tracking_no" value="<?php echo htmlentities((string) $getone['tracking_no']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="express_first_name" value="<?php echo htmlentities((string) $order_info['express_first_name']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="express_email" value="<?php echo htmlentities((string) $order_info['express_email']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Shipping Address：</label>
                                <input type="text" name="express_address" value="<?php echo htmlentities((string) $order_info['express_address']); ?>" />
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>物流公司：</label>
                                <input type="text" name="tracking_company" value="<?php echo htmlentities((string) $getone['tracking_company']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="express_last_name" value="<?php echo htmlentities((string) $order_info['express_last_name']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="express_phone" value="<?php echo htmlentities((string) $order_info['express_phone']); ?>" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Postcode：</label>
                                <input type="text" name="express_postcode" value="<?php echo htmlentities((string) $order_info['express_postcode']); ?>" />
                            </div>
                        </div>
                    </div>

                    <div class="de_y">
                        <button class="de_y_l" type="submit" id="submitBtn">修改订单</button>
                        <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
                    </div>
                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-basic-l">
                        <div class="cnt-basic-f">
                            <div class="cnt-title">试用券使用记录</div>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_coupon" data-title="使用试用券" data-width="674px" data-height="300px">
                                <span class="add-layer">+</span>使用试用券
                            </button>
                        </div>
                        <div class="cnt-basic-f-table">
                            <div class="class-table-item">
                                <table class="class-table" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>订单编号</th>
                                            <th>试用券</th>
                                            <th>使用时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if(is_array($used_coupon) || $used_coupon instanceof \think\Collection || $used_coupon instanceof \think\Paginator): $i = 0; $__LIST__ = $used_coupon;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <tr data-id="<?php echo htmlentities((string) $vo['id']); ?>">
                                            <td><?php echo htmlentities((string) $vo['order_no']); ?></td>
                                            <td><?php echo htmlentities((string) $vo['name']); ?>% <?php echo htmlentities((string) $vo['description']); ?></td>
                                            <td><?php echo isset($vo['update_time']) ? htmlentities((string) $vo['update_time']) : htmlentities((string) $vo['create_time']); ?></td>
                                            <td class="mid_s">
                                                <a href="<?php echo url('cancel_coupon', ['id'=>$vo['id']]); ?>" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                            </td>
                                        </tr>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-basic-l">
                        <div class="cnt-basic-f">
                            <div class="cnt-title">主订单保密文件</div>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_orderfile" data-title="新增保密文件" data-width="674px" data-height="500px">
                                <span class="add-layer">+</span>新增保密文件
                            </button>
                        </div>
                        <div class="cnt-basic-f-table">
                            <div class="class-table-item">
                                <table class="class-table" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>产品/服务</th>
                                            <th>文件/链接</th>
                                            <th>类型</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if(is_array($order_file) || $order_file instanceof \think\Collection || $order_file instanceof \think\Paginator): $i = 0; $__LIST__ = $order_file;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <tr data-id="<?php echo htmlentities((string) $vo['id']); ?>">
                                            <td><?php echo htmlentities((string) $vo['main_name']); ?></td>
                                            <td><?php if($vo['file_name']): ?><?php echo htmlentities((string) $vo['file_name']); else: ?><?php echo htmlentities((string) $vo['url']); ?><?php endif; ?></td>
                                            <td><?php echo $vo['file_type']==0 ? "保密文件" : "用户资料"; ?></td>
                                            <td><?php echo htmlentities((string) $vo['create_time']); ?></td>
                                            <td class="mid_s">
                                                <?php if($vo['file_type']==0): ?>
                                                <!--保密文件-->
                                                <a href="<?php echo url('del_file', ['id'=>$vo['id']]); ?>" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                <div class="button edit-files basic">修改</div>
                                                <?php else: ?>
                                                <!--用户资料-->
                                                <a href="<?php echo htmlentities((string) $vo['file']); ?>" download="<?php echo htmlentities((string) $vo['file_name']); ?>" class="w-[2.1875rem]">
                                                    <img src="/static/home/<USER>/icons/xiazai1.png" alt="" title="点击下载" class="w-[0.8rem] md:w-auto">
                                                </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-basic-l">
                        <div class="cnt-basic-f">
                            <div class="cnt-title">后续服务</div>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_order_subs" data-title="新增后续服务" data-width="674px" data-height="500px" style="margin-right: -450px;">
                                <span class="add-layer">+</span>新增后续服务
                            </button>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_orderfile_subs" data-title="新增保密文件" data-width="674px" data-height="500px">
                                <span class="add-layer">+</span>新增保密文件
                            </button>
                        </div>
                        <div class="cnt-basic-f-table">
                            <div class="class-table-item">
                                <table class="class-table" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>订单编号</th>
                                            <th>服务</th>
                                            <th>服务进度</th>
                                            <th>金额</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if(is_array($order_subs) || $order_subs instanceof \think\Collection || $order_subs instanceof \think\Paginator): $i = 0; $__LIST__ = $order_subs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <tr class="toggle-row" data-target="services-<?php echo htmlentities((string) $vo['id']); ?>" data-id="<?php echo htmlentities((string) $vo['id']); ?>">
                                            <td><?php echo htmlentities((string) $vo['order_no']); ?></td>
                                            <td><?php echo htmlentities((string) $vo['service_name']); ?></td>
                                            <td><?php echo htmlentities((string) $vo['service_progress']); ?></td>
                                            <td><?php echo htmlentities((string) $vo['money']); ?></td>
                                            <td><?php echo htmlentities((string) $vo['create_time']); ?></td>
                                            <td class="mid_s">
                                                <a href="<?php echo url('del_service', ['id'=>$vo['id']]); ?>" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                <div class="button edit-orderservice basic">修改</div>
                                            </td>
                                        </tr>

                                        <?php if(!empty($vo['order_file'])): ?>
                                        <tr class="services-row">
                                            <td colspan="6">
                                                <table class="class-table services" id="services-<?php echo htmlentities((string) $vo['id']); ?>" style="width: 85%;margin: 0 auto;">
                                                    <thead>
                                                        <tr>
                                                            <th>文件/链接</th>
                                                            <th>类型</th>
                                                            <th>创建时间</th>
                                                            <th>操作</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    <?php if(is_array($vo['order_file']) || $vo['order_file'] instanceof \think\Collection || $vo['order_file'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['order_file'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                                        <tr data-id="<?php echo htmlentities((string) $v['id']); ?>">
                                                            <td><?php if($v['file_name']): ?><?php echo htmlentities((string) $v['file_name']); else: ?><?php echo htmlentities((string) $v['url']); ?><?php endif; ?></td>
                                                            <td><?php echo $v['file_type']==0 ? "保密文件" : "用户资料"; ?></td>
                                                            <td><?php echo htmlentities((string) $v['create_time']); ?></td>
                                                            <td class="mid_s">
                                                                <?php if($v['file_type']==0): ?>
                                                                <!--保密文件-->
                                                                <a href="<?php echo url('del_file', ['id'=>$v['id']]); ?>" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                                <div class="button edit-files-sub basic">修改</div>
                                                                <?php else: ?>
                                                                <!--用户资料-->
                                                                <a href="<?php echo htmlentities((string) $v['file']); ?>" download="<?php echo htmlentities((string) $v['file_name']); ?>" class="w-[2.1875rem]" title="点击下载">
                                                                    <img src="/static/home/<USER>/icons/xiazai1.png" alt="" title="点击下载" class="w-[0.8rem] md:w-auto">
                                                                </a>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="cnt-basic-list cnt-basic-layer order-info" id="layer_order_subs">
    	<form action="<?php echo url('add_service'); ?>" method="post" class="layer-form" enctype="multipart/form-data">
	    	<input type="hidden" name="parent_id" value="<?php echo htmlentities((string) $getone['id']); ?>" />
            <input type="hidden" name="info_id" value="<?php echo htmlentities((string) $getone['info_id']); ?>" />
            <input type="hidden" name="user_id" value="<?php echo htmlentities((string) $getone['user_id']); ?>" />
	        <div class="cnt-basic-item">
	        	<div class="cnt-basic-i class_con">
	                <label>选择服务：</label>
                    <select name="service_id" id="layer_service_id" class="l_xiang">
                        <option value="">请选择</option>
                        <?php if(is_array($service) || $service instanceof \think\Collection || $service instanceof \think\Paginator): $i = 0; $__LIST__ = $service;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
	            </div>

                <div class="cnt-basic-i class_con">
                    <label>服务进度：</label>
                    <select name="service_progress_id" id="layer_service_progress_id" class="l_xiang">
                        <option value="">请选择</option>
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="cnt-basic-i class_con">
                    <label>订单金额：</label>
                    <input type="text" name="money" style="width: 300px;" />
                    <select name="money_unit" class="l_xiang" style="width: 72px;background: url(/static/admin/images/icon_04.png) 67px 12px no-repeat;margin-left: 4px;">
                        <option value="USD" <?php if($getone['money_unit']=='USD'): ?>selected<?php endif; ?>>USD</option>
                        <option value="RMB" <?php if($getone['money_unit']=='RMB'): ?>selected<?php endif; ?>>RMB</option>
                        <option value="EUR" <?php if($getone['money_unit']=='EUR'): ?>selected<?php endif; ?>>EUR</option>
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="cnt-basic-i class_con">
                    <label>项目号：</label>
                    <input type="text" name="project_no" value="<?php echo htmlentities((string) $getone['project_no']); ?>" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>物流单号：</label>
                    <input type="text" name="tracking_no" value="<?php echo htmlentities((string) $getone['tracking_no']); ?>" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>物流公司：</label>
                    <input type="text" name="tracking_company" value="<?php echo htmlentities((string) $getone['tracking_company']); ?>" />
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    <div class="cnt-basic-list cnt-basic-layer change-files order-info" id="layer_orderfile">
    	<form action="<?php echo url('add_file'); ?>" method="post" class="layer-form" enctype="multipart/form-data">
	    	<input type="hidden" name="order_id" value="<?php echo htmlentities((string) $getone['id']); ?>" />
	        <div class="cnt-basic-item">
                <div class="cnt-basic-i class_con">
                    <label>选择主体：</label>
                    <span class="selecttype type active" data-type="0" data-main-id="<?php echo htmlentities((string) $getone['product_id']); ?>"><?php echo htmlentities((string) $getone['product_name']); ?></span>
                    <?php if($getone['service_name']): ?>
                    <span class="selecttype type" data-type="1" data-main-id="<?php echo htmlentities((string) $getone['service_id']); ?>"><?php echo htmlentities((string) $getone['service_name']); ?></span>
                    <?php endif; ?>
                    <input type="hidden" name="type" value="0" />
                    <input type="hidden" name="main_id" value="<?php echo htmlentities((string) $getone['product_id']); ?>" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type active" data-value="url">网址</span>
                    <span class="selecttype up_type" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="url" />
                </div>

                <div class="url" style="display:block;">
                    <div class="cnt-basic-i class_con">
                        <label>网址：</label>
                        <input type="text" name="url" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="cnt-basic-i class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" />
                    </div>
                </div>

                <div class="file" style="display:none;">
                    <div class="cnt-basic-i class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="cnt-basic-i class_con">
                        <span class="input-tips">（建议PDF，不超过30M）</span>
                    </div>
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    <div class="cnt-basic-list cnt-basic-layer change-files order-info" id="layer_orderfile_subs">
    	<form action="<?php echo url('add_file'); ?>" method="post" class="layer-form" enctype="multipart/form-data">
	        <div class="cnt-basic-item">
                <div class="cnt-basic-i class_con">
                    <label>选择子订单：</label>
                    <select name="order_service_id" class="l_xiang">
                        <option value="">请选择</option>
                        <?php if(is_array($order_subs) || $order_subs instanceof \think\Collection || $order_subs instanceof \think\Paginator): $i = 0; $__LIST__ = $order_subs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['order_no']); ?> / <?php echo htmlentities((string) $vo['service_name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
                    <input type="hidden" name="type" value="1" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type active" data-value="url">网址</span>
                    <span class="selecttype up_type" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="url" />
                </div>

                <div class="url" style="display:block;">
                    <div class="cnt-basic-i class_con">
                        <label>网址：</label>
                        <input type="text" name="url" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="cnt-basic-i class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" />
                    </div>
                </div>

                <div class="file" style="display:none;">
                    <div class="cnt-basic-i class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="cnt-basic-i class_con">
                        <span class="input-tips">（建议PDF，不超过30M）</span>
                    </div>
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    <div class="cnt-basic-list cnt-basic-layer order-info" id="layer_coupon">
    	<form action="<?php echo url('use_coupon'); ?>" method="post" class="layer-form" enctype="multipart/form-data">
	        <div class="cnt-basic-item">
	        	<div class="cnt-basic-i class_con">
	                <label>选择订单：</label>
                    <select name="order_id" class="l_xiang">
                        <option value="">请选择</option>
                        <option value="<?php echo htmlentities((string) $getone['id']); ?>"><?php echo htmlentities((string) $getone['order_no']); ?></option>
                        <?php if(is_array($order_subs) || $order_subs instanceof \think\Collection || $order_subs instanceof \think\Paginator): $i = 0; $__LIST__ = $order_subs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['order_no']); ?> / <?php echo htmlentities((string) $vo['service_name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
	            </div>

                <div class="cnt-basic-i class_con">
	                <label>选择试用券：</label>
                    <select name="id" class="l_xiang">
                        <option value="">请选择</option>
                        <?php if(is_array($user_coupon) || $user_coupon instanceof \think\Collection || $user_coupon instanceof \think\Paginator): $i = 0; $__LIST__ = $user_coupon;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['name']); ?>% <?php echo htmlentities((string) $vo['description']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
	            </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">使用</button>
                </div>
	        </div>
        </form>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
<script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
<script src="/static/ckeditor5/ckeditor5.js"></script>

<script>
    var customEditor = new CKEditorManager();
    customEditor.initAll('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const $form = $('#formId');
    const $submitBtn = $('#submitBtn');
    // 初始绑定提交事件
    if ($form.length) {
        $form.on('submit', handleSubmit);
    }
    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 确保所有编辑器内容已同步到 textarea
        customEditor.editors.forEach((editor, index) => {
            editor.updateSourceElement();
        });

        // 解绑提交事件（避免重复提交）
        $form.off('submit', handleSubmit);
        // 禁用提交按钮（防止重复点击）
		const originalBtnText = $submitBtn.text();
        $submitBtn.prop('disabled', true).text('Submitting...');
        try {
            const formData = new FormData($form[0]);
            const response = await fetch($form.attr('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });
            const data = await response.json();
            console.log(data)
            if (data.code === 1) {
                // 提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                // 提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            $submitBtn.prop('disabled', false).text(originalBtnText);
            $form.on('submit', handleSubmit);
        }
    }
</script>

<script>
    //弹窗
    $(document).on('click', '.layer-trigger', function() {
        const $btn = $(this);
        const contentId = $btn.data('layer');
        const title = $btn.data('title');
        const width = $btn.data('width');
        const height = $btn.data('height');

        layer.open({
            type: 1,
            title: [title, 'font-size:18px; background-color: #0f2950; color: #fff;'],
            closeBtn: 1,
            shadeClose: true,
            area: [width, height],
            content: $("#" + contentId),
            resize: false,
            move: false
        });
    });

    //弹窗"提交"和"取消"事件
    // 表单初始化函数
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            location.reload();

                            // 优先使用保存的索引
                            // if(window.layerIndex !== undefined) {
                            //     layer.close(window.layerIndex);
                            // }
                            // // 其次尝试获取当前索引
                            // else if(layer.getFrameIndex && layer.getFrameIndex(window.name)) {
                            //     layer.close(layer.getFrameIndex(window.name));
                            // }
                            // // 最后全部关闭
                            // else {
                            //     layer.closeAll();
                            // }

                            // // 如果需要刷新
                            // if(response.reload) {
                            //     location.reload();
                            // }
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    layer.close(window.layerIndex);
                } else {
                    layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    $(document).ready(function() {
        // 遍历每一个多选框容器
        $(".multi-select-container").each(function() {
            const $container = $(this);
            const $select = $container.find(".user-multi-select");
            const $selectAllBtn = $container.find(".select-all-btn");
            const $deselectAllBtn = $container.find(".deselect-all-btn");
            const $countDisplay = $container.find(".count");

            // 初始化计数
            updateSelectedCount();

            // 全选
            $selectAllBtn.on("click", function() {
                $select.find("option").prop("selected", true);
                updateSelectedCount();
            });

            // 取消全选
            $deselectAllBtn.on("click", function() {
                $select.find("option").prop("selected", false);
                updateSelectedCount();
            });

            // 点击选项（无需按Ctrl）
            $select.on("mousedown", "option", function(e) {
                e.preventDefault();
                $(this).prop("selected", !$(this).prop("selected"));
                $select.trigger("change"); // 触发change事件更新计数
            });

            // 选择变化时更新计数
            $select.on("change", updateSelectedCount);

            // 更新已选项数量
            function updateSelectedCount() {
                const selectedCount = $select.find("option:selected").length;
                $countDisplay.text(selectedCount);
            }
        });
    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>

    <script>
        //修改后续服务弹窗
        $(".edit-orderservice").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改后续服务", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_service?id="+pid,
                shadeClose: true,
                area: ['674px', '500px'],
            });
        });

        //修改主订单保密文件
        $(".edit-files").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改保密文件", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_file?id="+pid,
                shadeClose: true,
                area: ['674px', '500px'],
            });
        });

        $(".edit-files-sub").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改保密文件", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_file?id="+pid,
                shadeClose: true,
                area: ['674px', '500px'],
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.toggle-row').click(function(e) {
                if($(e.target).closest('.mid_s').length) return;

                const targetId = $(this).data('target');
                $('#' + targetId).toggle();
                $('#' + targetId).parents(".services-row").toggle();
            });
        });
    </script>

    <script>
    $(document).ready(function() {
        // 主体选择
        $('.type').click(function() {
            $('.type').removeClass('active');
            $(this).addClass('active');
            $('input[name="type"]').val($(this).data('type'));
            $('input[name="main_id"]').val($(this).data('main-id'));
        });

        // 类型选择
        $('.up_type').click(function() {
            var this_parent = $(this).parent(".cnt-basic-i");
            this_parent.find('.up_type').removeClass('active');
            $(this).addClass('active');
            var upType = $(this).data('value');
            this_parent.find('input[name="up_type"]').val(upType);

            // 显示/隐藏对应的输入区域
            var this_parent_parent = this_parent.parent(".cnt-basic-item");
            if(upType === 'url') {
                this_parent_parent.find('.url').show();
                this_parent_parent.find('.file').hide();
            } else {
                this_parent_parent.find('.url').hide();
                this_parent_parent.find('.file').show();
            }
        });
    });
    </script>

    <script>
        // 监听 product 变化
        $('#product').on('change', function() {
            var productId = $(this).val();
            if (!productId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: "/admin/Order/getServices",
                type: 'GET',
                data: {product_id: productId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        //更新服务选项列表
                        var $serviceSelect = $('#service_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" selected>请选择</option>');
                        $.each(response.data.service, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');

                        //更新产品进度列表
                        var $serviceSelect = $('#product_progress_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" selected>请选择</option>');
                        $.each(response.data.progress, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');

                        //清空服务进度列表
                        $('#service_progress_id').empty().append('<option value="" selected>请选择</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });

        //服务变化，服务进度列表变化
        $('#service_id').on('change', function() {
            var serviceId = $(this).val();
            if (!serviceId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: "/admin/Order/getServiceProgress",
                type: 'GET',
                data: {service_id: serviceId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        //更新服务进度列表
                        var $serviceSelect = $('#service_progress_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" selected>请选择</option>');
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });

        //添加后续服务，服务-进度联动
        $('#layer_service_id').on('change', function() {
            var serviceId = $(this).val();
            if (!serviceId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: "/admin/Order/getServiceProgress",
                type: 'GET',
                data: {service_id: serviceId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        //更新服务进度列表
                        var $serviceSelect = $('#layer_service_progress_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" selected>请选择</option>');
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>