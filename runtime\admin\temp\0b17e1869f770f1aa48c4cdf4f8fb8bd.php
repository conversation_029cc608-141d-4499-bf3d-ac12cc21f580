<?php /*a:3:{s:66:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\order\add_file.html";i:1748413021;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1747812241;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1748510607;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<!-- tiny编辑器 -->
<script src="/static/admin/js/tinymce/tinymce.min.js"></script>
<script>
    // 通用配置
    const defaultTinyMCEConfig = {
        language: 'zh_CN',
        license_key: 'gpl',
        plugins: 'autoresize code image media link lists',
        height: '250px',
        toolbar: 'code | customMediaUpload image media | undo redo | bold italic | alignleft aligncenter alignright alignjustify | fontsize | link | numlist bullist',

        file_picker_types: 'media image', // 允许选择媒体和图片文件
        images_upload_url: '/admin/tinymceImage', // 图片上传接口

        min_height: 200,      // 最小高度200px
        max_height: 600,      // 最大高度600px
        // autoresize_bottom_margin: 10, // 底部间距（可选）
        // autoresize_overflow_padding: 10, // 溢出内边距（可选）
        promotion: false,  // 禁用推广提示
        content_style: 'img {max-width: 100%; height: auto;}', // 直接内联样式

        setup: function(editor) {
            editor.ui.registry.addButton('customMediaUpload', {
                icon: 'upload',
                tooltip: '上传视频',
                onAction: function() {
                    editor.windowManager.open({
                        title: '上传视频',
                        body: {
                            type: 'panel',
                                items: [{
                                type: 'htmlpanel',
                                html: '<input type="file" id="tinymce-media-upload" accept="video/*,audio/*">'
                            }]
                        },
                        buttons: [
                            {
                                type: 'cancel',
                                name: 'cancel',
                                text: 'Cancel'
                            },
                            {
                                type: 'submit',
                                name: 'save',
                                text: 'Upload',
                                primary: true
                            }
                        ],
                        onSubmit: function(api) {
                            var fileInput = document.getElementById('tinymce-media-upload');
                            if (fileInput.files.length > 0) {
                                var formData = new FormData();
                                formData.append('file', fileInput.files[0]);

                                fetch('/admin/tinymceMedia', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    console.log(data)
                                    if (data.location) {
                                        editor.insertContent(
                                            data.location.match(/\.(mp4|webm|ogg|mov|avi)$/) ?
                                            '<video controls src="' + data.location + '"></video>' :
                                            '<audio controls src="' + data.location + '"></audio>'
                                        );
                                        api.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
    };

    // 封装初始化函数
    function initTinyMCE(selector, customConfig = {}) {
        tinymce.init({
            selector: selector,
            ...defaultTinyMCEConfig, // 通用配置
            ...customConfig, // 自定义配置
        });
    }
</script>
</head>

<style>
    .class_con span{
        color: #464df4;
        margin-left: 0;
    }

    .class_con span.selecttype {
        padding: 5px 20px;
        margin-right: 10px;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 3px;
        display: inline-block;
        color: #3a3a3a;
    }

    .class_con span.selecttype.active {
        background-color: #337ab7;
        color: white;
        border-color: #2e6da4;
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加保密文件
                <?php if($order_type == 0): ?>
                <a href="<?php echo url('files', ['id'=>$order_id, 'type'=>$order_type]); ?>" class="de_y_r" >返回</a>
                <?php else: ?>
                <a href="<?php echo url('files', ['id'=>$order_service_id, 'type'=>$order_type]); ?>" class="de_y_r" >返回</a>
                <?php endif; ?>
            </p>
        </div>

        <div class="clas_box">
            <form action="<?php echo url('add_file'); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="order_id" value="<?php echo htmlentities((string) $order_id); ?>" />
                <input type="hidden" name="order_service_id" value="<?php echo htmlentities((string) $order_service_id); ?>" />

                <div class="class_con">
                    <label>订单编号：</label>
                    <span><?php echo htmlentities((string) $order_no); ?></span>
                </div>

                <?php if($order_type == 0): ?>
                <div class="class_con">
                    <label>选择主体：</label>
                    <span class="selecttype type active" data-type="0" data-main-id="<?php echo htmlentities((string) $product_id); ?>"><?php echo htmlentities((string) $product_name); ?></span>
                    <span class="selecttype type" data-type="1" data-main-id="<?php echo htmlentities((string) $service_id); ?>"><?php echo htmlentities((string) $service_name); ?></span>
                    <input type="hidden" name="type" value="0" />
                    <input type="hidden" name="main_id" value="<?php echo htmlentities((string) $product_id); ?>" />
                </div>
                <?php else: ?>
                <div class="class_con">
                    <label>主体：</label>
                    <span><?php echo htmlentities((string) $service_name); ?></span>
                </div>
                <input type="hidden" name="type" value="1" />
                <input type="hidden" name="main_id" value="<?php echo htmlentities((string) $service_id); ?>" />
                <?php endif; ?>

                <div class="class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type active" data-value="url">网址</span>
                    <span class="selecttype up_type" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="url" />
                </div>

                <div class="url" style="display:block;">
                    <div class="class_con">
                        <label>网址：</label>
                        <input type="text" name="url" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" />
                    </div>
                </div>

                <div class="file" style="display:none;">
                    <div class="class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="class_con layout_tip_css">
                        （建议PDF，不超过30M）
                    </div>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <?php if($order_type == 0): ?>
                    <a href="<?php echo url('files', ['id'=>$order_id, 'type'=>$order_type]); ?>" class="de_y_r" >返回</a>
                    <?php else: ?>
                    <a href="<?php echo url('files', ['id'=>$order_service_id, 'type'=>$order_type]); ?>" class="de_y_r" >返回</a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script>
    // 初始化编辑器
    initTinyMCE('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const $form = $('#formId');
    const $submitBtn = $('#submitBtn');
    // 初始绑定提交事件
    if ($form.length) {
        $form.on('submit', handleSubmit);
    }
    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 解绑提交事件（避免重复提交）
        $form.off('submit', handleSubmit);
        // 禁用提交按钮（防止重复点击）
        $submitBtn.prop('disabled', true).text('提交中...');
        try {
            // 同步 TinyMCE 内容（如果有）
            if (typeof tinymce !== 'undefined') {
                tinymce.triggerSave();
            }

            const formData = new FormData($form[0]);
            const response = await fetch($form.attr('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });
            const data = await response.json();
            if (data.code === 1) {
                // 提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                // 提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            $submitBtn.prop('disabled', false).text('确定');
            $form.on('submit', handleSubmit);
        }
    }
</script>

<script>
    //弹窗
    $(document).on('click', '.layer-trigger', function() {
        const $btn = $(this);
        const contentId = $btn.data('layer');
        const title = $btn.data('title');

        layer.open({
            type: 1,
            title: [title, 'font-size:18px; background-color: #0f2950; color: #fff;'],
            closeBtn: 1,
            shadeClose: true,
            area: ['674px', '300px'],
            content: $("#" + contentId),
            resize: false,
            move: false
        });
    });

    //弹窗"提交"和"取消"事件
    // 表单初始化函数
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            location.reload();

                            // 优先使用保存的索引
                            // if(window.layerIndex !== undefined) {
                            //     layer.close(window.layerIndex);
                            // }
                            // // 其次尝试获取当前索引
                            // else if(layer.getFrameIndex && layer.getFrameIndex(window.name)) {
                            //     layer.close(layer.getFrameIndex(window.name));
                            // }
                            // // 最后全部关闭
                            // else {
                            //     layer.closeAll();
                            // }

                            // // 如果需要刷新
                            // if(response.reload) {
                            //     location.reload();
                            // }
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    layer.close(window.layerIndex);
                } else {
                    layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    $(document).ready(function() {
        // 遍历每一个多选框容器
        $(".multi-select-container").each(function() {
            const $container = $(this);
            const $select = $container.find(".user-multi-select");
            const $selectAllBtn = $container.find(".select-all-btn");
            const $deselectAllBtn = $container.find(".deselect-all-btn");
            const $countDisplay = $container.find(".count");

            // 初始化计数
            updateSelectedCount();

            // 全选
            $selectAllBtn.on("click", function() {
                $select.find("option").prop("selected", true);
                updateSelectedCount();
            });

            // 取消全选
            $deselectAllBtn.on("click", function() {
                $select.find("option").prop("selected", false);
                updateSelectedCount();
            });

            // 点击选项（无需按Ctrl）
            $select.on("mousedown", "option", function(e) {
                e.preventDefault();
                $(this).prop("selected", !$(this).prop("selected"));
                $select.trigger("change"); // 触发change事件更新计数
            });

            // 选择变化时更新计数
            $select.on("change", updateSelectedCount);

            // 更新已选项数量
            function updateSelectedCount() {
                const selectedCount = $select.find("option:selected").length;
                $countDisplay.text(selectedCount);
            }
        });
    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>

    <script>
    $(document).ready(function() {
        // 主体选择
        $('.type').click(function() {
            $('.type').removeClass('active');
            $(this).addClass('active');
            $('input[name="type"]').val($(this).data('type'));
            $('input[name="main_id"]').val($(this).data('main-id'));
        });

        // 类型选择
        $('.up_type').click(function() {
            $('.up_type').removeClass('active');
            $(this).addClass('active');
            var upType = $(this).data('value');
            $('input[name="up_type"]').val(upType);

            // 显示/隐藏对应的输入区域
            if(upType === 'url') {
                $('.url').show();
                $('.file').hide();
            } else {
                $('.url').hide();
                $('.file').show();
            }
        });
    });
    </script>

</body>
</html>