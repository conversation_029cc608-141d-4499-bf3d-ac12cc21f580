/**
 * 通用tab切换与URL参数同步
 * @param {string} tabItemSelector tab按钮选择器
 * @param {string} tabContentSelector tab内容选择器
 * @param {string} paramName URL参数名，默认'tab'
 * @param {string} activeClass 激活tab的class，默认'active'
 */
function initTabSwitch(tabItemSelector, tabContentSelector, paramName = 'tab', activeClass = 'active') {
    // 读取URL参数
    function getQueryParam(name) {
        const url = new URL(window.location.href);
        return url.searchParams.get(name);
    }

    // 切换tab
    function activateTab(tabName) {
        document.querySelectorAll(tabItemSelector).forEach(function (el) {
            if (el.dataset.tab === tabName) {
                el.classList.add(activeClass);
            } else {
                el.classList.remove(activeClass);
            }
        });
        document.querySelectorAll(tabContentSelector).forEach(function (el) {
            if (el.dataset.tab === tabName) {
                el.style.display = '';
            } else {
                el.style.display = 'none';
            }
        });
    }

    // 绑定tab点击事件
    document.querySelectorAll(tabItemSelector).forEach(function (el) {
        el.addEventListener('click', function () {
            activateTab(el.dataset.tab);
            // 更新URL参数
            const url = new URL(window.location);
            url.searchParams.set(paramName, el.dataset.tab);
            window.history.replaceState({}, '', url);
        });
    });

    // 初始化
    const tabParam = getQueryParam(paramName);
    const defaultTab = document.querySelector(tabItemSelector)?.dataset.tab;
    if (tabParam && document.querySelector(tabItemSelector + '[data-tab=\"' + tabParam + '\"]')) {
        activateTab(tabParam);
    } else if (defaultTab) {
        activateTab(defaultTab);
    }
}