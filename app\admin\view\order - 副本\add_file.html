<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<style>
    .class_con span{
        color: #464df4;
        margin-left: 0;
    }

    .class_con span.selecttype {
        padding: 5px 20px;
        margin-right: 10px;
        cursor: pointer;
        border: 1px solid #ddd;
        border-radius: 3px;
        display: inline-block;
        color: #3a3a3a;
    }

    .class_con span.selecttype.active {
        background-color: #337ab7;
        color: white;
        border-color: #2e6da4;
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加保密文件
                {if $order_type == 0}
                <a href="{:url('files', ['id'=>$order_id, 'type'=>$order_type])}" class="de_y_r" >返回</a>
                {else /}
                <a href="{:url('files', ['id'=>$order_service_id, 'type'=>$order_type])}" class="de_y_r" >返回</a>
                {/if}
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add_file')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="order_id" value="{$order_id}" />
                <input type="hidden" name="order_service_id" value="{$order_service_id}" />

                <div class="class_con">
                    <label>订单编号：</label>
                    <span>{$order_no}</span>
                </div>

                {if $order_type == 0}
                <div class="class_con">
                    <label>选择主体：</label>
                    <span class="selecttype type active" data-type="0" data-main-id="{$product_id}">{$product_name}</span>
                    <span class="selecttype type" data-type="1" data-main-id="{$service_id}">{$service_name}</span>
                    <input type="hidden" name="type" value="0" />
                    <input type="hidden" name="main_id" value="{$product_id}" />
                </div>
                {else }
                <div class="class_con">
                    <label>主体：</label>
                    <span>{$service_name}</span>
                </div>
                <input type="hidden" name="type" value="1" />
                <input type="hidden" name="main_id" value="{$service_id}" />
                {/if}

                <div class="class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type active" data-value="url">网址</span>
                    <span class="selecttype up_type" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="url" />
                </div>

                <div class="url" style="display:block;">
                    <div class="class_con">
                        <label>网址：</label>
                        <input type="text" name="url" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" />
                    </div>
                </div>

                <div class="file" style="display:none;">
                    <div class="class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="class_con layout_tip_css">
                        （建议PDF，不超过30M）
                    </div>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    {if $order_type == 0}
                    <a href="{:url('files', ['id'=>$order_id, 'type'=>$order_type])}" class="de_y_r" >返回</a>
                    {else /}
                    <a href="{:url('files', ['id'=>$order_service_id, 'type'=>$order_type])}" class="de_y_r" >返回</a>
                    {/if}
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
    $(document).ready(function() {
        // 主体选择
        $('.type').click(function() {
            $('.type').removeClass('active');
            $(this).addClass('active');
            $('input[name="type"]').val($(this).data('type'));
            $('input[name="main_id"]').val($(this).data('main-id'));
        });

        // 类型选择
        $('.up_type').click(function() {
            $('.up_type').removeClass('active');
            $(this).addClass('active');
            var upType = $(this).data('value');
            $('input[name="up_type"]').val(upType);

            // 显示/隐藏对应的输入区域
            if(upType === 'url') {
                $('.url').show();
                $('.file').hide();
            } else {
                $('.url').hide();
                $('.file').show();
            }
        });
    });
    </script>

</body>
</html>