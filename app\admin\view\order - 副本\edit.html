<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改订单</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改订单
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box order-info">
            <form action="{:url('edit')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="cnt-basic cnt-information">
                    <div class="cnt-title">
	                    订单基本信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>用户：</label>
                                <select name="user_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="users" id="vo"}
                                        <option value="{$vo.id}" {if $getone.user_id==$vo.id}selected{/if}>{$vo.email}</option>
                                    {/volist}
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="email" value="{$getone.email}" />
                                <span class="must-input">*</span>
                            </div>
                            <div class="cnt-basic-i class_con">
                                <span class="input-tips"> 如果系统中不存在该Email账号，则自动创建用户账号，初始密码“opd123456”；如账号已存在，则自动归入该账号</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Country/Region：</label>
                                <input type="text" name="country" value="{$getone.country}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="first_name" value="{$getone.first_name}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="last_name" value="{$getone.last_name}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>产品：</label>
                                <select name="product_id" id="product" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="product" id="vo"}
                                        <option value="{$vo.id}" {if $getone.product_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>物流单号：</label>
                                <input type="text" name="tracking_no" value="{$getone.tracking_no}" />
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>Title：</label>
                                <input type="text" name="title" value="{$getone.title}" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="phone" value="{$getone.phone}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Organization：</label>
                                <input type="text" name="organization" value="{$getone.organization}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Shipping Address：</label>
                                <input type="text" name="shipping_address" value="{$getone.shipping_address}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>City：</label>
                                <input type="text" name="city" value="{$getone.city}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Postcode：</label>
                                <input type="text" name="postcode" value="{$getone.postcode}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>服务：</label>
                                <select name="service_id" id="service" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="service" id="vo"}
                                        <option value="{$vo.id}" {if $order_service.service_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单编号：</label>
                                <input type="text" name="order_no" value="{$getone.order_no}" readonly class="input-readonly" />
                            </div>
                        </div>
                    </div>

                    <div class="de_y">
                        <button class="de_y_l" type="submit" id="submitBtn">修改订单</button>
                        <a href="{:url('index')}" class="de_y_r" >返回</a>
                    </div>
                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-basic-l">
                        <div class="cnt-basic-f">
                            <div class="cnt-title">后续服务</div>
                            <button type="button" class="cnt-label add-btn" id="add_orderservice"><span class="add-layer">+</span>添加后续服务</button>
                        </div>

                        <div class="cnt-basic-f-table">
                            <div class="class-table-item">
                                <table class="class-table" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>订单编号</th>
                                            <th>服务</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {volist name="order_service_list" id="vo"}
                                        <tr data-id="{$vo.id}">
                                            <td>{$vo.order_no}</td>
                                            <td>{$vo.service_name}</td>
                                            <td>{$vo.create_time}</td>
                                            <td class="mid_s">
                                                <div class="del-orderservice delete-c">删除</div>
                                                <div class="edit-orderservice basic">修改</div>
                                            </td>
                                        </tr>
                                        {/volist}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="cnt-basic-list cnt-basic-layer order-info" id="layer_orderservice">
    	<form method="post" enctype="multipart/form-data" id="orderservice-form">
	    	<input type="hidden" name="order_id" value="{$getone.id}" />
	        <div class="cnt-basic-item">
	        	<div class="cnt-basic-i class_con">
	                <label>选择服务：</label>
                    <select name="service_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="service" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
	            </div>

                <div class="de_y">
                    <button type="button" class="cnt-label cancel" id="orderservice-close">取消</button>
                    <button type="button" class="cnt-label" id="orderservice-submit">提交</button>
                </div>
	        </div>
        </form>
    </div>

    {include file="common:foot"}

    <script>
        //添加后续服务弹窗
        $("#add_orderservice").click(function () {
            layer.open({
                type: 1,
                title: ['新增后续服务', 'font-size:18px; background-color: #0f2950; color: #fff;'],
                closeBtn: 1,
                shadeClose: true,
                area:['674px', '300px'],
                content: $("#layer_orderservice"),
                resize: false,
                move: false
            });
        });
        //提交后续服务
        $("#orderservice-submit").click(function(){
    		$.post("/admin/Order/add_service", $('#orderservice-form').serialize(), function(result){
    			if(result.code==1) {
                    layer.msg(result.msg, {
					  icon: 1,
					  time: 2000
					}, function(){
						window.location.href=result.url;
					});
    			} else {
    				layer.msg(result.msg, {icon: 2});
    			}
    		});
    	})
    	//取消后续服务
    	$("#orderservice-close").click(function(){
    		layer.close(layer.index);
    	})
        //修改后续服务弹窗
        $(".edit-orderservice").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改后续服务", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_service?id="+pid,
                shadeClose: true,
                area: ['674px', '300px'],
            });
        });
    </script>

    <script>
        // 监听用户选择变化
        $('select[name="user_id"]').change(function() {
            var userId = $(this).val();
            if (userId) {
                // 发送AJAX请求获取用户信息
                $.ajax({
                    url: "/admin/Order/getUserData",
                    type: "POST",
                    data: {user_id: userId},
                    dataType: "json",
                    success: function(response) {
                        if (response.code == 1) {
                            // 填充表单字段
                            $('input[name="email"]').val(response.data.email);
                            $('input[name="country"]').val(response.data.country);
                            $('input[name="first_name"]').val(response.data.first_name);
                            $('input[name="last_name"]').val(response.data.last_name);
                            $('input[name="title"]').val(response.data.title);
                            $('input[name="phone"]').val(response.data.phone);
                            $('input[name="organization"]').val(response.data.organization);
                        } else {
                            layer.msg(response.msg);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请重试');
                    }
                });
            } else {
                // 清空表单
                $('input[name="email"], input[name="country"], input[name="first_name"], input[name="last_name"], input[name="title"], input[name="phone"], input[name="organization"]').val('');
            }
        });

        // 监听 product 变化
        $('#product').on('change', function() {
            var productId = $(this).val();
            if (!productId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: '/getServices/' + productId,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        console.log(response)
                        var $serviceSelect = $('#service');
                        $serviceSelect.empty(); // 清空现有选项
                        // 添加默认选项
                        $serviceSelect.append('<option value="" disabled selected>请选择</option>');
                        // 动态添加 service 选项
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        // 更新 UI 颜色（如果之前有样式逻辑）
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>