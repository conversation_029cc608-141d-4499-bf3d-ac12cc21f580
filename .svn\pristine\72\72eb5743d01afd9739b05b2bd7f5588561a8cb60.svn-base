<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<style>
    .checkbox-block{
        display:inline-block;
    }
    .checkbox-block input{
        vertical-align: bottom;
        margin-right: 3px;
    }
    .checkbox-block.info {
        margin-right: 20px;
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;发送系统信息
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('send_message')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="main_id" value="{$id}" />

                <div class="class_con">
                    <label>选择发送渠道：</label>
                    <div class="checkbox-block info">
                        <input type="checkbox" value="info" name="message_type[]" />发送站内信
                    </div>
                    <div class="checkbox-block">
                        <input type="checkbox" value="email" name="message_type[]" />发送邮件
                    </div>
                </div>

                <div class="class_con">
                    <label>用户：</label>
                    <div class="multi-select-container">
                        <div class="user-select-header">
                            <button type="button" class="select-all-btn">全选</button>
                            <button type="button" class="deselect-all-btn">取消选择</button>
                            <span class="selected-count">已选择: <span class="count">0</span> 个用户</span>
                        </div>
                        <select name="user_ids[]" class="l_xiang user-multi-select" multiple="multiple" size="10">
                            {volist name="users" id="vo"}
                                <option value="{$vo.id}">{$vo.email} {$vo.phone}</option>
                            {/volist}
                        </select>
                    </div>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">发送</button>
                    <a href="{:url('template')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

</body>
</html>