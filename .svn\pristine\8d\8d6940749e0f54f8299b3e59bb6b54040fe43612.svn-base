<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改密码</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;编辑管理员
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone['id']}" />
                <div class="class_con">
                    <label>用户名：</label>
                    <input type="text" name="username" value="{$getone.username}" />
                </div>
                <div class="class_con">
                    <label>角色：</label>
                    <select name="group_id" class="l_xiang">
                        {volist name="List" id="vo"}
                            <option value="{$vo.id}" {if $getone.group_id == $vo.id}selected{/if}>{$vo.title}</option>
                        {/volist}
                    </select>
                </div>
                <div class="class_con">
                    <label>新密码：</label>
                    <input name="newpassword" type="password" placeholder="留空则不修改密码" /> <span class="input-tips">密码长度6-20位</span>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>



