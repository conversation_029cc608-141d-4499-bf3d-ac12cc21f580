{volist name="header_menus" id="menu"}
<li class="{$menu.children?'relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group md:border-0 md:h-full md:flex-nowrap':'md:h-full flex'}">
    {if $menu.type == 1}
        <!-- 静态链接 -->
        <a href="{$menu.url}" class="text-[#000] {$menu.children?'cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none':'flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff] md:border-0 md:leading-none md:flex md:items-center'}">
            {$menu.name}
        </a>
    {else}
        <!-- 动态菜单 -->
        <a href="{$menu.url|default='#'}" class="text-[#000] {$menu.children?'cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none':'flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff] md:border-0 md:leading-none md:flex md:items-center'}">
            {$menu.name}
        </a>
    {/if}
    
    {if $menu.children}
        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(__IMG__/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5 md:mr-0"></i>
        <ul class="secondary static z-50 hidden bg-[#f8fdff] w-full md:absolute md:top-24 md:left-1/2 md:-translate-x-1/2 md:bg-white md:w-[19.5rem]">
            {volist name="menu.children" id="submenu"}
            <li class="{$submenu.children?'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item':'grid grid-cols-1'} md:pr-[1.25rem] md:px-0">
                {if $submenu.type == 1}
                    <!-- 静态链接 -->
                    <a href="{$submenu.url}" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] md:h-auto">
                        {$submenu.name}
                    </a>
                {else}
                    <!-- 动态菜单 -->
                    <a href="{$submenu.url|default='#'}" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] md:h-auto">
                        {$submenu.name}
                    </a>
                {/if}
                
                {if $submenu.children}
                    <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(__IMG__/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5 md:mr-0"></i>
                    <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg">
                        {volist name="submenu.children" id="subsubmenu"}
                        <li class="border-b border-t border-[#e0eaff]">
                            {if $subsubmenu.type == 1}
                                <a href="{$subsubmenu.url}" class="flex items-center px-8 h-[3.125rem] md:h-auto md:px-8 md:py-5 line-clamp-1">
                                    {$subsubmenu.name}
                                </a>
                            {else}
                                <a href="{$subsubmenu.url|default='#'}" class="flex items-center px-8 h-[3.125rem] md:h-auto md:px-8 md:py-5 line-clamp-1">
                                    {$subsubmenu.name}
                                </a>
                            {/if}
                        </li>
                        {/volist}
                    </ul>
                {/if}
            </li>
            {/volist}
            
            <!-- 动态菜单内容 -->
            {if $menu.type == 2 && isset($menu_data_$menu['id'])}
                {volist name="menu_data_$menu['id']" id="product"}
                <li class="{$product.service?'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item':'grid grid-cols-1'} md:pr-[1.25rem] md:px-0">
                    <a href="/product/{$product.seo_url}" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] md:h-auto">
                        {$product.name}
                    </a>
                    {if $product.service}
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(__IMG__/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5 md:mr-0"></i>
                        <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg">
                            {volist name="product.service" id="service"}
                            <li class="border-b border-t border-[#e0eaff]">
                                <a href="/service/{$service.seo_url}" class="flex items-center px-8 h-[3.125rem] md:h-auto md:px-8 md:py-5 line-clamp-1">
                                    {$service.name}
                                </a>
                            </li>
                            {/volist}
                        </ul>
                    {/if}
                </li>
                {/volist}
            {/if}
            
            {if $menu.type == 3 && isset($menu_data_$menu['id'])}
                {volist name="menu_data_$menu['id']" id="resource" key="k"}
                <li class="grid grid-cols-1">
                    <a href="/resources/?tab={$k}" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                        {$resource.name}
                    </a>
                </li>
                {/volist}
            {/if}
        </ul>
    {/if}
</li>
{/volist} 