<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改角色
                <a href="{:url('roles')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit_roles')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>角色名称：</label>
                    <input type="text" name="name" value="{$getone.name}" readonly class="input-readonly" />
                </div>

                <div class="class_con">
                    <label>角色描述：</label>
                    <input type="text" name="description" value="{$getone.description}" />
                </div>

                <div class="class_con">
                    <label>后台角色：</label>
                    <label class="switch">
                        <input type="checkbox" {if $getone.is_admin==1}checked{/if} class="toggle-switch" data-target="is_admin">
                        <span class="toggle-slider"></span>
                        <input type="hidden" name="is_admin" value="{$getone.is_admin}">
                    </label>
                </div>

                <div class="class_con">
                    <label>升级金额：</label>
                    <input type="text" name="money" value="{$getone.money}" />
                </div>

                <div class="class_con">
                    <label>状态：</label>
                    <label class="switch">
                        <input type="checkbox" {if $getone.status==1}checked{/if} class="toggle-switch" data-target="status">
                        <span class="toggle-slider"></span>
                        <input type="hidden" name="status" value="{$getone.status}">
                    </label>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('roles')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>