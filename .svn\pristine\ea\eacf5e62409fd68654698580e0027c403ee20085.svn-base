<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
   <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>{$tdk.seo_title?$tdk.seo_title:$tdk.name}</title>
    <meta name="keywords" content="{$tdk.seo_keywords}" />
    <meta name="description" content="{$tdk.seo_description}" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-7 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        {$tdk.top_title}
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="flex flex-col gap-5" data-aos="fade-up">
                <div class="">
                    <h1 class="text-xl Roboto_Bold mb-2 md:text-4xl md:mb-5">
                        {$tdk.title}
                    </h1>
                    <div class="text-[#666] text-sm leading-6 mb-6
                    md:text-xl md:leading-10 md:min-h-[12.5rem] md:mb-12">
                        <div class="mb-2">
                            {$tdk.content|strip_tags}
                        </div>
                        <div>
                            <img src="{$tdk.image}" alt="" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="w-full">
        <div class="w-11/12 mx-auto py-10
        md:w-10/12 md:py-20
        " data-aos="fade-up">
            <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                {volist name="product" id="vo"}
                <div class="relative border border-[#c4d7ff] rounded-xl p-3">
                    <div class="bg-white rounded-xl border border-[#c4d7ff] w-[=28.125rem] h-[20rem] md:h-[28.875rem]">
                        <figure class="flex items-center justify-center
                        h-full
                        ">
                            <img src="{$vo.image_detail}" alt="图片名称" class="max-w-[86%] max-h-[86%] object-cover" />
                            <figcaption class="sr-only">
                                图片名称
                            </figcaption>
                        </figure>
                    </div>

                    <div class="relative p-2 text-center mt-2">
                        <div class="line-clamp-1 max-w-[70%] mx-auto Roboto_Bold text-lg
                        md:text-4xl md:my-10
                        ">
                        {$vo.name}
                        </div>

                        <button
                            class="w-8 h-8 border border-[#c4d7ff] rounded-full bg-[url('__IMG__/icons/changjiantou-zuoshang2.png')] bg-no-repeat bg-center bg-white cursor-pointer absolute right-5 top-1/2 -translate-y-1/2 bg-size-[1rem]
                            md:w-[5rem] md:h-[5rem] md:bg-size-[1.8rem]
                            ">
                        </button>
                    </div>
                    <a href="/product/{$vo.seo_url}" class="absolute w-full h-full z-[2] top-0 left-0"></a>
                </div>
                {/volist}
            </div>
        </div>
    </main>

    {include file="public:footer"}

    <!-- 图片弹窗 -->
    <div id="img-modal"
        style="display:none;position:fixed;z-index:999999;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.7);justify-content:center;align-items:center;">
        <div style="position:relative;max-width:90vw;max-height:90vh;">
            <img id="modal-img" src="" alt="放大图片"
                style="max-width:100%;max-height:80vh;display:block;margin:auto;border-radius:10px;">
            <button id="close-modal"
                style="position:absolute;top:10px;right:10px;background:#fff;border:none;border-radius:50%;width:32px;height:32px;font-size:20px;cursor:pointer;">×</button>
        </div>
    </div>

    {include file="public:foot"}

    <script>
        document.querySelectorAll('.click-open').forEach(function (btn) {
            btn.addEventListener('click', function () {
                var img = this.parentElement.querySelector('img');
                if (img) {
                    document.getElementById('modal-img').src = img.src;
                    document.getElementById('img-modal').style.display = 'flex';
                }
            });
        });
        document.getElementById('close-modal').onclick = function () {
            document.getElementById('img-modal').style.display = 'none';
        };
        // 点击遮罩层关闭
        document.getElementById('img-modal').onclick = function (e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        };
    </script>

</body>

</html>