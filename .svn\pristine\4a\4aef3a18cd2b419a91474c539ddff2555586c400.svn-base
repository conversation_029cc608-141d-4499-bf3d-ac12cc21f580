<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;
use think\Request;

class Faq extends Common
{
    public function index()
    {
        $category = Db::name('Faq_category')->field("id, name")->where("pid", 0)->select()->toArray();

        $child_category = [];
        foreach($category as $key=>$val){
            //子分类
            $child_category[] = Db::name('Faq_category')
                ->where("pid", $val['id'])
                ->select()
                ->each(function ($item) {
                    $item['faq'] = Db::name("Faq")->where("category_id", $item['id'])->select();

                    return $item;
                });
        }

        return view("", [
            "category" => $category,
            "child_category" => $child_category,
        ]);
    }

    //提交问题
    public function askQuestion(Request $request){
        $data = $request->post();

        if (empty($data['question'])) {
            $this->error('Please fill in all required fields');
        }

        $data['ip'] = $request->ip();
        $data['create_time'] = date("Y-m-d H:i:s");
        $s = Db::name("Faq_ask")->strict(false)->insertGetId($data);
        if ($s) {
            $this->success('Question sent successfully!');
        } else {
            $this->error("Failed to send question！");
        }
    }

    public function details()
    {
        $seo_url = input("url");

        $getone = Db::name("Faq")->where("seo_url", $seo_url)->find();

        if(empty($getone)) {
            return view("error/404")->code(404);
        }

        return view("", [
            "getone" => $getone,
        ]);
    }

    public function results(Request $request)
    {
        // 获取搜索关键词
        $keyword = $request->param('q', '');

        // 如果没有搜索词，显示空页面
        if (empty($keyword)) {
            return view('', [
                'keyword' => '',
                'results' => []
            ]);
        }

        $results = Db::name('Faq')
            ->where('question|answer', 'like', "%{$keyword}%")
            ->select();

        return view("", [
            'keyword' => $keyword,
            'results' => $results
        ]);
    }

}
