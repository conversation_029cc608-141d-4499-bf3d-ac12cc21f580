<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">角色管理</a></p>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_t">角色名称</td>
                            <td class="mid_one">描述</td>
                            <td class="mid_one">是否后台角色</td>
                            <td class="mid_one">状态</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_t">{$vo.name}</td>
                            <td class="mid_one">{$vo.description}</td>
                            <td class="mid_one">{$vo.is_admin==0?"否":"是"}</td>
                            <td class="mid_one">{$vo.status==0?"禁用":"正常"}</td>
                            <td class="mid_s">
                                <a href="{:url('edit_roles', ['id'=>$vo['id']])}" class="basic">修改</a>
                                <a href="{:url('roles_menus', ['id'=>$vo['id']])}" class="compile">菜单权限</a>
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>