/**
 * 用户信息卡片样式
 * 支持主评论和子评论的用户头像悬停显示
 */

/* 用户头像容器基础样式 */
.user-avatar-container {
    position: relative;
    transition: transform 0.2s ease;
}

.user-avatar-container:hover {
    transform: scale(1.05);
}

/* 用户信息卡片基础样式 */
.user-info-card {
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    min-width: 320px;
    max-width: 420px;
    width: max-content;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    white-space: normal;
    word-wrap: break-word;
}

/* Portal卡片样式 - 解决overflow:hidden问题 */
.user-card-portal {
    position: fixed !important;
    z-index: 9999 !important;
    min-width: 320px;
    max-width: 420px;
    width: max-content;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: auto !important;
    box-shadow: 0 10px 25px -5px rgba(84, 111, 138, 0.4) !important;
    cursor: default;
    white-space: normal;
    word-wrap: break-word;
}

/* 确保Portal卡片内的元素可以交互 */
.user-card-portal * {
    pointer-events: auto !important;
}

/* Portal卡片内的链接和按钮样式 */
.user-card-portal a,
.user-card-portal button {
    cursor: pointer !important;
    pointer-events: auto !important;
}

/* 卡片显示状态 */
.user-info-card[style*="block"],
.user-card-portal[style*="block"] {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Portal卡片显示状态 */
.user-card-portal {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* 子评论的卡片定位调整 */
.sub-comment-avatar .user-info-card {
    min-width: 280px;
}

/* 卡片右对齐（避免超出右边界） */
.user-info-card.card-right {
    left: auto !important;
    right: 0 !important;
}

/* 卡片上方显示（避免超出下边界） */
.user-info-card.card-top {
    top: auto !important;
    bottom: 100% !important;
    transform: translateY(10px);
}

.user-info-card.card-top[style*="block"] {
    transform: translateY(0);
}

/* 卡片内容样式优化 */
.user-info-card .bg-\[#fafbff\],
.user-card-portal .bg-\[#fafbff\] {
    background-color: #fafbff !important;
    border: 1px solid #dae9ff !important;
    box-shadow: 0 10px 25px -5px rgba(84, 111, 138, 0.3) !important;
    border-radius: 0.75rem !important;
    overflow: hidden;
    max-width: 100%;
}

/* 卡片主要内容区域 */
.user-info-card .flex.items-start,
.user-card-portal .flex.items-start {
    padding: 1rem !important;
    gap: 0.5rem !important;
}

/* 桌面端样式 */
@media (min-width: 768px) {
    .user-info-card .flex.items-start,
    .user-card-portal .flex.items-start {
        padding: 2rem !important;
        gap: 2rem !important;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-info-card,
    .user-card-portal {
        min-width: 280px !important;
        max-width: calc(100vw - 2rem) !important;
        width: auto !important;
    }

    .sub-comment-avatar .user-info-card,
    .sub-comment-avatar .user-card-portal {
        min-width: 260px !important;
    }

    /* 移动端总是显示在上方，避免被键盘遮挡 */
    .user-info-card {
        top: auto !important;
        bottom: 100% !important;
    }

    /* 移动端卡片内容调整 */
    .user-info-card .flex.items-start,
    .user-card-portal .flex.items-start {
        flex-direction: column !important;
        gap: 0.75rem !important;
        padding: 1rem !important;
    }

    .user-info-card .flex.items-end,
    .user-card-portal .flex.items-end {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.5rem !important;
    }

    .user-info-card .message-btn,
    .user-card-portal .message-btn {
        align-self: stretch;
    }

    .user-info-card .message-btn a,
    .user-card-portal .message-btn a {
        display: block;
        text-align: center;
        width: 100%;
    }
}

/* 卡片动画效果 */
@keyframes cardFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes cardFadeOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
}

.user-info-card.fade-in {
    animation: cardFadeIn 0.3s ease forwards;
}

.user-info-card.fade-out {
    animation: cardFadeOut 0.2s ease forwards;
}

/* 用户头像样式 */
.user-info-card .w-\[2\.5rem\],
.user-card-portal .w-\[2\.5rem\] {
    width: 3rem !important;
    height: 3rem !important;
    flex-shrink: 0;
}

@media (min-width: 768px) {
    .user-info-card .md\:w-\[8\.125rem\],
    .user-card-portal .md\:w-\[8\.125rem\] {
        width: 6rem !important;
        height: 6rem !important;
    }
}

/* 用户信息区域 */
.user-info-card .flex.flex-col,
.user-card-portal .flex.flex-col {
    flex: 1;
    min-width: 0;
}

/* 名称和私信按钮区域 */
.user-info-card .flex.items-end,
.user-card-portal .flex.items-end {
    gap: 0.5rem !important;
    align-items: flex-start !important;
    flex-wrap: wrap;
}

@media (min-width: 768px) {
    .user-info-card .flex.items-end,
    .user-card-portal .flex.items-end {
        gap: 1rem !important;
        align-items: flex-end !important;
        flex-wrap: nowrap;
    }
}

/* 卡片内部元素样式 */
.user-info-card .name-info a,
.user-card-portal .name-info a {
    color: #155797 !important;
    text-decoration: none;
    font-weight: bold;
    font-size: 1rem !important;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .user-info-card .name-info a,
    .user-card-portal .name-info a {
        font-size: 1.5rem !important;
    }
}

.user-info-card .name-info a:hover,
.user-card-portal .name-info a:hover {
    text-decoration: underline;
}

.user-info-card .name-info p,
.user-card-portal .name-info p {
    font-size: 0.75rem !important;
    color: #999 !important;
    margin-top: 0.25rem;
    white-space: nowrap;
}

@media (min-width: 768px) {
    .user-info-card .name-info p,
    .user-card-portal .name-info p {
        font-size: 1rem !important;
    }
}

.user-info-card .message-btn a,
.user-card-portal .message-btn a {
    background-color: #155797 !important;
    color: white !important;
    text-decoration: none;
    transition: background-color 0.2s ease;
    padding: 0.25rem 0.5rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.75rem !important;
    white-space: nowrap;
}

@media (min-width: 768px) {
    .user-info-card .message-btn a,
    .user-card-portal .message-btn a {
        padding: 0.5rem 1rem !important;
        font-size: 1rem !important;
    }
}

.user-info-card .message-btn a:hover,
.user-card-portal .message-btn a:hover {
    background-color: #0d4a7a !important;
}

/* 统计数据样式 */
.user-info-card ul,
.user-card-portal ul {
    display: flex !important;
    gap: 1rem !important;
    margin: 0.75rem 0 !important;
    padding: 0 !important;
    list-style: none !important;
    justify-content: flex-start !important;
}

@media (min-width: 768px) {
    .user-info-card ul,
    .user-card-portal ul {
        gap: 2rem !important;
        margin: 1.25rem 0 !important;
    }
}

.user-info-card ul li,
.user-card-portal ul li {
    text-align: center;
    flex-shrink: 0;
}

.user-info-card ul li span,
.user-card-portal ul li span {
    display: block;
    font-weight: bold;
    font-size: 1rem !important;
    color: #333 !important;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .user-info-card ul li span,
    .user-card-portal ul li span {
        font-size: 1.25rem !important;
    }
}

.user-info-card ul li p,
.user-card-portal ul li p {
    color: #155797 !important;
    font-size: 0.75rem !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0 !important;
}

@media (min-width: 768px) {
    .user-info-card ul li p,
    .user-card-portal ul li p {
        font-size: 1rem !important;
    }
}

/* 徽章和关于标签页样式 */
.user-info-card .badge-about,
.user-card-portal .badge-about {
    margin-top: 0.5rem;
    border-top: 1px solid #dae9ff;
}

/* 标签页按钮容器 */
.user-info-card .badge-about .flex.items-center,
.user-card-portal .badge-about .flex.items-center {
    padding: 0 !important;
    border-bottom: 1px solid #dae9ff !important;
}

.user-info-card .badge-about-btn,
.user-card-portal .badge-about-btn {
    padding: 0.5rem 1rem !important;
    cursor: pointer;
    color: #666 !important;
    transition: all 0.2s ease;
    /* font-size: 0.875rem !important; */
    background: none;
    border: none;
    outline: none;
}

@media (min-width: 768px) {
    .user-info-card .badge-about-btn,
    .user-card-portal .badge-about-btn {
        padding: 0.75rem 1.5rem !important;
        font-size: 1rem !important;
    }
}

.user-info-card .badge-about-btn:hover,
.user-card-portal .badge-about-btn:hover {
    color: #155797 !important;
}

.user-info-card .badge-about-btn.active,
.user-card-portal .badge-about-btn.active {
    color: #155797 !important;
    border-bottom-color: #155797 !important;
}

/* 标签页内容区域 */
.user-info-card .tab-content,
.user-card-portal .tab-content {
    max-height: 120px !important;
    overflow-y: auto;
    padding: 0.75rem !important;
}

@media (min-width: 768px) {
    .user-info-card .tab-content,
    .user-card-portal .tab-content {
        max-height: 150px !important;
        padding: 1rem !important;
    }
}

/* 标签页内容项 */
.user-info-card .tab-content-item,
.user-card-portal .tab-content-item {
    display: block;
}

.user-info-card .tab-content-item[style*="none"],
.user-card-portal .tab-content-item[style*="none"] {
    display: none !important;
}

/* 徽章项目样式 */
.user-info-card .badge-item,
.user-card-portal .badge-item {
    margin-bottom: 0.5rem;
}

.user-info-card .tab-content-item-no-badge,
.user-card-portal .tab-content-item-no-badge {
    text-align: center;
    color: #999;
    font-style: italic;
}

/* 关于信息样式 */
.user-info-card .about-item,
.user-card-portal .about-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.user-info-card .about-item:last-child,
.user-card-portal .about-item:last-child {
    border-bottom: none;
}

.user-info-card .about-item-left,
.user-card-portal .about-item-left {
    color: #666;
    font-size: 0.875rem;
}

.user-info-card .about-item-right,
.user-card-portal .about-item-right {
    color: #333;
    font-size: 0.875rem;
    font-weight: 500;
}

@media (min-width: 768px) {
    .user-info-card .about-item-left,
    .user-card-portal .about-item-left,
    .user-info-card .about-item-right,
    .user-card-portal .about-item-right {
        font-size: 1rem;
    }
}

/* 确保卡片在所有情况下都能正确显示 */
.user-info-card *,
.user-card-portal * {
    box-sizing: border-box;
}

/* 防止卡片内容溢出 */
.user-info-card,
.user-card-portal {
    overflow: visible;
}

.user-info-card > div,
.user-card-portal > div {
    overflow: hidden;
}

/* 文本处理 */
.user-info-card,
.user-card-portal {
    word-break: break-word;
    hyphens: auto;
}

/* 确保图片正确显示 */
.user-info-card img,
.user-card-portal img {
    max-width: 100%;
    height: auto;
    object-fit: cover;
}

/* 修复可能的布局问题 */
.user-info-card .flex,
.user-card-portal .flex {
    display: flex !important;
}

.user-info-card .items-start,
.user-card-portal .items-start {
    align-items: flex-start !important;
}

.user-info-card .items-center,
.user-card-portal .items-center {
    align-items: center !important;
}

.user-info-card .flex-col,
.user-card-portal .flex-col {
    flex-direction: column !important;
}

.user-info-card .flex-1,
.user-card-portal .flex-1 {
    flex: 1 1 0% !important;
}

.user-info-card .flex-shrink-0,
.user-card-portal .flex-shrink-0 {
    flex-shrink: 0 !important;
}

.user-info-card .tab-content {
    max-height: 200px;
    overflow-y: auto;
}

/* 滚动条样式 */
.user-info-card .tab-content::-webkit-scrollbar {
    width: 4px;
}

.user-info-card .tab-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.user-info-card .tab-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.user-info-card .tab-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
