<?php /*a:5:{s:61:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\user\order.html";i:1753155345;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1753070097;s:60:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\user\left.html";i:1753070097;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1754373185;s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1753068496;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Order - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(/static/home/<USER>/bg.jpg)]">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(/static/home/<USER>/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(/static/home/<USER>/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0 relative" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full md:relative md:z-0">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            <?php echo htmlentities((string) $v['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 bg-size-[1rem] group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>

                    <?php if($basic['forum_status']==1): ?>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8 md:relative md:z-20">
                <div class="md:flex md:items-center">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-size-[1.3rem] cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="/search" method="get"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[18.75rem] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                name="q"
                                value="<?php echo isset($keyword) ? htmlentities((string) $keyword) : ''; ?>"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-size-[1.3rem] z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-[1.2rem]">
                        </div>
                        <?php if($user_message_count>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) $user_message_count); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                                md:min-w-[25rem] md:-left-[100%]" id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-tl-xl rounded-tr-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[System]</strong><span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; if(count($user_icommunity)>0 && $basic['letter_status']==1): ?>
                        <div class="bg-white shadow-2xl rounded-bl-xl rounded-br-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">iCommunity</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_icommunity) || $user_icommunity instanceof \think\Collection || $user_icommunity instanceof \think\Paginator): $i = 0; $__LIST__ = $user_icommunity;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/private-message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[<?php echo htmlentities((string) $vo['first_name']); ?> <?php echo htmlentities((string) $vo['last_name']); ?>]</strong>
                                            <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/private-message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[0.8125rem] bg-[0.75rem_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-size-[1.1rem] md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[1.3rem] cursor-pointer"
                        id="menu_btn"></button>
                </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                <div class="flex flex-col gap-5 mb-5 md:w-1/3">
    <div class="border border-[var(--border-color)] bg-white rounded-lg shadow-2xs flex items-center flex-col py-6 md:py-[2.75rem] md:rounded-2xl">
        <div class="w-[4.375rem] h-[4.375rem] rounded-full mb-4
        md:w-[8.75rem] md:h-[8.75rem] md:mb-[2.5rem]
        ">
            <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="user" class="w-full h-full rounded-full object-cover" />
        </div>
        <!-- 徽章/经验条 -->
        <div class="badge-wrapper mb-4">
            <!-- 经验条 -->
            <div class="wrapper-experience h-[.3125rem] rounded-full bg-[#dae9ff] relative mb-3 md:h-[.4375rem]">
                <div class="experience-bar absolute left-0 top-0 h-full rounded-full bg-[#f08411]" style="width: <?php echo $user['role_id']<=4 ? '0%'  :  ($user['role_id'] == 5 ? '50%' : '100%'); ?>;">
                </div>
            </div>
            <!-- 徽章 -->
            <div class="badge-container bg-[#fafbff] rounded-full px-2 py-1 border border-[#dae9ff] flex items-center gap-x-2.5 md:px-3 md:py-1.5">
                <?php if($user['role_id']==5): ?>
                <img src="/static/home/<USER>/icons/badge_1.png" class="w-[1.25rem] md:w-[2rem]" alt="02" />
                <?php elseif($user['role_id']==6): ?>
                <img src="/static/home/<USER>/icons/badge_3.png" class="w-[1.25rem] md:w-[2rem]" alt="03" />
                <?php else: ?>
                <img src="/static/home/<USER>/icons/badge_2.png" class="w-[1.25rem] md:w-[2rem]" alt="01" />
                <?php endif; ?>
                <span class="text-[#155797] text-sm md:text-lg">
                    <?php echo htmlentities((string) $user['role_name']); ?>
                </span>
            </div>
        </div>
        <div class="Roboto_Bold text-xl mb-6 md:text-[1.875rem]">
            <?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?>
        </div>
        <div class="grid grid-cols-2 gap-5 text-center">
            <?php if($basic['coupon_status']==1): ?>
            <div class="border-r border-[#dfe7ff] relative">
                <span class="Roboto_Bold text-2xl md:text-4xl">
                    <?php echo htmlentities((string) $coupon_count); ?>
                </span>
                <p class="text-sm text-[#999] md:text-xl md:mt-2.5">
                    Voucher
                </p>
                <a href="/user/coupon" class="absolute top-0 left-0 w-full h-full"></a>
            </div>
            <?php endif; ?>

            <div class="relative">
                <span class="Roboto_Bold text-2xl md:text-4xl">
                    <?php echo isset($user['points']) ? htmlentities((string) $user['points']) : 0; ?>
                </span>
                <p class="text-sm text-[#999] md:text-xl md:mt-2.5">
                    HitGen Points
                </p>
                <a href="/user/points" class="absolute top-0 left-0 w-full h-full"></a>
            </div>
        </div>
    </div>
    <div
        class="w-full overflow-x-auto mb-3 bg-white
    md:border md:border-[var(--border-color)] md:rounded-2xl md:p-3.5 md:overflow-hidden md:m-0 md:px-[1.25rem]">
        <div class="flex flex-nowrap min-w-max gap-2 text-abse
        md:flex-col md:text-[1.5625rem] md:min-w-auto navigation-bar
        ">
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='index'): ?>text-[#f08411]<?php endif; ?>
                md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]
            ">
                <a href="/user">
                    <span class="line-clamp-1">User Profile Management</span>
                </a>
            </div>
            <div
                class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='order'): ?>text-[#f08411]<?php endif; ?>
            md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]">
                <a href="/user/order">
                    <span class="line-clamp-1">Order Management</span>
                </a>
            </div>
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='message'): ?>text-[#f08411]<?php endif; ?>
            md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]">
                <a href="/user/message">
                    <span>Tooltip</span>

                    <?php if(count($user_message)>0): ?>
                    <div class="absolute top-0 -right-[.525rem] text-white text-center
                    md:top-1/2 md:-translate-y-1/2 md:right-[4.375rem]
                    ">
                        <div class="min-w-[1rem] min-h-[1rem] text-[.75rem] bg-[#ff0000] rounded-full px-1 md:px-2
                            md:min-w-[1.875rem] md:min-h-[1.875rem] md:text-xl">
                            <span><?php echo htmlentities((string) count($user_message)); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </a>
            </div>

            <?php if($basic['forum_status']==1): ?>
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='post' || request()->action()=='comment' || request()->action()=='private_message'): ?>text-[#f08411]<?php endif; ?>
            md:border-0 md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]">
                <a href="/user/post">
                    <span>iCommunity</span>

                    <?php if($private_count>0): ?>
                    <div class="absolute top-0 -right-[.525rem] text-white text-center
                    md:top-1/2 md:-translate-y-1/2 md:right-[4.375rem]
                    ">
                        <div class="min-w-[1rem] min-h-[1rem] text-[.75rem] bg-[#ff0000] rounded-full px-1 md:px-2
                            md:min-w-[1.875rem] md:min-h-[1.875rem] md:text-xl">
                            <span><?php echo htmlentities((string) $private_count); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div
                        class="grid grid-cols-2 text-sm md:text-[1.5625rem] md:px-[1.875rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link text-center">
                        <span data-tab="order-list" class="tab-item">
                            Order List
                        </span>
                        <span data-tab="purchase-history" class="tab-item">
                            Purchase History
                        </span>
                    </div>
                    <div class="user-profile">
                        <div data-tab="order-list" class="profile-item">
                            <div class="p-4 md:p-[1.875rem]">
                                <!-- 切换 -->
                                <div class="order-tab flex gap-2 text-sm border-b border-[#dae9ff] mb-3 md:text-xl md:mb-[1.5625rem]">
                                    <div data-tab="Progress" class="order-tab-item">
                                        In Progress
                                    </div>
                                    <div data-tab="Completed" class="order-tab-item">
                                        Completed
                                    </div>
                                    <div data-tab="Cancelled" class="order-tab-item">
                                        Cancelled
                                    </div>
                                </div>

                                <!-- 切换容器 -->
                                <div class="order-list">
                                    <!--进行中-->
                                    <div data-tab="Progress" class="order-list-item">
                                        <div class="order-search mb-3 md:mb-[1.875rem]">
                                            <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                                <input type="text"
                                                    class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                                    placeholder="Product Title/Item Title">
                                                <button type="submit"
                                                    class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                                    id="OrderList_btn">
                                                    <img src="/static/home/<USER>/icons/sousuo.png" class="md:w-full" alt="" />
                                                </button>
                                            </div>
                                        </div>
                                        <div class="order-container">

                                                <?php if(is_array($orders_progress) || $orders_progress instanceof \think\Collection || $orders_progress instanceof \think\Paginator): $i = 0; $__LIST__ = $orders_progress;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                                <div class="order-list-item-container">
                                                    <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                        <div class="order-list-item-header-left md:flex md:items-center">
                                                            <div class="mb-1 md:mb-0 md:mr-5">
                                                                <span><?php echo date('j F Y', strtotime($vo['create_time'])); ?> </span>
                                                            </div>
                                                            <?php if($vo['tracking_no']): ?>
                                                            <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                                                <span>Tracking number:
                                                                    <strong class="copy-text"><?php echo htmlentities((string) $vo['tracking_no']); ?></strong>
                                                                </span>
                                                                <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                                                    <img src="/static/home/<USER>/icons/fuzhi.png"
                                                                        class="object-cover w-[1.0625rem] h-[1.0625rem]"
                                                                        alt="" />
                                                                </span>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="flex gap-x-1.5 items-center md:gap-x-5">
                                                            <a href="/user/order-detail-<?php echo htmlentities((string) $vo['id']); ?>.html" class="order-list-item-header-right-btn underline text-[#155797]">
                                                                <span>Order Details </span>
                                                            </a>
                                                            <img src="/static/home/<USER>/icons/xiala-.png" alt="">
                                                        </div>
                                                    </div>
                                                    <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                        <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                            <img src="<?php echo htmlentities((string) $vo['product']['image_detail']); ?>" alt="<?php echo htmlentities((string) $vo['product']['name']); ?>" class="max-h-2/3 max-w-2/3 object-cover" />
                                                        </div>
                                                        <div class="order-right-text md:w-full">
                                                            <div class="order-right-text-item">
                                                                <div class="text-base item-left-text-name">
                                                                    <a href="/product/<?php echo htmlentities((string) $vo['product']['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $vo['product']['name']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    <?php echo htmlentities((string) $vo['product']['product_progress']); ?>
                                                                </div>
                                                            </div>

                                                            <?php if(!(empty($vo['service_current']) || (($vo['service_current'] instanceof \think\Collection || $vo['service_current'] instanceof \think\Paginator ) && $vo['service_current']->isEmpty()))): ?>
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/<?php echo htmlentities((string) $vo['service_current']['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $vo['service_current']['name']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    <?php echo htmlentities((string) $vo['service_current']['service_progress']); ?>
                                                                </div>
                                                            </div>
                                                            <?php endif; if(is_array($vo['service_son']) || $vo['service_son'] instanceof \think\Collection || $vo['service_son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service_son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$s): $mod = ($i % 2 );++$i;?>
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/<?php echo htmlentities((string) $s['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $s['name']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    <?php echo htmlentities((string) $s['service_progress']); ?>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; endif; else: echo "" ;endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                                <div class="empty-state py-10 text-center no-results"style="display: none;">No orders in progress</div>
                                        </div>
                                    </div>

                                    <!--已完成-->
                                    <div data-tab="Completed" class="order-list-item">
                                        <div class="order-search mb-3 md:mb-[1.875rem]">
                                            <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                                <input type="text"
                                                    class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                                    placeholder="Product Title/Item Title">
                                                <button type="submit"
                                                    class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                                    id="OrderList_btn">
                                                    <img src="/static/home/<USER>/icons/sousuo.png" class="md:w-full" alt="" />
                                                </button>
                                            </div>
                                        </div>
                                        <div class="order-container">
                                                <?php if(is_array($orders_completed) || $orders_completed instanceof \think\Collection || $orders_completed instanceof \think\Paginator): $i = 0; $__LIST__ = $orders_completed;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                                <div class="order-list-item-container">
                                                    <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                        <div class="order-list-item-header-left md:flex md:items-center">
                                                            <div class="mb-1 md:mb-0 md:mr-5">
                                                                <span><?php echo date('j F Y', strtotime($vo['create_time'])); ?></span>
                                                            </div>
                                                            <?php if($vo['tracking_no']): ?>
                                                            <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                                                <span>Tracking number:
                                                                    <strong class="copy-text"><?php echo htmlentities((string) $vo['tracking_no']); ?></strong>
                                                                </span>
                                                                <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                                                    <img src="/static/home/<USER>/icons/fuzhi.png"
                                                                        class="object-cover w-[1.0625rem] h-[1.0625rem]"
                                                                        alt="" />
                                                                </span>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="flex gap-x-1.5 items-center md:gap-x-5">
                                                            <a href="/user/order-detail-<?php echo htmlentities((string) $vo['id']); ?>.html" class="order-list-item-header-right-btn underline text-[#155797]">
                                                                <span>Order Details </span>
                                                            </a>
                                                            <img src="/static/home/<USER>/icons/xiala-.png" alt="">
                                                        </div>
                                                    </div>
                                                    <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                        <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                            <img src="<?php echo htmlentities((string) $vo['product']['image_detail']); ?>" alt="<?php echo htmlentities((string) $vo['product']['name']); ?>" class="max-h-2/3 max-w-2/3 object-cover" />
                                                        </div>
                                                        <div class="order-right-text md:w-full">
                                                            <div class="order-right-text-item">
                                                                <div class="text-base item-left-text-name">
                                                                    <a href="/product/<?php echo htmlentities((string) $vo['product']['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $vo['product']['name']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    <?php echo htmlentities((string) $vo['product']['product_progress']); ?>
                                                                </div>
                                                            </div>

                                                            <?php if(!(empty($vo['service_current']) || (($vo['service_current'] instanceof \think\Collection || $vo['service_current'] instanceof \think\Paginator ) && $vo['service_current']->isEmpty()))): ?>
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/<?php echo htmlentities((string) $vo['service_current']['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $vo['service_current']['name']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    <?php echo htmlentities((string) $vo['service_current']['service_progress']); ?>
                                                                </div>
                                                            </div>
                                                            <?php endif; if(is_array($vo['service_son']) || $vo['service_son'] instanceof \think\Collection || $vo['service_son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service_son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$s): $mod = ($i % 2 );++$i;?>
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/<?php echo htmlentities((string) $s['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $s['name']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    <?php echo htmlentities((string) $s['service_progress']); ?>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; endif; else: echo "" ;endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                                <div class="empty-state py-10 text-center no-results"style="display: none;">No orders in progress</div>
                                        </div>
                                    </div>

                                    <!--已取消-->
                                    <div data-tab="Cancelled" class="order-list-item">
                                        <div class="order-search mb-3 md:mb-[1.875rem]">
                                            <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                                <input type="text"
                                                    class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                                    placeholder="Product Title/Item Title">
                                                <button type="submit"
                                                    class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                                    id="OrderList_btn">
                                                    <img src="/static/home/<USER>/icons/sousuo.png" class="md:w-full" alt="" />
                                                </button>
                                            </div>
                                        </div>
                                        <div class="order-container">
                                                <?php if(is_array($orders_cancelled) || $orders_cancelled instanceof \think\Collection || $orders_cancelled instanceof \think\Paginator): $i = 0; $__LIST__ = $orders_cancelled;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                                <div class="order-list-item-container">
                                                    <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                        <div class="order-list-item-header-left md:flex md:items-center">
                                                            <div>
                                                                <span><?php echo date('j F Y', strtotime($vo['create_time'])); ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                        <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                            <img src="<?php echo htmlentities((string) $vo['product']['image_detail']); ?>" alt="<?php echo htmlentities((string) $vo['product']['name']); ?>" class="max-h-2/3 max-w-2/3 object-cover" />
                                                        </div>
                                                        <div class="order-right-text md:w-full">
                                                            <div class="order-right-text-item">
                                                                <div class="text-base item-left-text-name">
                                                                    <a href="/product/<?php echo htmlentities((string) $vo['product']['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $vo['product']['name']); ?>
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    Cancelled
                                                                </div>
                                                            </div>

                                                            <?php if(!(empty($vo['service_current']) || (($vo['service_current'] instanceof \think\Collection || $vo['service_current'] instanceof \think\Paginator ) && $vo['service_current']->isEmpty()))): ?>
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/<?php echo htmlentities((string) $vo['service_current']['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $vo['service_current']['service_progress']); ?>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            <?php endif; if(is_array($vo['service_son']) || $vo['service_son'] instanceof \think\Collection || $vo['service_son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service_son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$s): $mod = ($i % 2 );++$i;?>
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/<?php echo htmlentities((string) $s['seo_url']); ?>">
                                                                        <?php echo htmlentities((string) $s['name']); ?>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; endif; else: echo "" ;endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                                <div class="empty-state py-10 text-center no-results"style="display: none;">No orders in progress</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--购买历史-->
                        <div data-tab="purchase-history" class="profile-item">
                            <div class="p-4 md:p-[1.875rem]">
                                <div class="order-search mb-3 md:mb-[1.875rem]">
                                    <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                        <input type="text"
                                            class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                            placeholder="Product Title/Item Title">
                                        <button type="submit"
                                            class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                            id="PurchaseHistory_btn">
                                            <img src="/static/home/<USER>/icons/sousuo.png" class="md:w-full" alt="" />
                                        </button>
                                    </div>
                                </div>
                                <div class="order-container">
                                    <?php if(is_array($orders_completed) || $orders_completed instanceof \think\Collection || $orders_completed instanceof \think\Paginator): $i = 0; $__LIST__ = $orders_completed;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <div class="order-list-item-container">
                                            <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                <div class="order-list-item-header-left md:flex md:items-center">
                                                    <div class="mb-1 md:mb-0 md:mr-5">
                                                        <span><?php echo date('j F Y', strtotime($vo['create_time'])); ?></span>
                                                    </div>
                                                    <?php if($vo['tracking_no']): ?>
                                                    <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                                        <span>Tracking number:
                                                            <strong class="copy-text"><?php echo htmlentities((string) $vo['tracking_no']); ?></strong>
                                                        </span>
                                                        <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                                            <img src="/static/home/<USER>/icons/fuzhi.png" class="object-cover w-[1.0625rem] h-[1.0625rem]" alt="" />
                                                        </span>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex gap-x-1.5 items-center md:gap-x-5">
                                                    <a href="/user/order-detail-<?php echo htmlentities((string) $vo['id']); ?>.html"
                                                        class="order-list-item-header-right-btn underline text-[#155797]">
                                                        <span>Order Details </span>
                                                    </a>
                                                    <img src="/static/home/<USER>/icons/xiala-.png" alt="">
                                                </div>
                                            </div>
                                            <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                    <img src="<?php echo htmlentities((string) $vo['product']['image_detail']); ?>" alt="<?php echo htmlentities((string) $vo['product']['name']); ?>" class="max-h-2/3 max-w-2/3 object-cover" />
                                                </div>
                                                <div class="order-right-text md:w-full">
                                                    <div class="order-right-text-item">
                                                        <div class="text-base item-left-text-name">
                                                            <a href="/product/<?php echo htmlentities((string) $vo['product']['seo_url']); ?>">
                                                                <?php echo htmlentities((string) $vo['product']['name']); ?>
                                                            </a>
                                                        </div>
                                                        <div class="text-[#999999] item-status">
                                                            <?php echo htmlentities((string) $vo['product']['product_progress']); ?>
                                                        </div>
                                                    </div>
                                                    <?php if(!(empty($service_current) || (($service_current instanceof \think\Collection || $service_current instanceof \think\Paginator ) && $service_current->isEmpty()))): ?>
                                                    <div class="order-right-text-item">
                                                        <div class="text-[#155797] text-sm item-left-text-name">
                                                            <a href="/service/<?php echo htmlentities((string) $vo['service_current']['seo_url']); ?>">
                                                                <?php echo htmlentities((string) $vo['service_current']['name']); ?>
                                                            </a>
                                                        </div>
                                                        <div class="text-[#999999] item-status">
                                                            <?php echo htmlentities((string) $vo['service_current']['service_progress']); ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; if(is_array($vo['service_son']) || $vo['service_son'] instanceof \think\Collection || $vo['service_son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service_son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$s): $mod = ($i % 2 );++$i;?>
                                                    <div class="order-right-text-item">
                                                        <div class="text-[#155797] text-sm item-left-text-name">
                                                            <a href="/service/<?php echo htmlentities((string) $s['seo_url']); ?>">
                                                                <?php echo htmlentities((string) $s['name']); ?>
                                                            </a>
                                                        </div>
                                                        <div class="text-[#999999] item-status">
                                                            <?php echo htmlentities((string) $s['service_progress']); ?>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                    <div class="empty-state py-10 text-center no-results" style="display: none;">No orders in progress</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-[10rem]" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="/">
            Open DEL
        </a>
        <a href="/about">
            About
        </a>
        <a href="/contact">
            Contact Us
        </a>
        <a href="/privacy/terms">
            Terms of Service
        </a>
        <a href="/privacy/">
            Privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] p-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-20 md:h-20">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="http://drt.zoosnet.net/LR/Chatpre.aspx?id=DRT22534948&lng=en" target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl border-[#dfe7ff] border">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 w-[1.3rem] inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-14 md:h-14 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-[1.3rem]" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" onclick="window.location.href = '/quote?' + localStorage.getItem('quoteParams'); return false;" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>
<script src="/static/home/<USER>/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup(params) {
        // 存储参数到本地存储或全局变量
        localStorage.setItem('quoteParams', params);

        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>

    <script src="/static/home/<USER>/TabSwitch.js"></script>
    <script src="/static/home/<USER>/filterOrders.js"></script>
    <script>

        function copyText(buttonElement) {
            // 获取最近的copy-text元素
            const strongElement = buttonElement.closest('.flex').querySelector('strong.copy-text');

            if (!strongElement) {
                console.error('找不到要复制的文本元素');
                return;
            }

            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = strongElement.textContent;
            document.body.appendChild(textarea);

            // 选中并复制文本
            textarea.select();
            document.execCommand('copy');

            // 移除临时元素
            document.body.removeChild(textarea);

            // 可选：显示复制成功的提示
            layer.msg('Copy successfully ', {
                time: 2000,
                icon: 1
            });
        }

        initTabSwitch('.tab-item', '.profile-item');
        initTabSwitch('.order-tab-item', '.order-list-item');


    </script>
       <script>
     // 初始化
    document.addEventListener('DOMContentLoaded', () => {
        // 订单状态搜索：
        initOrderSearch({
            containerSelector: '.order-list-item',
            inputSelector: '.order-search-input',
            itemSelector: '.order-list-item-container',
            debounceTime: 500 // 防抖时间调整为500ms
        });
        // 历史记录搜素
        initOrderSearch({
            containerSelector: '.profile-item',
            inputSelector: '.order-search-input',
            itemSelector: '.order-list-item-container',
            debounceTime: 500 // 防抖时间调整为500ms
        });
    });
   </script>
</body>

</html>