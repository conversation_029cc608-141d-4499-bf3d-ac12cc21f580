<?php /*a:3:{s:68:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\user\roles_menus.html";i:1749521071;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1752808589;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;菜单权限
                <a href="<?php echo url('roles'); ?>" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="<?php echo url('roles_menus'); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="<?php echo htmlentities((string) $getone['id']); ?>" />

                <div class="class_con">
                    <label>角色名称：</label>
                    <input type="text" name="name" value="<?php echo htmlentities((string) $getone['name']); ?>" readonly class="input-readonly" />
                </div>

                <div class="class_con">
                    <label>菜单权限：</label>
                    <div class="manag-radio-list">
                    <?php if(is_array($menus) || $menus instanceof \think\Collection || $menus instanceof \think\Paginator): $i = 0; $__LIST__ = $menus;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <div class="manag-radio-item parent" data-menu-id="<?php echo htmlentities((string) $vo['id']); ?>">
                            <label>
                                <input type="checkbox" name="menu_ids[]" value="<?php echo htmlentities((string) $vo['id']); ?>"
                                    data-level="1" data-parent="0" class="menu-checkbox">
                                <?php echo htmlentities((string) $vo['title']); ?>
                            </label>
                        </div>
                        <?php if(is_array($vo['son']) || $vo['son'] instanceof \think\Collection || $vo['son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo2): $mod = ($i % 2 );++$i;?>
                        <div class="manag-radio-item child" data-menu-id="<?php echo htmlentities((string) $vo2['id']); ?>" data-parent="<?php echo htmlentities((string) $vo['id']); ?>">
                            <label>
                                <input type="checkbox" name="menu_ids[]" value="<?php echo htmlentities((string) $vo2['id']); ?>"
                                    data-level="2" data-parent="<?php echo htmlentities((string) $vo['id']); ?>" class="menu-checkbox">
                                <?php echo htmlentities((string) $vo2['title']); ?>
                            </label>
                        </div>
                            <?php if(is_array($vo2['son']) || $vo2['son'] instanceof \think\Collection || $vo2['son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo2['son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo3): $mod = ($i % 2 );++$i;?>
                            <div class="manag-radio-item child2" data-menu-id="<?php echo htmlentities((string) $vo3['id']); ?>" data-parent="<?php echo htmlentities((string) $vo2['id']); ?>">
                                <label>
                                    <input type="checkbox" name="menu_ids[]" value="<?php echo htmlentities((string) $vo3['id']); ?>"
                                        data-level="3" data-parent="<?php echo htmlentities((string) $vo2['id']); ?>" class="menu-checkbox">
                                    &nbsp;&nbsp;<?php echo htmlentities((string) $vo3['title']); ?>
                                </label>
                            </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                    </div>
                </div>

                <input type="hidden" name="menus" value="" />

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="<?php echo url('roles'); ?>" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
<script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
<script src="/static/ckeditor5/ckeditor5.js"></script>

<script>
    var customEditor = new CKEditorManager();
    customEditor.initAll('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const $form = $('#formId');
    const $submitBtn = $('#submitBtn');
    // 初始绑定提交事件
    if ($form.length) {
        $form.on('submit', handleSubmit);
    }
    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 确保所有编辑器内容已同步到 textarea
        customEditor.editors.forEach((editor, index) => {
            editor.updateSourceElement();
        });

        // 解绑提交事件（避免重复提交）
        $form.off('submit', handleSubmit);
        // 禁用提交按钮（防止重复点击）
		const originalBtnText = $submitBtn.text();
        $submitBtn.prop('disabled', true).text('Submitting...');
        try {
            const formData = new FormData($form[0]);
            const response = await fetch($form.attr('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });
            const data = await response.json();
            console.log(data)
            if (data.code === 1) {
                // 提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                // 提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            $submitBtn.prop('disabled', false).text(originalBtnText);
            $form.on('submit', handleSubmit);
        }
    }
</script>

<script>
    //弹窗
    $(document).on('click', '.layer-trigger', function() {
        const $btn = $(this);
        const contentId = $btn.data('layer');
        const title = $btn.data('title');
        const width = $btn.data('width');
        const height = $btn.data('height');

        layer.open({
            type: 1,
            title: [title, 'font-size:18px; background-color: #0f2950; color: #fff;'],
            closeBtn: 1,
            shadeClose: true,
            area: [width, height],
            content: $("#" + contentId),
            resize: false,
            move: false
        });
    });

    //弹窗"提交"和"取消"事件
    // 表单初始化函数
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            location.reload();

                            // 优先使用保存的索引
                            // if(window.layerIndex !== undefined) {
                            //     layer.close(window.layerIndex);
                            // }
                            // // 其次尝试获取当前索引
                            // else if(layer.getFrameIndex && layer.getFrameIndex(window.name)) {
                            //     layer.close(layer.getFrameIndex(window.name));
                            // }
                            // // 最后全部关闭
                            // else {
                            //     layer.closeAll();
                            // }

                            // // 如果需要刷新
                            // if(response.reload) {
                            //     location.reload();
                            // }
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    layer.close(window.layerIndex);
                } else {
                    layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    $(document).ready(function() {
        // 遍历每一个多选框容器
        $(".multi-select-container").each(function() {
            const $container = $(this);
            const $select = $container.find(".user-multi-select");
            const $selectAllBtn = $container.find(".select-all-btn");
            const $deselectAllBtn = $container.find(".deselect-all-btn");
            const $countDisplay = $container.find(".count");

            // 初始化计数
            updateSelectedCount();

            // 全选
            $selectAllBtn.on("click", function() {
                $select.find("option").prop("selected", true);
                updateSelectedCount();
            });

            // 取消全选
            $deselectAllBtn.on("click", function() {
                $select.find("option").prop("selected", false);
                updateSelectedCount();
            });

            // 点击选项（无需按Ctrl）
            $select.on("mousedown", "option", function(e) {
                e.preventDefault();
                $(this).prop("selected", !$(this).prop("selected"));
                $select.trigger("change"); // 触发change事件更新计数
            });

            // 选择变化时更新计数
            $select.on("change", updateSelectedCount);

            // 更新已选项数量
            function updateSelectedCount() {
                const selectedCount = $select.find("option:selected").length;
                $countDisplay.text(selectedCount);
            }
        });
    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>

    <script>
        $(document).ready(function() {
            // 存储当前角色ID和已更新的菜单状态
            let currentRoleId = $('#role_id').val();
            let roleMenus = <?php echo $role_menus; ?>;
            let updatedMenus = {...roleMenus};

            function initMenuCheckboxes() {
                // 1. 初始化所有复选框状态（原 initCheckboxStates 的逻辑）
                $('.menu-checkbox').each(function() {
                    const menuId = $(this).val();
                    const state = roleMenus[menuId] || 0; // 默认未选中

                    $(this).prop('checked', state === 1); // 1=全选
                    $(this).prop('indeterminate', state === 2); // 2=半选
                });

                // 2. 自底向上更新父级状态
                // 先更新 3 级菜单的父级（2 级菜单）
                $('.menu-checkbox[data-level="3"]').each(function() {
                    updateParentState($(this));
                });

                // 再更新 2 级菜单的父级（1 级菜单）
                $('.menu-checkbox[data-level="2"]').each(function() {
                    updateParentState($(this));
                });
            }
            initMenuCheckboxes();

            // 更新父级复选框状态
            function updateParentState($checkbox) {
                const level = parseInt($checkbox.data('level'));
                const menuId = $checkbox.val();

                if (level === 3) {
                    // 三级菜单 - 更新二级父菜单
                    const parentId = $checkbox.data('parent');
                    updateSpecificParent(parentId);
                } else if (level === 2) {
                    // 二级菜单 - 更新一级父菜单
                    const parentId = $checkbox.data('parent');
                    updateSpecificParent(parentId);

                    // 同时更新自己的子菜单
                    const childCheckboxes = $(`.menu-checkbox[data-parent="${menuId}"]`);
                    const isChecked = $checkbox.prop('checked');
                    const isIndeterminate = $checkbox.prop('indeterminate');

                    if (!isIndeterminate) {
                        childCheckboxes.prop('checked', isChecked);
                        // 记录子菜单状态变化
                        childCheckboxes.each(function() {
                            recordMenuChange($(this));
                        });
                    }
                } else if (level === 1) {
                    // 一级菜单 - 更新所有子菜单
                    const childCheckboxes = $(`.menu-checkbox[data-parent="${menuId}"]`);
                    const isChecked = $checkbox.prop('checked');

                    childCheckboxes.prop('checked', isChecked);
                    childCheckboxes.prop('indeterminate', false);

                    // 记录子菜单状态变化
                    childCheckboxes.each(function() {
                        recordMenuChange($(this));
                    });

                    // 更新二级菜单的子菜单
                    childCheckboxes.filter('[data-level="2"]').each(function() {
                        const subChildCheckboxes = $(`.menu-checkbox[data-parent="${$(this).val()}"]`);
                        subChildCheckboxes.prop('checked', isChecked);
                        // 记录子菜单状态变化
                        subChildCheckboxes.each(function() {
                            recordMenuChange($(this));
                        });
                    });
                }
            }

            // 更新特定父菜单状态
            function updateSpecificParent(parentId) {
                const $parentCheckbox = $(`.menu-checkbox[value="${parentId}"]`);
                if ($parentCheckbox.length === 0) return;

                const childCheckboxes = $(`.menu-checkbox[data-parent="${parentId}"]`);
                const checkedCount = childCheckboxes.filter(':checked').length;
                const indeterminateCount = childCheckboxes.filter(function() {
                    return $(this).prop('indeterminate');
                }).length;
                const totalCount = childCheckboxes.length;

                let newState;
                if (checkedCount === 0 && indeterminateCount === 0) {
                    $parentCheckbox.prop('checked', false);
                    $parentCheckbox.prop('indeterminate', false);
                    newState = 0;
                } else if (checkedCount === totalCount) {
                    $parentCheckbox.prop('checked', true);
                    $parentCheckbox.prop('indeterminate', false);
                    newState = 1;
                } else {
                    $parentCheckbox.prop('checked', false);
                    $parentCheckbox.prop('indeterminate', true);
                    newState = 2;
                }

                // 记录父菜单状态变化
                recordMenuChange($parentCheckbox, newState);

                // 递归更新上级菜单
                const grandParentId = $parentCheckbox.data('parent');
                if (grandParentId != 0) {
                    updateSpecificParent(grandParentId);
                }
            }

            // 记录菜单状态变化
            function recordMenuChange($checkbox, state = null) {
                const menuId = $checkbox.val();
                const currentState = state !== null ? state : ($checkbox.prop('indeterminate') ? 2 : ($checkbox.prop('checked') ? 1 : 0));

                updatedMenus[menuId] = currentState;

                $("input[name='menus']").val(JSON.stringify(updatedMenus));
            }

            // 绑定change事件
            $(document).on('change', '.menu-checkbox', function() {
                $(this).prop('indeterminate', false);
                updateParentState($(this));
                recordMenuChange($(this));
            });

            // 初始化indeterminate状态
            $('[data-indeterminate="true"]').each(function() {
                $(this).prop('indeterminate', true);
            });
        });
    </script>
</body>
</html>