<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>创建订单</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;创建订单
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box order-info">
            <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="quote_id" {if $quote}value="{$quote.id}"{/if} />

                <div class="cnt-basic">
                    <div class="cnt-title">
	                    订单基本信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>用户：</label>
                                <select name="user_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="users" id="vo"}
                                        <option value="{$vo.id}">{$vo.email}</option>
                                    {/volist}
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="email" {if $quote}value="{$quote.email}"{/if} />
                                <span class="must-input">*</span>
                            </div>
                            <div class="cnt-basic-i class_con">
                                <span class="input-tips"> 如果系统中不存在该Email账号，则自动创建用户账号，初始密码“opd123456”；如账号已存在，则自动归入该账号</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Country/Region：</label>
                                <select name="country" class="l_xiang">
                                    <option value="">
                                        Please select your country/region
                                    </option>
                                    {volist name="country" id="vo"}
                                    <option value="{$vo.en_name}" {if $quote && $quote.country==$vo.en_name}selected{/if}>{$vo.en_name} {$vo.cn_name}</option>
                                    {/volist}
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="first_name" {if $quote}value="{$quote.first_name}"{/if} />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单金额：</label>
                                <input type="text" name="money" style="width: 300px;" />
                                <select name="money_unit" class="l_xiang" style="width: 72px;background: url(__IMG__/icon_04.png) 67px 12px no-repeat;margin-left: 4px;">
                                    <option value="USD">USD</option>
                                    <option value="RMB">RMB</option>
                                    <option value="EUR">EUR</option>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>产品：</label>
                                <select name="product_id" id="product" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="product" id="vo"}
                                        <option value="{$vo.id}" {if $quote && $quote.product_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>项目号：</label>
                                <input type="text" name="project_no" value="" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>选择试用券：</label>
                                <select name="coupon_id" class="l_xiang" id="coupon_id">
                                    <option value="">请选择</option>
                                    {volist name="user_coupon" id="vo"}
                                        <option value="{$vo.id}">{$vo.name}% {$vo.description}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>Title：</label>
                                <input type="text" name="title" {if $quote}value="{$quote.title}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="phone" {if $quote}value="{$quote.phone}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Organization：</label>
                                <input type="text" name="organization" {if $quote}value="{$quote.organization}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>City：</label>
                                <input type="text" name="city" {if $quote}value="{$quote.city}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="last_name" {if $quote}value="{$quote.last_name}"{/if} />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单状态：</label>
                                <select name="order_status" class="l_xiang">
                                    <option value="0">进行中</option>
                                    <option value="1">已完成</option>
                                    <option value="2">已取消</option>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>服务：</label>
                                <select name="service_id" id="service" class="l_xiang">
                                    <option value="">请选择</option>
                                    {if $service}
                                    {volist name="service" id="vo"}
                                    <option value="{$vo.id}" {if $quote && $quote.service_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                    {/if}
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>备注：</label>
                                <textarea name="remark"></textarea>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="cnt-basic">
                    <div class="cnt-title">
	                    邮寄信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>物流单号：</label>
                                <input type="text" name="tracking_no" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="express_first_name" {if $quote}value="{$quote.first_name}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="express_email" {if $quote}value="{$quote.email}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Shipping Address：</label>
                                <input type="text" name="express_address" {if $quote}value="{$quote.express_address}"{/if} />
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>物流公司：</label>
                                <input type="text" name="tracking_company" value="" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="express_last_name" {if $quote}value="{$quote.last_name}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="express_phone" {if $quote}value="{$quote.phone}"{/if} />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Postcode：</label>
                                <input type="text" name="express_postcode" {if $quote}value="{$quote.express_postcode}"{/if} />
                            </div>
                        </div>
                    </div>

                    <div class="de_y">
                        <button class="de_y_l" type="submit" id="submitBtn">创建订单</button>
                        <a href="{:url('index')}" class="de_y_r" >返回</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        // 监听用户选择变化
        $('select[name="user_id"]').change(function() {
            var userId = $(this).val();
            if (userId) {
                // 发送AJAX请求获取用户信息
                $.ajax({
                    url: "/admin/Order/getUserData",
                    type: "POST",
                    data: {user_id: userId},
                    dataType: "json",
                    success: function(response) {
                        if (response.code == 1) {
                            // 填充表单字段
                            $('input[name="email"]').val(response.data.email);
                            $('input[name="first_name"]').val(response.data.first_name);
                            $('input[name="last_name"]').val(response.data.last_name);
                            $('input[name="title"]').val(response.data.title);
                            $('input[name="phone"]').val(response.data.phone);
                            $('input[name="organization"]').val(response.data.organization);
                            $('input[name="city"]').val(response.data.city);
                            $('input[name="express_address"]').val(response.data.express_address);
                            $('input[name="express_postcode"]').val(response.data.express_postcode);

                            $('input[name="express_email"]').val(response.data.email);
                            $('input[name="express_first_name"]').val(response.data.first_name);
                            $('input[name="express_last_name"]').val(response.data.last_name);
                            $('input[name="express_phone"]').val(response.data.phone);

                            $('select[name="country"]').val(response.data.country);

                            var $serviceSelect = $('#coupon_id');
                            $serviceSelect.empty(); // 清空现有选项
                            // 添加默认选项
                            $serviceSelect.append('<option value="" selected>请选择</option>');
                            // 动态添加 service 选项
                            $.each(response.data.user_coupon, function(index, service) {
                                $serviceSelect.append(
                                    '<option value="' + service.id + '">' + service.name + service.description + '</option>'
                                );
                            });
                            // 更新 UI 颜色（如果之前有样式逻辑）
                            $serviceSelect.css('color', '#333');
                        } else {
                            layer.msg(response.msg);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请重试');
                    }
                });
            } else {
                // 清空表单
                $('input[name="email"], input[name="country"], input[name="first_name"], input[name="last_name"], input[name="title"], input[name="phone"], input[name="express_address"], input[name="express_postcode"], input[name="organization"], select[name="country"], input[name="express_email"], input[name="express_first_name"], input[name="express_last_name"], input[name="express_phone"]').val('');
            }
        });

        // 监听 product 变化
        $('#product').on('change', function() {
            var productId = $(this).val();
            if (!productId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: '/getServices/' + productId,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        console.log(response)
                        var $serviceSelect = $('#service');
                        $serviceSelect.empty(); // 清空现有选项
                        // 添加默认选项
                        $serviceSelect.append('<option value="" selected>请选择</option>');
                        // 动态添加 service 选项
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        // 更新 UI 颜色（如果之前有样式逻辑）
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>