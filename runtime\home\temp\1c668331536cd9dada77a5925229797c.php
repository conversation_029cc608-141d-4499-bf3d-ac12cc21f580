<?php /*a:4:{s:66:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\icommunity\user.html";i:1751438870;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1750987676;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1751421836;s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1751438536;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>iCommunity-User - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(/static/home/<USER>/backgrounds/m_bj.jpg)] bg-no-repeat pb-5 bg-size-[100%] md:bg-[url(/static/home/<USER>/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-8">

        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            <?php echo htmlentities((string) $v['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8">
                <div class="md:flex md:items-center relative">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-auto cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="" method="post"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[300px] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-auto z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-auto">
                        </div>
                        <?php if(count($user_message)>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) count($user_message)); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                        md:min-w-[25rem] md:-left-[100%]
                        " id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-auto"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <strong>[System]</strong>
                                        <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div>
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[13px] bg-[12px_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-auto md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[26px] cursor-pointer"
                        id="menu_btn"></button>
                </div>
            </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Start here
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="contact md:mb-10">
                <!-- user-name -->
                <div class="bg-white border border-[#dae9ff] rounded-xl mb-4" data-aos="fade-up">
                    <!-- 头像左，名称经验右侧 -->
                    <div
                        class="flex items-center gap-x-4 p-4 border-b border-[#dae9ff] md:px-[3.75rem] md:py-[3.125rem] md:gap-x-[2.5rem]">
                        <div
                            class="flex-shrink-0 w-[4.375rem] h-[4.375rem] rounded-full overflow-hidden md:w-[8.75rem] md:h-[8.75rem]">
                            <img src="/static/home/<USER>/user-2.jpg" class="w-full h-full object-cover" alt="" />
                        </div>
                        <div class="flex flex-col gap-y-2 text-sm flex-1 md:gap-y-4">
                            <h1 class="text-lg font-bold md:text-3xl">Your Name</h1>
                            <div class="flex items-center gap-x-5 md:gap-x-5">
                                <div
                                    class="w-[100%] h-[.625rem] bg-[#dae9ff] rounded-md flex-1 md:flex-initial md:h-[.9375rem]  md:w-[12.5rem]">
                                    <div class="h-full bg-[#f08411] rounded-md" style="width: 50%;"></div>
                                </div>
                                <span class="flex-1 text-[#999] md:text-2xl md:flex-2">Senior user</span>
                            </div>
                        </div>
                    </div>

                    <!-- 展示话题 -->
                    <div class="p-4 border-b border-[#dae9ff] md:px-[3.75rem] md:py-[2.5rem]">
                        <div class="flex items-center gap-x-5 text-sm text-[#155797] md:text-2xl md:gap-x-10">
                            <span>Questions 20</span>
                            <span>posts 14</span>
                            <span>Reply 2</span>
                        </div>
                    </div>

                    <!-- 徽章列表 -->
                    <div class="p-4 md:px-[3.75rem] md:py-[2.5rem]">
                        <h2 class="text-lg font-bold mb-4 md:text-2xl md:mb-10">Badges</h2>

                        <!-- 徽章为空的时候 -->
                        <div class="text-center bg-[#fafbff] text-sm text-[#111] p-4 md:text-2xl"
                            style="display: none;">
                            Cris Rice did not receive any badges yet.
                        </div>
                        <!-- 徽章不为空的时候 -->
                        <div class="grid grid-cols-3 gap-4 md:flex md:flex-wrap md:gap-x-10 md:gap-y-5">
                            <div class="flex flex-col items-center gap-y-2.5 md:gap-y-5 badge-item">
                                <div class="w-[2.5rem] md:w-auto flex-shrink-0">
                                    <img src="/static/home/<USER>/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                </div>

                                <div class="text-sm md:text-xl text-[#111]">
                                    <p>Junior Badge</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-center gap-y-2.5 md:gap-y-5 badge-item">
                                <div class="w-[2.5rem] md:w-[4.6875rem] flex-shrink-0">
                                    <img src="/static/home/<USER>/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                </div>

                                <div class="text-sm md:text-xl text-[#111]">
                                    <p>Intermediate Badge</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-center gap-y-2.5 md:gap-y-5 badge-item">
                                <div class="w-[2.5rem] md:w-[4.6875rem] flex-shrink-0">
                                    <img src="/static/home/<USER>/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                </div>

                                <div class="text-sm md:text-xl text-[#111]">
                                    <p>Senior Badge</p>
                                </div>
                            </div>

                        </div>

                    </div>

                    <!-- 关于我 -->
                    <div class="p-4 md:px-[3.75rem] md:py-[2.5rem]">
                        <h2 class="text-lg font-bold mb-4 md:mb-10 md:text-2xl">About</h2>
                        <div class="border-t border-[#dae9ff] pt-4 text-sm flex flex-col gap-y-3 md:gap-y-5 md:text-xl md:pt-10">
                            <div
                                class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Registration Date
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>10 Mar 2022</p>
                                </div>
                            </div>
                            <div
                                class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Organization/Institution/Corporation
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>xx Biopharmaceutical Company</p>
                                </div>
                            </div>
                            <div
                                class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Title
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>Medical representative</p>
                                </div>
                            </div>
                            <div
                                class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Location (City, Country, Earth)
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>Ayr, Ontario</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- posts -->

                <div class="bg-white border border-[#dae9ff] rounded-xl" data-aos="fade-up" data-aos-delay="100">
                    <h2 class="text-lg font-bold mb-4 p-4 border-b border-[#dae9ff] md:text-2xl md:py-[2.5rem] md:px-[3.75rem]">Posts</h2>
                    <div class="list-posts">
                        <ul role="list">
                            <li>
                                <div class="item-posts">
                                    <div
                                        class="item-posts-left flex-shrink-0 w-[2.5rem] h-[2.5rem] rounded-md overflow-hidden md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                        <img src="/static/home/<USER>/user.jpg" class="w-full h-full object-cover" alt="" />
                                    </div>

                                    <div class="item-posts-right text-sm md:text-xl">
                                        <div class="item-posts-right-info text-[#999]">
                                            <span>10:13 AM on March 4, 2025</span> | <span>Published in <a href=""
                                                    class="underline">xxTopics</a></span>
                                        </div>
                                        <div class="item-posts-right-title">
                                            <h3 class="text-base md:text-2xl">
                                                <a href="" class="text-[#666]">This is a placeholder for the post
                                                    title</a>
                                            </h3>
                                        </div>
                                        <!-- 两个状态 审核中/审核失败-->
                                        <div class="item-posts-right-review text-[#f08411] md:absolute md:top-1/2 md:-translate-y-1/2 md:right-[3.75rem]">
                                            <p>Under review</p>
                                        </div>

                                        <!-- <div class="item-posts-right-review text-[#ff0000]">
                                            <p>Review failed</p>
                                        </div> -->
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="item-posts">

                                    <div
                                        class="item-posts-left flex-shrink-0 w-[2.5rem] h-[2.5rem] rounded-md overflow-hidden md:w-[5rem] md:h-[5rem]">
                                        <img src="/static/home/<USER>/user.jpg" class="w-full h-full object-cover" alt="" />
                                    </div>

                                    <div class="item-posts-right text-sm md:text-xl">
                                        <div class="item-posts-right-info text-[#999]">
                                            <span>10:13 AM on March 4, 2025</span> | <span>Published in <a href=""
                                                    class="underline">xxTopics</a></span>
                                        </div>
                                        <div class="item-posts-right-title">
                                            <h3 class="text-base md:text-2xl">
                                                <a href="" class="text-[#666]">This is a placeholder for the post
                                                    title</a>
                                            </h3>
                                        </div>
                                        <div class="item-posts-right-review text-[#ff0000] md:absolute md:top-1/2 md:-translate-y-1/2 md:right-[3.75rem]">
                                            <p>Review failed</p>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="item-posts">

                                    <div
                                        class="item-posts-left flex-shrink-0 w-[2.5rem] h-[2.5rem] rounded-md overflow-hidden md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                        <img src="/static/home/<USER>/user.jpg" class="w-full h-full object-cover" alt="" />
                                    </div>

                                    <div class="item-posts-right text-sm md:text-xl">
                                        <div class="item-posts-right-info text-[#999]">
                                            <span>10:13 AM on March 4, 2025</span> | <span>Published in <a href=""
                                                    class="underline">xxTopics</a></span>
                                        </div>
                                        <div class="item-posts-right-title">
                                            <h3 class="text-base md:text-2xl">
                                                <a href="" class="text-[#666]">This is a placeholder for the post
                                                    title</a>
                                            </h3>
                                        </div>
                                        <div class="item-posts-right-review text-[#f08411] md:absolute md:top-1/2 md:-translate-y-1/2 md:right-[3.75rem]">
                                            <p>Under review</p>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="item-posts">

                                    <div
                                        class="item-posts-left flex-shrink-0 w-[2.5rem] h-[2.5rem] rounded-md overflow-hidden md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                        <img src="/static/home/<USER>/user.jpg" class="w-full h-full object-cover" alt="" />
                                    </div>

                                    <div class="item-posts-right text-sm md:text-xl">
                                        <div class="item-posts-right-info text-[#999] md:mb-2">
                                            <span>10:13 AM on March 4, 2025</span> | <span>Published in <a href=""
                                                    class="underline">xxTopics</a></span>
                                        </div>
                                        <div class="item-posts-right-title">
                                            <h3 class="text-base md:text-2xl">
                                                <a href="" class="text-[#666]">This is a placeholder for the post
                                                    title</a>
                                            </h3>
                                        </div>
                                        <div class="item-posts-right-review text-[#f08411] md:absolute md:top-1/2 md:-translate-y-1/2 md:right-[3.75rem]">
                                            <p>Under review</p>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="item-posts">

                                    <div
                                        class="item-posts-left flex-shrink-0 w-[2.5rem] h-[2.5rem] rounded-md overflow-hidden md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                        <img src="/static/home/<USER>/user.jpg" class="w-full h-full object-cover" alt="" />
                                    </div>

                                    <div class="item-posts-right text-sm md:mt-5 md:text-xl">
                                        <div class="item-posts-right-info text-[#999] md:mb-2">
                                            <span>10:13 AM on March 4, 2025</span> | <span>Published in <a href=""
                                                    class="underline">xxTopics</a></span>
                                        </div>
                                        <div class="item-posts-right-title">
                                            <h3 class="text-base md:text-2xl group">
                                                <a href="" class="text-[#666] line-clamp-1 group-hover:text-[#999]">This is a placeholder for the post
                                                    title</a>
                                            </h3>
                                        </div>
                                        <div class="item-posts-right-review text-[#f08411] md:absolute md:top-1/2 md:-translate-y-1/2 md:right-[3.75rem]">
                                            <p>Under review</p>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div class="show-more-activity-btn text-sm p-3 md:py-[2.5rem] md:px-[3.5625rem] md:text-xl">
                            <button type="button"
                                class=" rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[5rem]">
                                SHOW MORE ACTIVITY
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-auto" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="/">
            Open DEL
        </a>
        <a href="/about">
            About
        </a>
        <a href="/contact">
            Contact Us
        </a>
        <a href="/privacy/terms">
            Terms of Service
        </a>
        <a href="/privacy/">
            Privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] py-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-28 md:h-28">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="" _target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-20 md:h-20 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-auto" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup() {
        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>


    <script>
        //显示更多
        setupShowMoreActivity(
            '.list-posts',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
    </script>

</body>

</html>