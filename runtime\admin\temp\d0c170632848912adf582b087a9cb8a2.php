<?php /*a:3:{s:74:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\resource\edit_category.html";i:1747127247;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1747295446;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1745983105;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<!-- tiny编辑器 -->
<script src="/static/admin/js/tinymce/tinymce.min.js"></script>
<script>
    // 通用配置
    const defaultTinyMCEConfig = {
        language: 'zh_CN',
        license_key: 'gpl',
        plugins: 'autoresize code image media link lists',
        height: '250px',
        toolbar: 'code | customMediaUpload image media | undo redo | bold italic | alignleft aligncenter alignright alignjustify | fontsize | link | numlist bullist',

        file_picker_types: 'media image', // 允许选择媒体和图片文件
        images_upload_url: '/admin/tinymceImage', // 图片上传接口

        min_height: 200,      // 最小高度200px
        max_height: 600,      // 最大高度600px
        // autoresize_bottom_margin: 10, // 底部间距（可选）
        // autoresize_overflow_padding: 10, // 溢出内边距（可选）
        promotion: false,  // 禁用推广提示

        setup: function(editor) {
            editor.ui.registry.addButton('customMediaUpload', {
                icon: 'upload',
                tooltip: '上传视频',
                onAction: function() {
                    editor.windowManager.open({
                        title: '上传视频',
                        body: {
                            type: 'panel',
                                items: [{
                                type: 'htmlpanel',
                                html: '<input type="file" id="tinymce-media-upload" accept="video/*,audio/*">'
                            }]
                        },
                        buttons: [
                            {
                                type: 'cancel',
                                name: 'cancel',
                                text: 'Cancel'
                            },
                            {
                                type: 'submit',
                                name: 'save',
                                text: 'Upload',
                                primary: true
                            }
                        ],
                        onSubmit: function(api) {
                            var fileInput = document.getElementById('tinymce-media-upload');
                            if (fileInput.files.length > 0) {
                                var formData = new FormData();
                                formData.append('file', fileInput.files[0]);

                                fetch('/admin/tinymceMedia', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    console.log(data)
                                    if (data.location) {
                                        editor.insertContent(
                                            data.location.match(/\.(mp4|webm|ogg|mov|avi)$/) ?
                                            '<video controls src="' + data.location + '"></video>' :
                                            '<audio controls src="' + data.location + '"></audio>'
                                        );
                                        api.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
    };

    // 封装初始化函数
    function initTinyMCE(selector, customConfig = {}) {
        tinymce.init({
            selector: selector,
            ...defaultTinyMCEConfig, // 通用配置
            ...customConfig, // 自定义配置
        });
    }
</script>
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改分类
                <a href="<?php echo url('category'); ?>" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="<?php echo url('edit_category'); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="<?php echo htmlentities((string) $getone['id']); ?>" />

                <div class="class_con">
                    <label>分类名称：</label>
                    <input type="text" name="name" value="<?php echo htmlentities((string) $getone['name']); ?>" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>图标：</label>
                    <input type="file" name="icon" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽800px 高500px。格式：png、jpg、jpeg）
                </div>
                <?php if($getone['icon']): ?>
                <div class="class_con">
                    <img src="<?php echo htmlentities((string) $getone['icon']); ?>">
                </div>
                <?php endif; ?>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="<?php echo url('category'); ?>" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script>
    // 初始化编辑器
    initTinyMCE('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const form = document.getElementById('formId');
    const submitBtn = document.getElementById('submitBtn');

    // 初始绑定提交事件
    if (form) {
        form.addEventListener('submit', handleSubmit);
    }

    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 解绑提交事件（避免重复提交）
        form.removeEventListener('submit', handleSubmit);

        // 禁用提交按钮（防止重复点击）
        submitBtn.disabled = true;
        submitBtn.textContent = '提交中...';

        try {
            // 同步 TinyMCE 内容（如果有）
            if (typeof tinymce !== 'undefined') {
                tinymce.triggerSave();
            }

            const formData = new FormData(form);

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });

            const data = await response.json();
            if (data.code === 1) {
                //提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                //提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.textContent = '确定';
            form.addEventListener('submit', handleSubmit);
        }
    }
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            addListItem(containerId, titleName, contentName, fileName); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, contentName, fileName) {
            const listItem = $('<div>').addClass('list-item');

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                listItem.append(titleInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const deselectAllBtn = document.getElementById('deselectAllBtn');
        const userSelect = document.querySelector('.user-multi-select');
        const selectedCount = document.getElementById('selectedCount');

        // 全选按钮
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                Array.from(userSelect.options).forEach(option => {
                    option.selected = true;
                });
                updateSelectedCount();
            });
        }

        // 取消全选按钮
        if(deselectAllBtn){
            deselectAllBtn.addEventListener('click', function() {
                Array.from(userSelect.options).forEach(option => {
                    option.selected = false;
                });
                updateSelectedCount();
            });
        }

        // 修改选择行为 - 不需要按Ctrl键
        if(userSelect){
            userSelect.addEventListener('mousedown', function(e) {
                if (e.target.tagName === 'OPTION') {
                    e.preventDefault();
                    e.target.selected = !e.target.selected;

                    // 触发更新
                    const event = new Event('change');
                    userSelect.dispatchEvent(event);
                }
            });

            // 用户选择变化时更新计数
            userSelect.addEventListener('change', updateSelectedCount);

            // 初始化计数
            updateSelectedCount();

            function updateSelectedCount() {
                const selectedOptions = Array.from(userSelect.selectedOptions);
                selectedCount.textContent = selectedOptions.length;
            }
        }

    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>

</body>
</html>