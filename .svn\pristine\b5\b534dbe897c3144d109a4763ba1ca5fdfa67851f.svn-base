/**
 * 封装的“显示更多”交互（原生JS）
 * @param {string} containerSelector - 外层容器选择器
 * @param {string} ulSelector - ul选择器
 * @param {string} btnSelector - 按钮选择器
 * @param {number} showCount - 默认显示的li数量
 */
function setupShowMoreActivity(containerSelector, ulSelector, btnSelector, showCount) {
    document.querySelectorAll(containerSelector).forEach(function(item) {
        var ul = item.querySelector(ulSelector);
        var lis = ul ? ul.children : [];
        var btn = item.querySelector(btnSelector);
        if (lis.length > showCount) {
            for (var i = showCount; i < lis.length; i++) {
                lis[i].style.display = 'none';
            }
            if (btn) btn.style.display = '';
        } else {
            if (btn) btn.style.display = 'none';
        }
        if (btn) {
            btn.addEventListener('click', function() {
                for (var i = 0; i < lis.length; i++) {
                    lis[i].style.display = '';
                }
                btn.style.display = 'none';
            });
        }
    });
}

// 调用示例
// setupShowMoreActivity(
//     '.iCommunity-content-item',   // 外层容器
//     'ul[role="list"]',            // ul选择器
//     'button',                     // 按钮选择器
//     4                             // 默认显示4个
// );