/**
 * 模板管理模块
 * 统一管理所有评论相关的HTML模板
 *
 * ===== 后端对接说明 =====
 *
 * 1. 模板渲染说明：
 *    - 主评论模板：generateMainComment()
 *    - 子评论模板：generateSubComment()
 *    - 编辑器模板：generateEditor()
 *
 * 2. @用户名高亮处理：
 *    - 检测格式：@用户名：内容
 *    - 自动高亮@用户名部分（蓝色显示）
 *    - 支持中文冒号和英文冒号
 *
 * 3. 用户信息显示：
 *    - 用户头像：user.avatar
 *    - 用户名：user.username
 *    - 用户等级：user.userLevel
 *    - 发布时间：time字段
 */
window.TemplateManager = (function() {
    'use strict';

    // 模板缓存
    const templates = {};

    /**
     * 初始化模板
     */
    function init() {
        // 缓存现有的模板
        templates.editor = $('#tpl-lzl-editor').html();
        templates.replyItem = $('#tpl-lzl-item').html();
    }

    /**
     * 生成主评论HTML
     * @param {object} data - 评论数据
     * @returns {string} HTML字符串
     */
    function generateMainComment(data) {
        return `
            <div class="py-5 px-3 reply-list" data-comment-id="${data.id}">
                <div class="p_postlist_c">
                    <div class="flex gap-x-2 md:gap-x-5 reply-list-item mb-3">
                        ${generateUserAvatar(data.user)}
                        <div class="iCommunity-right pt-1 flex-1 md:pt-5">
                            ${generateCommentHeader(data)}
                            ${generateCommentContent(data.content)}
                        </div>
                    </div>
                </div>
                ${generateCommentActions(data)}
                ${generateReplyContainer()}
            </div>
        `;
    }

    /**
     * 生成子评论HTML
     * @param {object} data - 评论数据
     * @returns {string} HTML字符串
     */
    function generateSubComment(data) {
        return `
            <div class="flex gap-x-2 md:gap-x-5 comment-reply-item mb-3" data-comment-id="${data.id}">
                ${generateUserAvatar(data.user, true)}
                <div class="iCommunity-right pt-1 flex-1 md:pt-5">
                    ${generateCommentHeader(data, true)}
                    ${generateCommentContent(data.content)}
                    ${generateSubCommentActions(data)}
                </div>
            </div>
        `;
    }

    /**
     * 生成用户头像区域
     *
     * ===== 后端对接说明 =====
     * 用户信息卡片需要的额外数据字段：
     * - user.id: 用户ID (用于data-user-id属性)
     * - user.stats: 用户统计数据 (可选)
     *   {
     *     questions: number,  // 提问数
     *     posts: number,      // 帖子数
     *     replies: number     // 回复数
     *   }
     * - user.badges: 用户徽章数据 (可选)
     * - user.about: 用户详细信息 (可选)
     *
     * @param {object} user - 用户数据
     * @param {boolean} isSubComment - 是否为子评论
     * @returns {string} HTML字符串
     */
    function generateUserAvatar(user, isSubComment = false) {
        // 为子评论也生成用户信息卡片
        const userInfoCard = generateUserInfoCard(user);

        // 为子评论添加特殊的CSS类用于定位
        const containerClass = isSubComment ? 'user-avatar-container sub-comment-avatar' : 'user-avatar-container main-comment-avatar';

        return `
            <div class="iCommunity-left w-[2.5rem] h-[2.5rem] rounded-md flex-shrink-0 relative md:w-[5rem] md:h-[5rem] border border-[#dae9ff] ${containerClass}" data-user-id="${user.id}">
                <div class="w-full h-full cursor-pointer user-avatar-trigger">
                    <img src="${user.avatar}" alt="${user.username}" class="w-full h-full rounded-md object-cover">
                </div>
                ${userInfoCard}
            </div>
        `;
    }

    /**
     * 生成用户信息卡片
     * @param {object} user - 用户数据
     * @returns {string} HTML字符串
     */
    function generateUserInfoCard(user) {
        return `
            <div class="iCommunity-left-info user-info-card absolute left-0 top-full z-50" style="display: none;">
                <div class="bg-[#fafbff] rounded-xl border border-[#dae9ff]" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                    <div class="flex items-start gap-x-3 md:gap-x-4 p-4 md:p-6">
                        <div class="w-[3rem] h-[3rem] md:w-[4rem] md:h-[4rem] flex-shrink-0">
                            <img src="${user.avatar || '/images/user.jpg'}" alt="${user.username || '用户'}" class="w-full h-full rounded-md object-cover">
                        </div>
                        <div class="flex flex-col gap-y-2 flex-1 min-w-0">
                            <div class="flex flex-col gap-y-1 md:flex-row md:items-center md:justify-between md:gap-x-3">
                                <div class="name-info min-w-0">
                                    <a href="#" class="text-base md:text-lg font-bold text-[#155797] block truncate">${user.username || '用户'}</a>
                                    <p class="text-xs md:text-sm text-[#999]">${user.userLevel || '普通用户'}</p>
                                </div>
                                <div class="message-btn flex-shrink-0">
                                    <a href="#" class="text-xs px-2 py-1 md:text-sm md:px-3 md:py-1.5 bg-[#155797] text-white rounded-md hover:bg-[#0d4a7a] transition-colors whitespace-nowrap">
                                        Private message
                                    </a>
                                </div>
                            </div>
                            <div class="mt-2 md:mt-3">
                                <ul class="text-sm justify-between md:text-xl flex items-center gap-x-3 md:gap-x-6 text-center md:justify-start">
                                    <li><span class="block font-bold">${(user.stats && user.stats.questions) || 0}</span><p class="text-[#155797] text-xs md:text-sm">Questions</p></li>
                                    <li><span class="block font-bold">${(user.stats && user.stats.posts) || 0}</span><p class="text-[#155797] text-xs md:text-sm">Posts</p></li>
                                    <li><span class="block font-bold">${(user.stats && user.stats.replies) || 0}</span><p class="text-[#155797] text-xs md:text-sm">Reply</p></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    ${generateBadgeAboutTabs(user)}
                </div>
            </div>
        `;
    }

    /**
     * 生成徽章/关于标签页
     * @param {object} user - 用户数据
     * @returns {string} HTML字符串
     */
    function generateBadgeAboutTabs(user = {}) {
        const badges = user.badges || [];
        const about = user.about || {};
        return `
            <div class="badge-about">
                <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                    <div class="badge-about-btn active">Badge</div>
                    <div class="badge-about-btn">About</div>
                </div>
                <div class="tab-content p-3 md:p-5">
                    <!-- Badge标签页内容 -->
                    <div class="tab-content-item">
                        ${generateBadgeContent(badges)}
                    </div>
                    <!-- About标签页内容 -->
                    <div class="tab-content-item" style="display: none;">
                        ${generateAboutContent(about)}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成徽章内容
     * @param {Array} badges - 徽章数组
     * @returns {string} HTML字符串
     */
    function generateBadgeContent(badges = []) {
        if (badges.length === 0) {
            return `
                <div class="tab-content-item-no-badge text-sm md:text-base text-center py-4">
                    <p class="text-gray-500">The user has not yet obtained any badges.</p>
                </div>
            `;
        }

        const badgeItems = badges.map(badge => `
            <div class="tab-content-item-badge mb-4">
                <div class="flex items-center gap-x-2.5 md:gap-x-4 badge-item">
                    <div class="flex flex-col gap-y-2 items-center">
                        <div class="w-[2rem] h-[2rem] md:w-[3rem] md:h-[3rem] flex-shrink-0 bg-white rounded">
                            <img src="${badge.icon || '/images/iCommunity/icon_1.png'}" alt="${badge.name}" class="w-full h-full object-cover">
                        </div>
                        <div class="text-xs md:text-sm text-gray-600">
                            <p>${badge.name || 'Badge'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        return `<div class="flex flex-wrap gap-4">${badgeItems}</div>`;
    }

    /**
     * 生成关于内容
     * @param {Object} about - 关于信息对象
     * @returns {string} HTML字符串
     */
    function generateAboutContent(about = {}) {
        const items = [
            { label: 'Registration Date', value: about.joinDate || '2024-01-01' },
            { label: 'Location', value: about.location || 'Unknown' },
            { label: 'Organization', value: about.organization || 'N/A' }
        ];

        const aboutItems = items.map(item => `
            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-1 md:gap-y-0 md:gap-x-4 py-2 border-b border-gray-100 last:border-b-0">
                <div class="about-item-left text-sm md:text-base text-gray-600">${item.label}</div>
                <div class="about-item-right text-sm md:text-base font-medium text-gray-800">${item.value}</div>
            </div>
        `).join('');

        return `<div class="flex flex-col gap-y-2">${aboutItems}</div>`;
    }

    /**
     * 生成评论头部信息
     * @param {object} data - 评论数据
     * @param {boolean} isSubComment - 是否为子评论
     * @returns {string} HTML字符串
     */
    function generateCommentHeader(data, isSubComment = false) {
        if (isSubComment) {
            return `
                <div class="iCommunity-right-info flex gap-y-1 text-sm md:text-xl md:mb-5">
                    <div class="flex gap-x-1 md:gap-x-3">
                        <div class="iCommunity-right-title-name">
                            <a href="#" class="text-[#155797]">${data.user.username}</a>
                        </div>
                        <div class="text-[#999]">${data.user.userLevel}</div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="iCommunity-right-info flex justify-between flex-col md:flex-row gap-y-1 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                <div class="flex gap-x-1 md:gap-x-3">
                    <div class="iCommunity-right-title-name">
                        <a href="#" class="text-[#155797]">${data.user.username}</a>
                    </div>
                    <div class="text-[#999]">${data.user.userLevel}</div>
                </div>
                <div class="iCommunity-right-title-time text-[#999]">${data.time}</div>
            </div>
        `;
    }

    /**
     * 生成评论内容
     * @param {string} content - 评论内容
     * @returns {string} HTML字符串
     */
    function generateCommentContent(content) {
        // 处理@用户名格式，将其高亮显示
        let processedContent = CommentUtils.escapeHtml(content);

        // 检查是否包含@用户名：格式
        const atUserMatch = processedContent.match(/^@([^：:]+)[：:]\s*(.*)$/);
        if (atUserMatch) {
            const userName = atUserMatch[1];
            const actualContent = atUserMatch[2];
            processedContent = `<span class="text-[#155797] font-medium">@${userName}：</span>${actualContent}`;
        }

        return `
            <div class="iCommunity-right-content mb-2 md:mb-5">
                <div class="iCommunity-right-content-info about-container text-sm md:text-xl">
                    <p>${processedContent}</p>
                </div>
            </div>
        `;
    }

    /**
     * 生成主评论操作按钮
     * @param {object} data - 评论数据
     * @returns {string} HTML字符串
     */
    function generateCommentActions(data) {
        return `
            <div class="text-right text-sm">
                <div class="iCommunity-right-time-right flex justify-end">
                    <button type="button" class="text-[#155797] flex items-center justify-center gap-x-1.5 py-1 px-3 bg-white border border-[#dae9ff] md:gap-x-2 text-sm cursor-pointer pack-reply" style="display: none;">
                        ${generateReplyIcon()}
                        <span>pack up</span>
                    </button>
                    <button type="button" class="text-[#155797] flex items-center justify-center gap-x-1.5 py-1 px-3 bg-white border border-[#dae9ff] md:gap-x-2 text-sm cursor-pointer lzl_link_unfold">
                        <span>Reply(0)</span>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 生成子评论操作按钮
     * @param {object} data - 评论数据
     * @returns {string} HTML字符串
     */
    function generateSubCommentActions(data) {
        return `
            <div class="flex gap-x-2 justify-end text-sm md:text-base">
                <div class="iCommunity-right-title-time text-[#999]">${data.time}</div>
                <button type="button" class="cursor-pointer text-[#155797] item-building">Reply</button>
            </div>
        `;
    }

    /**
     * 生成回复容器
     * @returns {string} HTML字符串
     */
    function generateReplyContainer() {
        return `
            <div class="comment-reply md:pl-[6.25rem]" style="display: none;">
                <div class="bg-white border border-[#dae9ff] p-3 md:p-5">
                    <div class="l_comment_list mb-3 md:mb-5"></div>
                    <div class="flex flex-col reply-comment">
                        <div class="flex justify-end md:text-base">
                            <button type="button" class="text-[#155797] flex items-center justify-center gap-x-1.5 py-1 px-3 bg-white border border-[#dae9ff] md:gap-x-2 text-sm cursor-pointer item-reply">
                                ${generateReplyIcon()}
                                <span>Reply</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成回复图标
     * @returns {string} SVG图标HTML
     */
    function generateReplyIcon() {
        return `
            <svg t="1751520152503" class="w-[1rem] md:w-6" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4543">
                <path d="M191.825 155.411h639.133c31.777 0 60.641 12.979 81.549 33.885 20.908 20.908 33.886 49.78 33.886 81.547v386.981c0 31.773-12.978 60.642-33.886 81.55s-49.771 33.881-81.549 33.881H617.095c-51.358 65.264-86.005 97.265-115.505 96.842-34.341-0.48-51.522-33.026-64.004-96.842h-245.76c-31.77 0-60.641-12.973-81.547-33.881-20.908-20.908-33.885-49.776-33.885-81.55v-386.98c0-31.767 12.977-60.639 33.885-81.547 20.905-20.907 49.776-33.886 81.546-33.886zM321.3 397.295h4.778c26.955 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005H321.3c-26.955 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.044-48.999 48.999-48.999z m370.743 0h4.777c26.956 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005h-4.777c-26.955 0-48.998-22.05-48.998-49.005v-0.006c0-26.955 22.043-48.999 48.998-48.999z m-188.393 0h4.779c26.953 0 48.997 22.043 48.997 48.999v0.006c0 26.955-22.044 49.005-48.997 49.005h-4.779c-26.953 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.046-48.999 48.999-48.999z m327.308-190.478H191.825c-17.576 0-33.59 7.215-45.2 18.827-11.614 11.612-18.827 27.626-18.827 45.2v386.981c0 17.58 7.213 33.589 18.827 45.202 11.61 11.614 27.625 18.825 45.2 18.825H480.773l3.555 21.583c8.232 49.979 13.602 75.405 17.866 75.462 11.309 0.163 36.949-28.559 82.164-87.002l7.764-10.043h238.836c17.583 0 33.592-7.211 45.202-18.825 11.613-11.613 18.828-27.622 18.828-45.202V270.844c0-17.574-7.215-33.588-18.828-45.2-11.61-11.612-27.619-18.827-45.202-18.827z" fill="#155797" p-id="4544"></path>
            </svg>
        `;
    }

    /**
     * 生成编辑器HTML
     * @param {string} type - 编辑器类型 (reply, building)
     * @param {string} placeholder - 占位符文本
     * @returns {string} HTML字符串
     */
    function generateEditor(type = 'reply', placeholder = '', replyToComment = '') {
        return `
            <div class="lzl_editor_container" data-off="true" data-type="${type}" data-comment-id="${replyToComment}">
                <div class="edui-container h-[6.25rem] overflow-y-auto border border-[#dae9ff] rounded-sm p-3 bg-white text-sm mb-2 md:mb-5" contenteditable="true">${placeholder}</div>
                <div class="flex justify-end">
                    <button type="button" class="bg-[#155797] py-2 px-3.5 text-[#fff] text-sm rounded-sm cursor-pointer md:text-base release-btn">Release</button>
                </div>
            </div>
        `;
    }

    // 公开API
    return {
        init,
        generateMainComment,
        generateSubComment,
        generateEditor,
        templates
    };
})();
