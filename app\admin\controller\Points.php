<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Points extends Common
{
    public function index(){
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["u.email|u.first_name|u.last_name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('User_points')->alias("p")
            ->field("p.*, u.email")
            ->where($where)
            ->leftJoin('User u', 'u.id = p.user_id')
            ->order("p.id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

}
