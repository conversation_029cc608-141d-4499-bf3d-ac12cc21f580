<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">用户列表</a></p>

                <a href="{:url('add', ['type' => $type])}" class="add-button">
                    {if $type==0}
                    添加前台用户
                    {else}
                    添加后台用户
                    {/if}
                </a>

                <div class="search-container">
                    <form method="get" action="{:url('index')}">
                        <input
                            type="text"
                            name="keyword"
                            placeholder="请输入邮箱或姓名..."
                            value="{$params.keyword ?? ''}"
                            class="search-input"
                        >
                        <button type="submit" class="search-button">搜索</button>
                    </form>
                </div>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">Email</td>
                            <td class="mid_one">First Name</td>
                            <td class="mid_one">Last Name</td>
                            <td class="mid_one">角色</td>
                            <td class="mid_one">注册时间</td>
                            <td class="mid_one">状态</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one">{$vo.email}</td>
                            <td class="mid_one">{$vo.first_name}</td>
                            <td class="mid_one">{$vo.last_name}</td>
                            <td class="mid_one">{$vo.role_name}</td>
                            <td class="mid_one">{$vo.create_time}</td>
                            <td class="mid_one">{$vo.status==0?"禁用":"正常"}</td>
                            <td class="mid_s">
                                <a href="{:url('del', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit', ['id'=>$vo['id']])}" class="basic">修改</a>
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>