<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Email extends Common
{
    public function index()
    {
        $List = Db::name('Email')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                $item['user'] = Db::name("User")->where("id", $item['user_id'])->field("id, email, phone")->find();
                $item['template'] = Db::name("Email_template")->where("id", $item['template_id'])->find();

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

    public function account()
    {
        $List = Db::name('Email_account')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add_account()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['host'] || !$data['port'] || !$data['email'] || !$data['password']){
                $this->error("必填项未填！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Email_account")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit_account()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['host'] || !$data['port'] || !$data['email'] || !$data['password']){
                $this->error("必填项未填！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Email_account")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Email_account")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_account()
    {
        $id = input('id');
        $s = Db::name("Email_account")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    public function template()
    {
        $List = Db::name('Email_template')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                $item['email'] = $item['account_id']?Db::name("Email_account")->where("id", $item['account_id'])->value('email'):"";

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

    public function add_template()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['account_id'] || !$data['identifier'] || !$data['subject'] || !$data['content']){
                $this->error("必填项未填！");
            }

            if(Db::name("Email_template")->where("identifier", $data['identifier'])->find()){
                $this->error("模板标识已存在，请重新填写！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Email_template")->strict(false)->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            //发件账号
            $accounts = Db::name("Email_account")->select();

            return view("", [
                "accounts" => $accounts
            ]);
        }
    }

    public function edit_template()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['account_id'] || !$data['identifier'] || !$data['subject'] || !$data['content']){
                $this->error("必填项未填！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Email_template")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Email_template")->where("id", $id)->find();

            //发件账号
            $accounts = Db::name("Email_account")->select();

            return view("", [
                "getone" => $getone,
                "accounts" => $accounts
            ]);
        }
    }

    public function del_template()
    {
        $id = input('id');
        $s = Db::name("Email_template")->where("id", $id)->update(["status"=>0]);
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }
}
