<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加试用券
                <a href="{:url('index', ['coupon_type'=>$coupon_type])}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="coupon_type" value="{$coupon_type}" />

                <div class="class_con">
                    <label>折扣：</label>
                    <input type="text" name="name" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>说明：</label>
                    <input type="text" name="description" />
                    <span class="must-input">*</span>
                </div>

                {if $coupon_type==0}
                <div class="class_con">
                    <label>选择产品：</label>
                    <div class="multi-select-container">
                        <div class="user-select-header">
                            <button type="button" class="select-all-btn">全选</button>
                            <button type="button" class="deselect-all-btn">取消选择</button>
                            <span class="selected-count">已选择: <span class="count">0</span> 个产品</span>
                        </div>
                        <select name="product_ids[]" class="l_xiang user-multi-select" multiple="multiple" size="5">
                            {volist name="product" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>

                <div class="class_con">
                    <label>选择服务：</label>
                    <div class="multi-select-container">
                        <div class="user-select-header">
                            <button type="button" class="select-all-btn">全选</button>
                            <button type="button" class="deselect-all-btn">取消选择</button>
                            <span class="selected-count">已选择: <span class="count">0</span> 个服务</span>
                        </div>
                        <select name="service_ids[]" class="l_xiang user-multi-select" multiple="multiple" size="6">
                            {volist name="service" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>

                <div class="class_con">
                    <label>整体：</label>
                    <input type="checkbox" name="scope_type" />
                    <span class="input-tips">默认“单独”（可选择用户赠送），“整体”用于所有用户</span>
                </div>

                <div class="class_con">
                    <label>开始时间：</label>
                    <input type="text" name="start_time" id="start_time" value="{:date('Y-m-d')}" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>结束时间：</label>
                    <input type="text" name="end_time" id="end_time" value="" />
                    <span class="must-input">*</span>
                </div>
                {else }
                <div class="class_con">
                    <label>积分数：</label>
                    <input type="text" name="points" value="0" />
                    <span class="must-input">*</span>
                </div>
                {/if}

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index', ['coupon_type'=>$coupon_type])}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        laydate.render({
            elem: '#start_time',  // 绑定元素
            type: 'date',     // 选择日期
            format: 'yyyy-MM-dd'  // 自定义格式：年-月-日
        });

        laydate.render({
            elem: '#end_time',  // 绑定元素
            type: 'date',     // 选择日期
            format: 'yyyy-MM-dd'  // 自定义格式：年-月-日
        });
    </script>

</body>
</html>