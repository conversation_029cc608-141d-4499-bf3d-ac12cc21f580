<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

use app\admin\model\Menu as MenuModel;

class Menu extends Common
{
    public function index(){
        if ($this->request->isPost()) {
            $menuOrder = json_decode($this->request->post('menu_order'), true);

            if (!empty($menuOrder)) {
                $menuModel = new MenuModel(); // 替换为你的菜单模型

                try {
                    $menuModel->startTrans();

                    foreach ($menuOrder as $item) {
                        $menuModel->where('id', $item['id'])
                                ->update([
                                    'parent_id' => $item['parent_id'],
                                    'sort' => $item['sort']
                                ]);
                    }

                    $menuModel->commit();
                    return json(['code' => 1, 'msg' => '排序保存成功']);
                } catch (\Exception $e) {
                    $menuModel->rollback();
                    return json(['code' => 0, 'msg' => '排序保存失败: ' . $e->getMessage()]);
                }
            }

        } else {
            $menus = Db::name("Backend_menus")
                ->where("status", 1)
                ->order('parent_id asc, sort asc')
                ->select()
                ->toArray();;

            // 构建树形结构
            $menuTree = $this->buildMenuTree($menus);

            // 生成初始顺序数据
            $initialOrder = $this->generateInitialOrder($menuTree);

            return view("", [
                'menus' => $menuTree,
                'initialOrder' => json_encode($initialOrder)
            ]);
        }
    }

    // 构建树形结构
    protected function buildMenuTree($menus, $parentId = 0)
    {
        $tree = [];
        foreach ($menus as $menu) {
            if ($menu['parent_id'] == $parentId) {
                $children = $this->buildMenuTree($menus, $menu['id']);
                if ($children) {
                    $menu['son'] = $children;
                }
                $tree[] = $menu;
            }
        }
        return $tree;
    }


    // 生成初始顺序数据
    protected function generateInitialOrder($menuTree)
    {
        $orderData = [];
        $order = 0;

        foreach ($menuTree as $level1) {
            $order++;
            $orderData[] = [
                'id' => $level1['id'],
                'parent_id' => 0,
                'order' => $order
            ];

            if (!empty($level1['son'])) {
                $childOrder = 0;
                foreach ($level1['son'] as $level2) {
                    $childOrder++;
                    $orderData[] = [
                        'id' => $level2['id'],
                        'parent_id' => $level1['id'],
                        'order' => $childOrder
                    ];

                    if (!empty($level2['son'])) {
                        $child2Order = 0;
                        foreach ($level2['son'] as $level3) {
                            $child2Order++;
                            $orderData[] = [
                                'id' => $level3['id'],
                                'parent_id' => $level2['id'],
                                'order' => $child2Order
                            ];
                        }
                    }
                }
            }
        }

        return $orderData;
    }

}
