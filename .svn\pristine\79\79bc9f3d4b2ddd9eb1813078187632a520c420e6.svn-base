<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Faq extends Common
{
    public function index()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["question|answer", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('Faq')
            ->where($where)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['question']){
                $this->error("必填项未填！");
            }

            if(!trim($data['seo_url'])){
                $data['seo_url'] = seoFriendlyUrl($data['question']);
            }
            if(Db::name('Faq')->where("seo_url", $data['seo_url'])->find()){
                throw new \Exception("URL已存在！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Faq")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            $category = Db::name('Faq_category')
            ->where("pid", 0)
            ->select()
            ->each(function($item){
                //子分类
                $where = [
                    "pid" => $item['id']
                ];
                $son = Db::name('Faq_category')
                ->where($where)
                ->select();
                $item['son'] = $son;

                return $item;
            });

            return view("", [
                "category" => $category
            ]);
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['question']){
                $this->error("必填项未填！");
            }

            if(!trim($data['seo_url'])){
                $data['seo_url'] = seoFriendlyUrl($data['question']);
            }
            $where = [
                ['id', '<>', $data['id']],
                ['seo_url', '=', $data['seo_url']],
            ];
            if(Db::name('Faq')->where($where)->find()){
                throw new \Exception("URL已存在！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Faq")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Faq")->where("id", $id)->find();

            $category = Db::name('Faq_category')
            ->where("pid", 0)
            ->select()
            ->each(function($item){
                //子分类
                $where = [
                    "pid" => $item['id']
                ];
                $son = Db::name('Faq_category')
                ->where($where)
                ->select();
                $item['son'] = $son;

                return $item;
            });

            return view("", [
                "getone" => $getone,
                "category" => $category
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Faq")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    public function category()
    {
        $where = [
            "pid" => 0
        ];

        $List = Db::name('Faq_category')
        ->where($where)
        ->select()
        ->each(function($item){
            //子分类
            $where = [
                "pid" => $item['id']
            ];
            $son = Db::name('Faq_category')
            ->where($where)
            ->select();
            $item['son'] = $son;

            return $item;
        });

        return view("", [
            "List" => $List
        ]);
    }

    public function add_category()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Faq_category")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            $category = Db::name("Faq_category")->where("pid", 0)->column("id, name");
            return view("", [
                "category" => $category,
            ]);
        }
    }

    public function edit_category()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Faq_category")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Faq_category")->where("id", $id)->find();

            $category = Db::name("Faq_category")->where("pid", 0)->column("id, name");

            return view("", [
                "getone" => $getone,
                "category" => $category,
            ]);
        }
    }

    public function del_category()
    {
        $id = input('id');
        $s = Db::name("Faq_category")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    public function ask()
    {
        $List = Db::name('Faq_ask')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

}
