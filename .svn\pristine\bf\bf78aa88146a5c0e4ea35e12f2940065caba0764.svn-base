<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">用户信息</a></p>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">用户名</td>
                            <td class="mid_one">手机号码</td>
                            <td class="mid_one">内容</td>
                            <td class="mid_one">系统回复</td>
                            <td class="mid_one">创建时间</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one">{$vo.user.usernmae}</td>
                            <td class="mid_one">{$vo.user.phone}</td>
                            <td class="mid_one">{$vo.content}</td>
                            <td class="mid_one">{$vo.reply_content}</td>
                            <td class="mid_one">{$vo.create_time}</td>
                            <td class="mid_s">
                                <a href="{:url('reply_info', ['id'=>$vo['id']])}" class="basic">回复</a>
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>