<?php /*a:4:{s:61:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\forum\post.html";i:1753861553;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1753070097;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1753067529;s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1753068496;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Post - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
    <link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
    <link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>
<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(/static/home/<USER>/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(/static/home/<USER>/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(/static/home/<USER>/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0 relative" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full md:relative md:z-0">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            <?php echo htmlentities((string) $v['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 bg-size-[1rem] group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>

                    <?php if($basic['forum_status']==1): ?>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8 md:relative md:z-20">
                <div class="md:flex md:items-center">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-size-[1.3rem] cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="/search" method="get"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[18.75rem] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                name="q"
                                value="<?php echo isset($keyword) ? htmlentities((string) $keyword) : ''; ?>"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-size-[1.3rem] z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-[1.2rem]">
                        </div>
                        <?php if($user_message_count>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) $user_message_count); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                                md:min-w-[25rem] md:-left-[100%]" id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-tl-xl rounded-tr-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[System]</strong><span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; if(count($user_icommunity)>0 && $basic['letter_status']==1): ?>
                        <div class="bg-white shadow-2xl rounded-bl-xl rounded-br-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">iCommunity</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_icommunity) || $user_icommunity instanceof \think\Collection || $user_icommunity instanceof \think\Paginator): $i = 0; $__LIST__ = $user_icommunity;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/private-message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[<?php echo htmlentities((string) $vo['first_name']); ?> <?php echo htmlentities((string) $vo['last_name']); ?>]</strong>
                                            <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/private-message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[0.8125rem] bg-[0.75rem_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-size-[1.1rem] md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[1.3rem] cursor-pointer"
                        id="menu_btn"></button>
                </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Search
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="contact" data-aos="fade-up">
                <h1 class="text-2xl md:text-5xl md:mb-10 mb-5 Roboto_Bold"> Start here</h1>
                <div class="bg-[#f8fdff] rounded-xl border border-[#dae9ff] p-4 md:px-7 md:py-10">
                    <form action="/iCommunity/post" id="formId">
                        <!-- 类型选择 -->
                        <div class="mb-5 md:mb-10">
                            <h2 class="text-xl font-bold mb-2.5 md:text-xl md:mb-6">
                                What would you like to do...
                            </h2>
                            <div class="flex flex-col gap-y-3.5 md:flex-row md:gap-x-10 md:gap-y-0 md:w-10/12 md:grid md:grid-cols-2">
                                <label class="grid grid-cols-[24px_1fr_auto] items-center gap-6 rounded-lg p-4 ring-1 ring-transparent hover:bg-gray-100 has-checked:bg-indigo-50 has-checked:text-[#155797] has-checked:font-bold has-checked:ring-indigo-200 dark:hover:bg-white/5 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked: text-[#111111] has-checked:border-[#155290] border border-[#c4d7ff] text-center cursor-pointer md:text-3xl md:px-6"
                                    for="question">
                                    <svg class="w-4 md:w-8" fill="currentColor" viewBox="0 0 1024 1024">
                                        <path
                                            d="M511.998 16.512c-273.649 0-495.482 221.837-495.482 495.486 0 273.651 221.833 495.49 495.482 495.49 273.653 0 495.486-221.839 495.486-495.488 0-273.649-221.831-495.488-495.486-495.488zM560.084 797.914c0 13.682-11.089 24.776-24.774 24.776h-74.323c-13.679 0-24.772-11.093-24.772-24.776v-74.321c0-13.686 11.093-24.778 24.772-24.778h74.323c13.684 0 24.774 11.091 24.774 24.778v74.321zM682.891 456.897c-9.958 14.199-32.561 32.291-60.858 54.35l-31.359 21.64c-15.23 11.814-28.738 25.568-33.733 41.315-1.707 5.365-2.986 14.183-3.847 23.706-0.434 4.792-4.721 14.568-14.741 14.568-24.551 0-71.341 0-80.651 0-13.109 0-15.451-10.268-15.232-15.291 1.451-32.919 4.468-62.144 17.88-77.878 27.155-31.839 88.943-71.469 88.943-71.469 9.407-7.099 17.023-14.816 22.783-23.226 10.471-14.438 19.158-30.294 19.158-47.626 0-19.921-5.824-38.079-17.51-54.515-11.646-16.371-32.979-24.573-63.891-24.573-30.43 0-52.001 10.1-64.716 30.291-9.393 14.918-15.307 28.634-17.756 43.558-0.871 5.282-4.258 16.407-15.548 16.407-23.854 0-67.833 0-78.66 0-16.749 0-20.437-10.854-19.953-16.086 6.063-65.94 31.831-110.993 77.393-139.922 30.981-19.918 69.097-29.913 114.31-29.913 59.41 0 108.726 14.162 148.043 42.527 39.247 28.326 58.927 70.299 58.927 125.952 0.004 34.082-11.958 62.822-28.98 86.185z"
                                            p-id="4486"></path>
                                    </svg>
                                    Ask a Question
                                    <input id="question" type="radio" class="box-content h-1.5 w-1.5 appearance-none rounded-full border-[0.3125rem] border-white bg-white bg-clip-padding ring-1 ring-gray-950/20 outline-none checked:border-[#155797] checked:ring-[#155797] md:w-3 md:h-3"
                                        name="post_type" checked="" value="0">
                                </label>

                                <label for="posting" class="grid grid-cols-[24px_1fr_auto] items-center gap-6 rounded-lg p-4 ring-1 ring-transparent hover:bg-gray-100 has-checked:bg-indigo-50 has-checked:text-[#155797] has-checked:font-bold has-checked:ring-indigo-200 dark:hover:bg-white/5 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked: text-[#111111] has-checked:border-[#155290] border border-[#c4d7ff] text-center cursor-pointer md:text-3xl md:px-6">
                                    <svg t="1751437602379" fill="currentColor" class="w-4 md:w-8"
                                        viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                                        p-id="6249" >
                                        <path
                                            d="M504.149333 89.429333A422.570667 422.570667 0 0 0 81.578667 512c0 66.56 2.048 192.170667 4.096 289.109333a136.533333 136.533333 0 0 0 136.533333 133.461334h267.946667a431.445333 431.445333 0 0 0 436.565333-414.72A422.570667 422.570667 0 0 0 504.149333 89.429333z m30.037334 580.266667H286.72a34.133333 34.133333 0 1 1 0-68.266667h247.466667a34.133333 34.133333 0 0 1 0 68.266667zM662.869333 443.733333H286.72a34.133333 34.133333 0 1 1 0-68.266666h375.466667a34.133333 34.133333 0 0 1 0 68.266666z"
                                            p-id="6250"></path>
                                    </svg>
                                    Start posting
                                    <input id="posting" type="radio"
                                        class="box-content h-1.5 w-1.5 appearance-none rounded-full border-[0.3125rem] border-white bg-white bg-clip-padding ring-1 ring-gray-950/20 outline-none checked:border-[#155797] checked:ring-[#155797] md:w-3 md:h-3"
                                        name="post_type" value="1">
                                </label>
                            </div>
                        </div>

                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">
                                Title
                            </h2>
                            <div class="">
                                <input id="post_title" name="title" type="text" placeholder="Enter title here" value="" class="border w-full py-2.5 px-3 border-[#bdcbe9] bg-white rounded-md h-[3.125rem] md:h-[5rem] md:rounded-xl md:px-[2.1875rem] md:text-xl" />
                            </div>
                            <div class="hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the information.
                            </div>
                        </div>
                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">Description</h2>
                            <div class="md:text-xl">
                                <textarea name="content" placeholder="Enter description here" class="tiny-editor border w-full py-2.5 px-3 border-[#bdcbe9] bg-white rounded-md h-[10rem]" id="textarea"></textarea>
                            </div>
                        </div>

                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">Topic</h2>
                            <div class="relative flex flex-col mt-2 z-10 bg-white md:text-xl">
                                <select name="topic" id="Country" class="country w-full h-[3.125rem] border border-[#bdcbe9] rounded-md bg-transparent px-5 pr-12 appearance-none text-[#999] cursor-pointer
                                    md:h-[5rem]
                                    ">
                                    <option value="" disabled selected style="color:#999;">Choose your Topic</option>
                                    <?php if(is_array($topic) || $topic instanceof \think\Collection || $topic instanceof \think\Paginator): $i = 0; $__LIST__ = $topic;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <option value="<?php echo htmlentities((string) $vo['name']); ?>" style="color: #333;"><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <button type="button" class="rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full -z-10 flex items-center justify-center">
                                    <svg t="1751443158415" class="w-4 md:w-6" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1472"><path d="M483.072 714.496l30.165333 30.208 415.957334-415.829333a42.837333 42.837333 0 0 0 0-60.288 42.538667 42.538667 0 0 0-60.330667-0.042667l-355.541333 355.413333-355.242667-355.413333a42.496 42.496 0 0 0-60.288 0 42.837333 42.837333 0 0 0-0.085333 60.330667l383.701333 383.872 1.706667 1.749333z" fill="#999999" p-id="1473"></path></svg>
                                </button>
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2 md:text-base">
                                Please fill in the information.
                            </div>
                        </div>

                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">
                              Tags <span class="text-[#999999]">Optional</span>
                            </h2>
                            <div class="relative flex flex-col mt-2 z-10 bg-white md:text-xl">
                                <input type="hidden" name="tags" id="tags-input" value="">
                                <div id="multiselect"></div>
                            </div>
                        </div>

                        <input type="hidden" name="mentioned_users" id="mentioned-users" value="">

                        <div class="">
                            <button type="submit" id="submitBtn" class="bg-[#155797] text-white text-lg w-full h-[3.125rem] rounded-md md:h-[5rem] md:w-[14.375rem] cursor-pointer">CREATE</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </section>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-[10rem]" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="/">
            Open DEL
        </a>
        <a href="/about">
            About
        </a>
        <a href="/contact">
            Contact Us
        </a>
        <a href="/privacy/terms">
            Terms of Service
        </a>
        <a href="/privacy/">
            Privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] p-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-20 md:h-20">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="" _target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl border-[#dfe7ff] border">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 w-[1.3rem] inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-14 md:h-14 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-[1.3rem]" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" onclick="window.location.href = '/quote?' + localStorage.getItem('quoteParams'); return false;" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>
<script src="/static/home/<USER>/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup(params) {
        // 存储参数到本地存储或全局变量
        localStorage.setItem('quoteParams', params);

        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>


    <script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
    <script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
    <script src="/static/ckeditor5/ckeditor5-home.js"></script>

    <script>
        var customEditor = new CKEditorManager();
        customEditor.initAll('.tiny-editor');
    </script>

    <script>
        var selects = document.querySelectorAll('select.country');
        selects.forEach(function (select) {
            // 监听 change 事件
            select.addEventListener('change', function () {
                if (select.value === "") {
                    select.style.color = "#999";
                } else {
                    select.style.color = "#333";
                }
            });
            // 初始化时设置颜色
            if (select.value === "") {
                select.style.color = "#999";
            } else {
                select.style.color = "#333";
            }
        });

        const options = <?php echo $tagsOptions; ?>;
        const multi = new MultiSelect({
            container: '#multiselect',
            options,
            max: 8, // 最多能选n个
            defaultSelected: [] // 这里设置默认选中
        });

        // 覆盖原方法，直接更新 input
        multi.renderTags = function() {
            MultiSelect.prototype.renderTags.call(this); // 调用父类方法
            MultiSelect.prototype.renderDropdown.call(this); // 调用父类方法
            document.getElementById('tags-input').value = this.selected.join(',');
        };
    </script>

    <script>
        // 获取表单和提交按钮
        const $form = $('#formId');
        const $submitBtn = $('#submitBtn');
        // 初始绑定提交事件
        if ($form.length) {
            $form.on('submit', handleSubmit);
        }
        // 提交处理函数
        async function handleSubmit(event) {
            event.preventDefault(); // 阻止默认表单提交

            // 确保所有编辑器内容已同步到 textarea
            customEditor.editors.forEach((editor, index) => {
                editor.updateSourceElement();

                // 提取提及的用户
                const mentionedUsers = getMentionedUsers(editor);
                console.log('被提及的用户:', mentionedUsers);

                // 可以将这些用户ID添加到表单数据中
                const userIds = mentionedUsers.map(user => user.userId).join(',');
                document.getElementById('formId').querySelector('[name="mentioned_users"]').value = userIds;
            });

            // 解绑提交事件（避免重复提交）
            $form.off('submit', handleSubmit);
            // 禁用提交按钮（防止重复点击）
			const originalBtnText = $submitBtn.text();
            $submitBtn.prop('disabled', true).text('Submitting...');
            try {
                const formData = new FormData($form[0]);
                const response = await fetch($form.attr('action'), {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                });
                const data = await response.json();
                console.log(data)
                if (data.code === 1) {
                    // 提交成功
                    layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                        location.reload();
                    });
                } else {
                    // 提交失败
                    if(data.url && data.url !== '') {
                        layer.msg(data.msg, { icon: 2, time: 2000 }, () => {
                            window.location.href = data.url;
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                layer.msg("提交失败，请重试", { icon: 2 });
            } finally {
                // 无论成功或失败，重新绑定事件并恢复按钮状态
                $submitBtn.prop('disabled', false).text(originalBtnText);
                $form.on('submit', handleSubmit);
            }
        }
    </script>

</body>

</html>