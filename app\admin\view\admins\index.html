<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">管理员列表</a></p>

                <a href="{:url('add')}" class="add-button">添加管理员</a>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                    <tr class="mid_01">
                        <td class="mid_one"></td>
                        <td class="mid_t">用户名</td>
                        <td class="mid_one">角色</td>
                        <td class="mid_s">操作</td>
                    </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_t">{$vo.username}</td>
                            <td class="mid_one">{$vo.group_name}</td>
                            <td class="mid_s">
                                <a href="{:url('del', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit', ['id'=>$vo['id']])}" class="basic">修改</a>
                            </td>
                        </tr>

                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>
</body>
</html>