<?php /*a:3:{s:61:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\order\add.html";i:1754450126;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1752808589;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>创建订单</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;创建订单
                <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box order-info">
            <form action="<?php echo url('add'); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="quote_id" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['id']); ?>"<?php endif; ?> />

                <div class="cnt-basic">
                    <div class="cnt-title">
	                    订单基本信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>用户：</label>
                                <select name="user_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    <?php if(is_array($users) || $users instanceof \think\Collection || $users instanceof \think\Paginator): $i = 0; $__LIST__ = $users;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['email']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="email" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['email']); ?>"<?php endif; ?> />
                                <span class="must-input">*</span>
                            </div>
                            <div class="cnt-basic-i class_con">
                                <span class="input-tips"> 如果系统中不存在该Email账号，则自动创建用户账号，初始密码“opd123456”；如账号已存在，则自动归入该账号</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Country/Region：</label>
                                <select name="country" class="l_xiang">
                                    <option value="">
                                        Please select your country/region
                                    </option>
                                    <?php if(is_array($country) || $country instanceof \think\Collection || $country instanceof \think\Paginator): $i = 0; $__LIST__ = $country;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <option value="<?php echo htmlentities((string) $vo['en_name']); ?>" <?php if($quote && $quote['country']==$vo['en_name']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['en_name']); ?> <?php echo htmlentities((string) $vo['cn_name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="first_name" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['first_name']); ?>"<?php endif; ?> />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单金额：</label>
                                <input type="text" name="money" style="width: 300px;" />
                                <select name="money_unit" class="l_xiang" style="width: 72px;background: url(/static/admin/images/icon_04.png) 67px 12px no-repeat;margin-left: 4px;">
                                    <option value="USD">USD</option>
                                    <option value="RMB">RMB</option>
                                    <option value="EUR">EUR</option>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>产品：</label>
                                <select name="product_id" id="product" class="l_xiang">
                                    <option value="">请选择</option>
                                    <?php if(is_array($product) || $product instanceof \think\Collection || $product instanceof \think\Paginator): $i = 0; $__LIST__ = $product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($quote && $quote['product_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>项目号：</label>
                                <input type="text" name="project_no" value="" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>选择试用券：</label>
                                <select name="coupon_id" class="l_xiang" id="coupon_id">
                                    <option value="">请选择</option>
                                    <?php if(is_array($user_coupon) || $user_coupon instanceof \think\Collection || $user_coupon instanceof \think\Paginator): $i = 0; $__LIST__ = $user_coupon;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['name']); ?>% <?php echo htmlentities((string) $vo['description']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>Title：</label>
                                <input type="text" name="title" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['title']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="phone" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['phone']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Organization：</label>
                                <input type="text" name="organization" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['organization']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>City：</label>
                                <input type="text" name="city" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['city']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="last_name" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['last_name']); ?>"<?php endif; ?> />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单状态：</label>
                                <select name="order_status" class="l_xiang">
                                    <option value="0">进行中</option>
                                    <option value="1">已完成</option>
                                    <option value="2">已取消</option>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>服务：</label>
                                <select name="service_id" id="service" class="l_xiang">
                                    <option value="">请选择</option>
                                    <?php if($service): if(is_array($service) || $service instanceof \think\Collection || $service instanceof \think\Paginator): $i = 0; $__LIST__ = $service;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($quote && $quote['service_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>备注：</label>
                                <textarea name="remark"></textarea>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="cnt-basic">
                    <div class="cnt-title">
	                    邮寄信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>物流单号：</label>
                                <input type="text" name="tracking_no" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="express_first_name" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['first_name']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="express_email" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['email']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Shipping Address：</label>
                                <input type="text" name="express_address" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['express_address']); ?>"<?php endif; ?> />
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>物流公司：</label>
                                <input type="text" name="tracking_company" value="" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="express_last_name" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['last_name']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="express_phone" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['phone']); ?>"<?php endif; ?> />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Postcode：</label>
                                <input type="text" name="express_postcode" <?php if($quote): ?>value="<?php echo htmlentities((string) $quote['express_postcode']); ?>"<?php endif; ?> />
                            </div>
                        </div>
                    </div>

                    <div class="de_y">
                        <button class="de_y_l" type="submit" id="submitBtn">创建订单</button>
                        <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
<script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
<script src="/static/ckeditor5/ckeditor5.js"></script>

<script>
    var customEditor = new CKEditorManager();
    customEditor.initAll('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const $form = $('#formId');
    const $submitBtn = $('#submitBtn');
    // 初始绑定提交事件
    if ($form.length) {
        $form.on('submit', handleSubmit);
    }
    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 确保所有编辑器内容已同步到 textarea
        customEditor.editors.forEach((editor, index) => {
            editor.updateSourceElement();
        });

        // 解绑提交事件（避免重复提交）
        $form.off('submit', handleSubmit);
        // 禁用提交按钮（防止重复点击）
		const originalBtnText = $submitBtn.text();
        $submitBtn.prop('disabled', true).text('Submitting...');
        try {
            const formData = new FormData($form[0]);
            const response = await fetch($form.attr('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });
            const data = await response.json();
            console.log(data)
            if (data.code === 1) {
                // 提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                // 提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            $submitBtn.prop('disabled', false).text(originalBtnText);
            $form.on('submit', handleSubmit);
        }
    }
</script>

<script>
    //弹窗
    $(document).on('click', '.layer-trigger', function() {
        const $btn = $(this);
        const contentId = $btn.data('layer');
        const title = $btn.data('title');
        const width = $btn.data('width');
        const height = $btn.data('height');

        layer.open({
            type: 1,
            title: [title, 'font-size:18px; background-color: #0f2950; color: #fff;'],
            closeBtn: 1,
            shadeClose: true,
            area: [width, height],
            content: $("#" + contentId),
            resize: false,
            move: false
        });
    });

    //弹窗"提交"和"取消"事件
    // 表单初始化函数
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            location.reload();

                            // 优先使用保存的索引
                            // if(window.layerIndex !== undefined) {
                            //     layer.close(window.layerIndex);
                            // }
                            // // 其次尝试获取当前索引
                            // else if(layer.getFrameIndex && layer.getFrameIndex(window.name)) {
                            //     layer.close(layer.getFrameIndex(window.name));
                            // }
                            // // 最后全部关闭
                            // else {
                            //     layer.closeAll();
                            // }

                            // // 如果需要刷新
                            // if(response.reload) {
                            //     location.reload();
                            // }
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    layer.close(window.layerIndex);
                } else {
                    layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    $(document).ready(function() {
        // 遍历每一个多选框容器
        $(".multi-select-container").each(function() {
            const $container = $(this);
            const $select = $container.find(".user-multi-select");
            const $selectAllBtn = $container.find(".select-all-btn");
            const $deselectAllBtn = $container.find(".deselect-all-btn");
            const $countDisplay = $container.find(".count");

            // 初始化计数
            updateSelectedCount();

            // 全选
            $selectAllBtn.on("click", function() {
                $select.find("option").prop("selected", true);
                updateSelectedCount();
            });

            // 取消全选
            $deselectAllBtn.on("click", function() {
                $select.find("option").prop("selected", false);
                updateSelectedCount();
            });

            // 点击选项（无需按Ctrl）
            $select.on("mousedown", "option", function(e) {
                e.preventDefault();
                $(this).prop("selected", !$(this).prop("selected"));
                $select.trigger("change"); // 触发change事件更新计数
            });

            // 选择变化时更新计数
            $select.on("change", updateSelectedCount);

            // 更新已选项数量
            function updateSelectedCount() {
                const selectedCount = $select.find("option:selected").length;
                $countDisplay.text(selectedCount);
            }
        });
    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>

    <script>
        // 监听用户选择变化
        $('select[name="user_id"]').change(function() {
            var userId = $(this).val();
            if (userId) {
                // 发送AJAX请求获取用户信息
                $.ajax({
                    url: "/admin/Order/getUserData",
                    type: "POST",
                    data: {user_id: userId},
                    dataType: "json",
                    success: function(response) {
                        if (response.code == 1) {
                            // 填充表单字段
                            $('input[name="email"]').val(response.data.email);
                            $('input[name="first_name"]').val(response.data.first_name);
                            $('input[name="last_name"]').val(response.data.last_name);
                            $('input[name="title"]').val(response.data.title);
                            $('input[name="phone"]').val(response.data.phone);
                            $('input[name="organization"]').val(response.data.organization);
                            $('input[name="city"]').val(response.data.city);
                            $('input[name="express_address"]').val(response.data.express_address);
                            $('input[name="express_postcode"]').val(response.data.express_postcode);

                            $('input[name="express_email"]').val(response.data.email);
                            $('input[name="express_first_name"]').val(response.data.first_name);
                            $('input[name="express_last_name"]').val(response.data.last_name);
                            $('input[name="express_phone"]').val(response.data.phone);

                            $('select[name="country"]').val(response.data.country);

                            var $serviceSelect = $('#coupon_id');
                            $serviceSelect.empty(); // 清空现有选项
                            // 添加默认选项
                            $serviceSelect.append('<option value="" selected>请选择</option>');
                            // 动态添加 service 选项
                            $.each(response.data.user_coupon, function(index, service) {
                                $serviceSelect.append(
                                    '<option value="' + service.id + '">' + service.name + service.description + '</option>'
                                );
                            });
                            // 更新 UI 颜色（如果之前有样式逻辑）
                            $serviceSelect.css('color', '#333');
                        } else {
                            layer.msg(response.msg);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请重试');
                    }
                });
            } else {
                // 清空表单
                $('input[name="email"], input[name="country"], input[name="first_name"], input[name="last_name"], input[name="title"], input[name="phone"], input[name="express_address"], input[name="express_postcode"], input[name="organization"], select[name="country"], input[name="express_email"], input[name="express_first_name"], input[name="express_last_name"], input[name="express_phone"]').val('');
            }
        });

        // 监听 product 变化
        $('#product').on('change', function() {
            var productId = $(this).val();
            if (!productId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: '/getServices/' + productId,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        console.log(response)
                        var $serviceSelect = $('#service');
                        $serviceSelect.empty(); // 清空现有选项
                        // 添加默认选项
                        $serviceSelect.append('<option value="" selected>请选择</option>');
                        // 动态添加 service 选项
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        // 更新 UI 颜色（如果之前有样式逻辑）
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>