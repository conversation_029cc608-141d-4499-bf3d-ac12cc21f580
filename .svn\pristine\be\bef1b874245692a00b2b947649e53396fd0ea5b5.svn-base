<?php
declare (strict_types = 1);

namespace app\validate;

use think\Validate;

class Users extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'email'  => 'require|email|unique:user', // 检查 user 表的 email 字段是否唯一
        'captcha' => 'require',  // 验证码必填
        'password' => 'require|checkPassword',  // 密码必填 + 自定义校验（如长度、复杂度）
        'password_confirm' => 'require|confirm:password', // 确保两次输入一致
        'country' => 'require',  // 国家必选
        'first_name' => 'require|min:2|max:20',  // 名字必填，长度 2-20
        'last_name' => 'require|min:2|max:10',  // 姓氏必填，长度 2-10
        'phone'      => 'require|checkPhone', // 手机号必填 + 自定义国际手机号校验
        'organization' => 'require',  // 组织/公司必填
        'new_password' => 'checkPassword',  // 修改密码时，非空则校验
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'email.require' => 'Please fill in the email information',
        'email.email'     => 'Please enter a valid email address',
        'email.unique'     => 'Email already exists',
        'captcha.require' => 'Please enter email verification code',
        'password.require'   => 'Password must',
        'password.checkPassword'   => '8-16 characters must contain both digits and letters',
        'password_confirm.confirm' => 'The passwords entered twice do not match.',
        'country.require'   => 'Country must',
        'first_name.require'   => 'First Name must',
        'first_name.min'   => 'First Name at least 2 characters',
        'first_name.max'   => 'First Name no more than 20 characters',
        'last_name.require'   => 'Last Name must',
        'last_name.min'   => 'Last Name at least 2 characters',
        'last_name.max'   => 'Last Name no more than 10 characters',
        'phone.require'   => 'Phone must',
        'phone.checkPhone'   => 'invalid phone',
        'organization.require'   => 'Organization must',
        'new_password.checkPassword'   => '8-16 characters must contain both digits and letters',
    ];

    protected $scene = [
        'front-register'  =>  ['email', 'captcha', 'password', 'password_confirm', 'country', 'first_name', 'last_name', 'organization'],
        'front-login' => [
            'email' => 'require|email', // 只保留 require 和 email 规则
            'captcha',
            'password' => 'require',
        ],
        'admin-login' => [
            'email' => 'require|email', // 只保留 require 和 email 规则
            'captcha',
            'password' => 'require',
        ],
        'front-edit'  =>  ['country', 'first_name', 'last_name', 'phone', 'organization'],
        'front-editpassword'  =>  ['captcha', 'password', 'password_confirm'],
        'front-forgot'  =>  ['email' => 'require|email', 'captcha', 'password', 'password_confirm'],
        'back-add'  =>  ['email', 'password', 'country', 'first_name', 'last_name', 'organization'],
        'back-edit' => [
            'email' => 'require|email',
            'country',
            'first_name',
            'first_name',
            'last_name',
            'organization',
            'new_password'
        ],
        'back-editpassword'  =>  ['password', 'password_confirm'],
    ];

    // 自定义验证方法
    protected function checkPassword($value, $rule, $data=[])
    {
        if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d).{8,16}$/', $value)) {
            return '8-16 characters must contain both digits and letters';
        }
        return true;
    }

    // 自定义规则：支持中国手机号、国际手机号、座机号
    public function checkPhone($value, $rule, $data)
    {
        // 1. 中国手机号（11位，1开头）
        $chinaMobile = '/^1[3-9]\d{9}$/';

        // 2. 国际手机号（+国家码-号码，如 +86-13800138000 或 ******-555-1234）
        $internationalMobile = '/^\+\d{1,4}-?\d{6,15}$/';

        // 3. 座机号（带区号，如 010-12345678 或 021 1234 5678）
        $landline = '/^(0\d{2,3})-?\d{7,8}$/';

        if(preg_match($chinaMobile, $value) || preg_match($internationalMobile, $value) || preg_match($landline, $value)) {
            return true;
        }

        return "invalid phone";
    }

    // 动态设置场景规则
    public function sceneBackEdit($id = null)
    {
        $rules = $this->scene['back-edit'];

        // 替换email规则
        $rules['email'] = 'require|email|unique:user,email,'.$id;

        return $rules;
    }
}
