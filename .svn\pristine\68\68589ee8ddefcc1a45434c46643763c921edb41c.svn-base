<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">兑换记录</a></p>

                <div class="search-container">
                    <form method="get" action="{:url('exchange')}">
                        <input
                            type="text"
                            name="keyword"
                            placeholder="请输入邮箱或姓名"
                            value="{$params.keyword ?? ''}"
                            class="search-input"
                        >
                        <button type="submit" class="search-button">搜索</button>
                    </form>
                </div>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">用户</td>
                            <td class="mid_t">优惠券</td>
                            <td class="mid_one">开始时间</td>
                            <td class="mid_one">结束时间</td>
                            <td class="mid_one">是否使用</td>
                            <td class="mid_one">兑换时间</td>
                            <td class="mid_one">使用时间</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one">{$vo.email}</td>
                            <td class="mid_t">{$vo.description} {$vo.name}%</td>
                            <td class="mid_one">{$vo.start_time}</td>
                            <td class="mid_one">{$vo.end_time}</td>
                            <td class="mid_one">
                                {if $vo.is_used==0}
                                <span style="color:#f08411">未使用</span>
                                {else }
                                <span style="color:#155797">已使用</span>
                                {/if}
                            </td>
                            <td class="mid_one">{$vo.create_time}</td>
                            <td class="mid_one">{$vo.update_time}</td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>