<?php /*a:1:{s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\index\index.html";i:1745989405;}*/ ?>
<html>
<head>
	<title>后台管理中心</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta http-equiv="Content-Language" content="zh-CN">

	<link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
    <link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

	<script>
        // 提供给子框架调用的方法：返回主窗口的 CSS 链接
        function getParentCSS() {
            const links = document.querySelectorAll('link[rel="stylesheet"]');
            return Array.from(links).map(link => link.outerHTML).join('');
        }
    </script>
</head>

	<frameset rows="54,*" cols="*" frameborder="no" border="0" framespacing="0">
	<frame name="headerframe" noresize scrolling="no" src="/admin/Publics/header">
	<frameset cols="200,*" rows="*" frameborder="no" border="0" framespacing="0">
		<frame name="menuframe" noresize scrolling="yes" src="/admin/Publics/menu">
		<frame name="mainframe" noresize scrolling="yes" src="/admin/Publics/main">
	</frameset>
	<noframes>
		<body>
			<font color=red style=font-size:14px><b>你的浏览器不支持帧页面，请升级！</b></font>
		</body>
	</noframes>
</html>
