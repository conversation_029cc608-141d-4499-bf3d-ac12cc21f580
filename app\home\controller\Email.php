<?php
namespace app\home\controller;

use app\home\controller\Common;

use app\services\MailService;
use think\facade\Validate;
use think\facade\Cache;
use think\facade\Db;

class Email extends Common
{
    // 验证码有效期（分钟）
    const CAPTCHA_EXPIRE = 15;

    //测试优惠券邮件发送
    public function test(){
        exit;
        $email = "<EMAIL>";
        $captcha = mt_rand(10000000, 99999999);
        $data = [
            'captcha' => $captcha,
            'expire' => self::CAPTCHA_EXPIRE,
            'username' => strstr($email, '@', true)
        ];

        $result = MailService::sendEmail("send-captcha", $email, $data);
        var_dump($result);
        exit;

        $data = [
            "system_name" => config('app.system_name'),
            "username" => strstr($email, '@', true),
            "order_no" => "202506161507101560",
            "order_date" => date("Y-m-d"),
            "money" => 10000,
            "login_link" => config('app.site_url')."login",
        ];

        $result = MailService::sendEmail("order-notification", $email, $data);
        var_dump($result);
    }

    //检测密码
    public function passw()
    {
        $preg = preg_match('/^(?=.*[A-Za-z])(?=.*\d).{8,16}$/', "7ujm*IK<");
        var_dump($preg);
        exit;
    }

}