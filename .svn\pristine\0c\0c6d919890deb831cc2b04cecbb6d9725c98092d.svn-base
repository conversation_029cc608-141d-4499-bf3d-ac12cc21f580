<?php

function addstr($str)
{
    $k = mb_strlen($str, 'utf-8');
    $new = '';
    for ($i = 0; $i < $k; $i++) {
        $new .= mb_substr($str, $i, 1, 'utf-8') . '@@@';
    }
    return $new;
}

function getFileType($file_name)
{
    $file_type_arr = $file_name ? substr($file_name, strrpos($file_name, '.')) : '';
    switch ($file_type_arr) {
        case '.rtf':
        case '.docx':
        case '.doc':
            $file_type = 'word';
            break;
        case '.xls':
        case '.xlsx':
            $file_type = 'excel';
            break;
        case '.rar':
        case '.zip':
            $file_type = 'zip';
            break;
        case '.ppt':
        case '.pdf':
        case '.pptx':
            $file_type = 'ppt';
            break;
        case '.png':
        case '.PNG':
        case '.jpg':
        case '.JPG':
        case '.gif':
        case '.GIF':
        case '.JPEG':
        case '.jpeg':
            $file_type = 'image';
            break;
        case '.txt':
            $file_type = 'txt';
            break;
        default:
            $file_type = '';
    }

    return $file_type;
}

/**
 *
 * @param string $value 要加密的值
 * @param int $type     0加密  1解密
 */
function encryption($value, $type = 0)
{
    $key = md5("ecomcrmiY6*nl98");
    if ($type == 0) {
        $value = $key ^ $value;
        $value = str_replace('=', '', base64_encode($value));
        return $value;
    } else if ($type == 1) {
        $value = base64_decode($value);
        return $value ^ $key;
    }
}


/**
 * @function chkstr()
 * @description: 验证字符串
 * @param $str 字符串
 * @param $reg 正则别名
 * @return boolean
 */
function chkstr($str, $type = 'int')
{
    $regex = array(
        'license' => '/^[\x{4e00}-\x{9fa5}]{1}[A-Z]{1}[A-Z0-9]{5}$/u',   //车牌
        'code' => '/^[1-9a-zA-z]+$/',   //验证码
        'seccode' => '/^[0-9]+$/',   //手机验证码
        'hash' => '/^[a-zA-Z0-9]+$/', //校验码
        'date' => '/^[1-2]{1}\d{3}-(([0]{1}[1-9]{1})|([1]{1}[0-2]{1}))-(([0]{1}[1-9]{1})|([1-2]{1}[0-9]{1}|[3]{1}[0-1]{1}))$/', //日期
        'int' => '/^[1-9]{1}[0-9]+$/', //不可以0开头的数字
        'num' => '/^[0-9]+$/', //可以0开头
        'tel' => '/^(\d{3})-(\d{8})|(\d{4})-(\d{7})|(\d{4}|\d{3})-(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1})|(\d{7,8})-(\d{4}|\d{3}|\d{2}|\d{1})$/', //座机
        'mobile' => '/^[1]{1}(3[0-9]|5[0-9]|8[0-9]|47|7[0-9])\d{8}$/', //手机
        'h:m' => '/^[0-2]{1}[0-9]{1}:[0-5]{1}[0-9]{1}$/', //时:分
        'email' => '/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/', //邮箱
        'username' => '/^[a-zA-Z]{1}{a-zA-Z0-9_\.}{5,15}$/', //用户名;
    );
    if (!isset($regex[$type])) return false;
    if (!preg_match($regex[$type], $str)) return false;
    return true;
}

//返回UUID,去掉杠后的UUID
function returnUUID()
{
    return  str_replace("-", "", md5(uniqid(mt_rand(), true)));
}

function seoFriendlyUrl($string) {
    // 转换为小写
    $string = mb_strtolower($string, 'UTF-8');

    // 替换空格和非字母数字字符为连字符
    $string = preg_replace('/[^\p{L}\p{N}]+/u', '-', $string);

    // 去除首尾的连字符
    $string = trim($string, '-');

    return $string;
}