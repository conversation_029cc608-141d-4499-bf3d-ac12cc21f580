.btn {
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	background-color: #ff8400;
	color: #fff;
	display: inline-block;
	height: 28px;
	line-height: 28px;
	text-align: center;
	width: 72px;
	transition: background-color 0.2s linear 0s;
	border: none;
	cursor: pointer;
	margin: 0 0 20px;
}

.demo {
	width: 700px;
	margin: 0 auto
}

.btn:hover {
	background-color: #e95a00;
	text-decoration: none
}

.ul_pics li {
	float: left;
	width: 160px;
	height: 160px;
	border: 1px solid #ddd;
	padding: 2px;
	text-align: center;
	margin: 0 5px 5px 0;
}

.ul_pics li .img {
	width: 160px;
	height: 140px;
	display: table-cell;
	vertical-align: middle;
}

.ul_pics li img {
	max-width: 160px;
	max-height: 140px;
	vertical-align: middle;
}

.progress {
	position: relative;
	padding: 1px;
	border-radius: 3px;
	margin: 60px 0 0 0;
}

.bar {
	background-color: green;
	display: block;
	width: 0%;
	height: 20px;
	border-radius: 3px;
}

.percent {
	position: absolute;
	height: 20px;
	display: inline-block;
	top: 3px;
	left: 2%;
	color: #fff
}