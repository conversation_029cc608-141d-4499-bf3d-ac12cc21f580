<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;

use think\facade\Db;
use think\facade\Filesystem;

class CleanOrderFiles extends Command
{
    protected function configure()
    {
        $this->setName('clean:order_files')
             ->setDescription('Clean up order files older than 7 days');
    }

    protected function execute(Input $input, Output $output)
    {
        // 计算7天前的日期
        $sevenDaysAgo = date('Y-m-d H:i:s', strtotime('-7 days'));

        // 查询7天前的记录
        $files = Db::name('Order_file')
            ->where('update_time', '<', $sevenDaysAgo)
            ->select();

        $deletedCount = 0;
        $errorCount = 0;
        foreach ($files as $file) {
            try {
                // 删除实体文件
                if (!empty($file['file'])) {
                    $filePath = public_path() . ltrim($file['file'], '/');
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }

                // 删除数据库记录
                Db::name('Order_file')
                    ->where('id', $file['id'])
                    ->delete();

                $deletedCount++;
            } catch (\Exception $e) {
                $errorCount++;
                // 记录错误日志
                $output->writeln("Error deleting file ID {$file['id']}: " . $e->getMessage());
            }
        }

        $output->writeln("Deleted {$deletedCount} files. Errors: {$errorCount}");
    }
}