<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改轮播图
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>图片：</label>
                    <input type="file" name="image" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽1660px 高560px。格式：png、jpg、jpeg）
                </div>
                {if condition="$getone.image"}
                <div class="class_con">
                    <img src="{$getone.image}">
                </div>
                {/if}

                <div class="class_con">
                    <label>手机端图片：</label>
                    <input type="file" name="smt_image" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽690px 高400px。格式：png、jpg、jpeg）
                </div>
                {if condition="$getone.smt_image"}
                <div class="class_con">
                    <img src="{$getone.smt_image}">
                </div>
                {/if}

                <div class="class_con">
                    <label>排序：</label>
                    <input type="text" name="sort" value="{$getone.sort}" />
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>