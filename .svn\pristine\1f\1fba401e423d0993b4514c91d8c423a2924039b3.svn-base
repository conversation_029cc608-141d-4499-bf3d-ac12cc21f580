<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.css">
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改分类
                <a href="{:url('category')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit_category')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>分类名称：</label>
                    <input type="text" name="name" value="{$getone.name}" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>字体颜色：</label>
                    <input type="color" id="color" name="color" value="{$getone.color}">
                </div>

                <div class="class_con">
                    <label>背景颜色：</label>
                    <input type="text" id="backgroud_color" name="backgroud_color" value="{$getone.backgroud_color}">
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('category')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.js"></script>
    <script>
        $("#color").spectrum({
            preferredFormat: "hex",
            showInput: true // 显示输入框以便查看HEX值
        });

        // $("#backgroud_color").spectrum({
        //     preferredFormat: "hex", // 颜色格式
        // });

        $("#backgroud_color").spectrum({
            showAlpha: true,
            preferredFormat: "rgb",
            showInput: true,       // 显示输入框方便查看
            allowEmpty: false,     // 不允许空值（可选）

            // 颜色变化时（包括手动输入、拖动、选择）
            change: function(color) {
                // 强制更新 input.value 为 RGB 格式
                $(this).val(color ? color.toRgbString() : "rgb(0, 0, 0)");
            },

            // 拖动颜色时实时更新
            move: function(color) {
                $(this).val(color.toRgbString());
            },

            // 关闭颜色选择器时确保值正确
            hide: function(color) {
                $(this).val(color ? color.toRgbString() : "rgb(0, 0, 0)");
            },
        });
    </script>

</body>
</html>