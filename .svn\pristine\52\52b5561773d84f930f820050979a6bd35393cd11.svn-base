<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加新闻
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <div class="class_con">
                    <label>分类：</label>
                    <select name="category_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="category" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
                </div>

                <div class="class_con">
                    <label>标题：</label>
                    <input type="text" name="title" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>SEO链接：</label>
                    <input type="text" name="seo_url" />
                </div>

                <div class="class_con editor-container">
                    <label>内容：</label>
                    <textarea name="content" class="tiny-editor" style="height: 300px;"></textarea>
                </div>

                <div class="class_con">
                    <label>图片：</label>
                    <input type="file" name="image" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽800px 高500px。格式：png、jpg、jpeg）
                </div>

                <div class="class_con">
                    <label>发布日期：</label>
                    <input type="text" name="publish_date" id="publish_date" value="{:date('Y-m-d')}" />
                </div>

                <div class="class_con">
                    <label>Topic：</label>
                    <select name="topic" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="topic" id="vo"}
                            <option value="{$vo.name}">{$vo.name}</option>
                        {/volist}
                    </select>
                </div>

                <div class="class_con">
                    <label>Tags：</label>
                    <input type="hidden" name="tags" id="tags-input" value="">
                    <div id="multiselect"></div>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script src="__STATIC__/home/<USER>/MultiSelect.js"></script>

    <script>
        const options = {$tagsOptions|raw};
        const multi = new MultiSelect({
            container: '#multiselect',
            options,
            max: 8, // 最多能选n个
            defaultSelected: [] // 这里设置默认选中
        });

        // 覆盖原方法，直接更新 input
        multi.renderTags = function() {
            MultiSelect.prototype.renderTags.call(this); // 调用父类方法
            MultiSelect.prototype.renderDropdown.call(this); // 调用父类方法
            document.getElementById('tags-input').value = this.selected.join(',');
        };
    </script>

    <script>
        //结束日期
        laydate.render({
            elem: '#publish_date'
        });
    </script>

</body>
</html>