<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Order - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)]">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                {include file="user:left"}

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div
                        class="grid grid-cols-2 text-sm md:text-[1.5625rem] md:px-[1.875rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link text-center">
                        <span data-tab="order-list" class="tab-item">
                            Order List
                        </span>
                        <span data-tab="purchase-history" class="tab-item">
                            Purchase History
                        </span>
                    </div>
                    <div class="user-profile">
                        <div data-tab="order-list" class="profile-item">
                            <div class="p-4 md:p-[1.875rem]">
                                <!-- 切换 -->
                                <div class="order-tab flex gap-2 text-sm border-b border-[#dae9ff] mb-3 md:text-xl md:mb-[1.5625rem]">
                                    <div data-tab="Progress" class="order-tab-item">
                                        In Progress
                                    </div>
                                    <div data-tab="Completed" class="order-tab-item">
                                        Completed
                                    </div>
                                    <div data-tab="Cancelled" class="order-tab-item">
                                        Cancelled
                                    </div>
                                </div>

                                <!-- 切换容器 -->
                                <div class="order-list">
                                    <!--进行中-->
                                    <div data-tab="Progress" class="order-list-item">
                                        <div class="order-search mb-3 md:mb-[1.875rem]">
                                            <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                                <input type="text"
                                                    class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                                    placeholder="Product Title/Item Title">
                                                <button type="submit"
                                                    class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                                    id="OrderList_btn">
                                                    <img src="__IMG__/icons/sousuo.png" class="md:w-full" alt="" />
                                                </button>
                                            </div>
                                        </div>
                                        <div class="order-container">

                                                {volist name="orders_progress" id="vo"}
                                                <div class="order-list-item-container">
                                                    <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                        <div class="order-list-item-header-left md:flex md:items-center">
                                                            <div class="mb-1 md:mb-0 md:mr-5">
                                                                <span>{:date('j F Y', strtotime($vo.create_time))} </span>
                                                            </div>
                                                            {if $vo.tracking_no}
                                                            <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                                                <span>Tracking number:
                                                                    <strong class="copy-text">{$vo.tracking_no}</strong>
                                                                </span>
                                                                <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                                                    <img src="__IMG__/icons/fuzhi.png"
                                                                        class="object-cover w-[1.0625rem] h-[1.0625rem]"
                                                                        alt="" />
                                                                </span>
                                                            </div>
                                                            {/if}
                                                        </div>
                                                        <div class="flex gap-x-1.5 items-center md:gap-x-5">
                                                            <a href="/user/order-detail-{$vo.id}.html" class="order-list-item-header-right-btn underline text-[#155797]">
                                                                <span>Order Details </span>
                                                            </a>
                                                            <img src="__IMG__/icons/xiala-.png" alt="">
                                                        </div>
                                                    </div>
                                                    <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                        <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                            <img src="{$vo.product.image_detail}" alt="{$vo.product.name}" class="max-h-2/3 max-w-2/3 object-cover" />
                                                        </div>
                                                        <div class="order-right-text md:w-full">
                                                            <div class="order-right-text-item">
                                                                <div class="text-base item-left-text-name">
                                                                    <a href="/product/{$vo.product.seo_url}">
                                                                        {$vo.product.name}
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    {$vo.product.product_progress}
                                                                </div>
                                                            </div>

                                                            {notempty name="$vo.service_current"}
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/{$vo.service_current.seo_url}">
                                                                        {$vo.service_current.name}
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    {$vo.service_current.service_progress}
                                                                </div>
                                                            </div>
                                                            {/notempty}

                                                            {volist name="vo.service_son" id="s"}
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/{$s.seo_url}">
                                                                        {$s.name}
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    {$s.service_progress}
                                                                </div>
                                                            </div>
                                                            {/volist}
                                                        </div>
                                                    </div>
                                                </div>
                                                {/volist}
                                                <div class="empty-state py-10 text-center no-results"style="display: none;">No orders in progress</div>
                                        </div>
                                    </div>

                                    <!--已完成-->
                                    <div data-tab="Completed" class="order-list-item">
                                        <div class="order-search mb-3 md:mb-[1.875rem]">
                                            <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                                <input type="text"
                                                    class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                                    placeholder="Product Title/Item Title">
                                                <button type="submit"
                                                    class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                                    id="OrderList_btn">
                                                    <img src="__IMG__/icons/sousuo.png" class="md:w-full" alt="" />
                                                </button>
                                            </div>
                                        </div>
                                        <div class="order-container">
                                                {volist name="orders_completed" id="vo"}
                                                <div class="order-list-item-container">
                                                    <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                        <div class="order-list-item-header-left md:flex md:items-center">
                                                            <div class="mb-1 md:mb-0 md:mr-5">
                                                                <span>{:date('j F Y', strtotime($vo.create_time))}</span>
                                                            </div>
                                                            {if $vo.tracking_no}
                                                            <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                                                <span>Tracking number:
                                                                    <strong class="copy-text">{$vo.tracking_no}</strong>
                                                                </span>
                                                                <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                                                    <img src="__IMG__/icons/fuzhi.png"
                                                                        class="object-cover w-[1.0625rem] h-[1.0625rem]"
                                                                        alt="" />
                                                                </span>
                                                            </div>
                                                            {/if}
                                                        </div>
                                                        <div class="flex gap-x-1.5 items-center md:gap-x-5">
                                                            <a href="/user/order-detail-{$vo.id}.html" class="order-list-item-header-right-btn underline text-[#155797]">
                                                                <span>Order Details </span>
                                                            </a>
                                                            <img src="__IMG__/icons/xiala-.png" alt="">
                                                        </div>
                                                    </div>
                                                    <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                        <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                            <img src="{$vo.product.image_detail}" alt="{$vo.product.name}" class="max-h-2/3 max-w-2/3 object-cover" />
                                                        </div>
                                                        <div class="order-right-text md:w-full">
                                                            <div class="order-right-text-item">
                                                                <div class="text-base item-left-text-name">
                                                                    <a href="/product/{$vo.product.seo_url}">
                                                                        {$vo.product.name}
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    {$vo.product.product_progress}
                                                                </div>
                                                            </div>

                                                            {notempty name="$vo.service_current"}
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/{$vo.service_current.seo_url}">
                                                                        {$vo.service_current.name}
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    {$vo.service_current.service_progress}
                                                                </div>
                                                            </div>
                                                            {/notempty}

                                                            {volist name="vo.service_son" id="s"}
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/{$s.seo_url}">
                                                                        {$s.name}
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    {$s.service_progress}
                                                                </div>
                                                            </div>
                                                            {/volist}
                                                        </div>
                                                    </div>
                                                </div>
                                                {/volist}
                                                <div class="empty-state py-10 text-center no-results"style="display: none;">No orders in progress</div>
                                        </div>
                                    </div>

                                    <!--已取消-->
                                    <div data-tab="Cancelled" class="order-list-item">
                                        <div class="order-search mb-3 md:mb-[1.875rem]">
                                            <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                                <input type="text"
                                                    class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                                    placeholder="Product Title/Item Title">
                                                <button type="submit"
                                                    class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                                    id="OrderList_btn">
                                                    <img src="__IMG__/icons/sousuo.png" class="md:w-full" alt="" />
                                                </button>
                                            </div>
                                        </div>
                                        <div class="order-container">
                                                {volist name="orders_cancelled" id="vo"}
                                                <div class="order-list-item-container">
                                                    <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                        <div class="order-list-item-header-left md:flex md:items-center">
                                                            <div>
                                                                <span>{:date('j F Y', strtotime($vo.create_time))}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                        <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                            <img src="{$vo.product.image_detail}" alt="{$vo.product.name}" class="max-h-2/3 max-w-2/3 object-cover" />
                                                        </div>
                                                        <div class="order-right-text md:w-full">
                                                            <div class="order-right-text-item">
                                                                <div class="text-base item-left-text-name">
                                                                    <a href="/product/{$vo.product.seo_url}">
                                                                        {$vo.product.name}
                                                                    </a>
                                                                </div>
                                                                <div class="text-[#f08411] item-status">
                                                                    Cancelled
                                                                </div>
                                                            </div>

                                                            {notempty name="$vo.service_current"}
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/{$vo.service_current.seo_url}">
                                                                        {$vo.service_current.service_progress}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            {/notempty}

                                                            {volist name="vo.service_son" id="s"}
                                                            <div class="order-right-text-item">
                                                                <div class="text-[#155797] text-sm item-left-text-name">
                                                                    <a href="/service/{$s.seo_url}">
                                                                        {$s.name}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            {/volist}
                                                        </div>
                                                    </div>
                                                </div>
                                                {/volist}
                                                <div class="empty-state py-10 text-center no-results"style="display: none;">No orders in progress</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--购买历史-->
                        <div data-tab="purchase-history" class="profile-item">
                            <div class="p-4 md:p-[1.875rem]">
                                <div class="order-search mb-3 md:mb-[1.875rem]">
                                    <div class="search-bar flex items-center relative md:w-[35.625rem]">
                                        <input type="text"
                                            class="w-full px-3 py-2 rounded-lg border border-[var(--border-color)] pr-10 text-sm md:h-[3.125rem] md:text-xl md:px-[1.25rem] md:border-2xl order-search-input"
                                            placeholder="Product Title/Item Title">
                                        <button type="submit"
                                            class="w-[1rem] absolute top-1/2 -translate-1/2 right-2 z-10 md:w-[1.5625rem] md:h-[1.5625rem] cursor-pointer"
                                            id="PurchaseHistory_btn">
                                            <img src="__IMG__/icons/sousuo.png" class="md:w-full" alt="" />
                                        </button>
                                    </div>
                                </div>
                                <div class="order-container">
                                    {volist name="orders_completed" id="vo"}
                                        <div class="order-list-item-container">
                                            <div class="order-list-item-header flex text-xs bg-[#fafbff] flex-col px-3 py-2 rounded-tl-xl rounded-tr-xl border border-[#e0eaff] mb-2.5 md:text-base md:flex-row md:px-[1.25rem] md:py-2.5 md:justify-between md:mb-5">
                                                <div class="order-list-item-header-left md:flex md:items-center">
                                                    <div class="mb-1 md:mb-0 md:mr-5">
                                                        <span>{:date('j F Y', strtotime($vo.create_time))}</span>
                                                    </div>
                                                    {if $vo.tracking_no}
                                                    <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                                        <span>Tracking number:
                                                            <strong class="copy-text">{$vo.tracking_no}</strong>
                                                        </span>
                                                        <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                                            <img src="__IMG__/icons/fuzhi.png" class="object-cover w-[1.0625rem] h-[1.0625rem]" alt="" />
                                                        </span>
                                                    </div>
                                                    {/if}
                                                </div>
                                                <div class="flex gap-x-1.5 items-center md:gap-x-5">
                                                    <a href="/user/order-detail-{$vo.id}.html"
                                                        class="order-list-item-header-right-btn underline text-[#155797]">
                                                        <span>Order Details </span>
                                                    </a>
                                                    <img src="__IMG__/icons/xiala-.png" alt="">
                                                </div>
                                            </div>
                                            <div class="order-list-item-container-item flex flex-col md:flex-row">
                                                <div class="order-left-img bg-[#fafbff] border border-[#dae9ff] rounded-lg flex items-center justify-center mb-2 p-3 md:flex-shrink-0 md:w-[9.375rem] md:h-[9.375rem] md:rounded-xl md:mr-[2.1875rem]">
                                                    <img src="{$vo.product.image_detail}" alt="{$vo.product.name}" class="max-h-2/3 max-w-2/3 object-cover" />
                                                </div>
                                                <div class="order-right-text md:w-full">
                                                    <div class="order-right-text-item">
                                                        <div class="text-base item-left-text-name">
                                                            <a href="/product/{$vo.product.seo_url}">
                                                                {$vo.product.name}
                                                            </a>
                                                        </div>
                                                        <div class="text-[#999999] item-status">
                                                            {$vo.product.product_progress}
                                                        </div>
                                                    </div>
                                                    {notempty name="service_current"}
                                                    <div class="order-right-text-item">
                                                        <div class="text-[#155797] text-sm item-left-text-name">
                                                            <a href="/service/{$vo.service_current.seo_url}">
                                                                {$vo.service_current.name}
                                                            </a>
                                                        </div>
                                                        <div class="text-[#999999] item-status">
                                                            {$vo.service_current.service_progress}
                                                        </div>
                                                    </div>
                                                    {/notempty}
                                                    {volist name="vo.service_son" id="s"}
                                                    <div class="order-right-text-item">
                                                        <div class="text-[#155797] text-sm item-left-text-name">
                                                            <a href="/service/{$s.seo_url}">
                                                                {$s.name}
                                                            </a>
                                                        </div>
                                                        <div class="text-[#999999] item-status">
                                                            {$s.service_progress}
                                                        </div>
                                                    </div>
                                                    {/volist}
                                                </div>
                                            </div>
                                        </div>
                                    {/volist}
                                    <div class="empty-state py-10 text-center no-results" style="display: none;">No orders in progress</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}
    <script src="__JS__/TabSwitch.js"></script>
    <script src="__JS__/filterOrders.js"></script>
    <script>

        function copyText(buttonElement) {
            // 获取最近的copy-text元素
            const strongElement = buttonElement.closest('.flex').querySelector('strong.copy-text');

            if (!strongElement) {
                console.error('找不到要复制的文本元素');
                return;
            }

            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = strongElement.textContent;
            document.body.appendChild(textarea);

            // 选中并复制文本
            textarea.select();
            document.execCommand('copy');

            // 移除临时元素
            document.body.removeChild(textarea);

            // 可选：显示复制成功的提示
            layer.msg('Copy successfully ', {
                time: 2000,
                icon: 1
            });
        }

        initTabSwitch('.tab-item', '.profile-item');
        initTabSwitch('.order-tab-item', '.order-list-item');


    </script>
       <script>
     // 初始化
    document.addEventListener('DOMContentLoaded', () => {
        // 订单状态搜索：
        initOrderSearch({
            containerSelector: '.order-list-item',
            inputSelector: '.order-search-input',
            itemSelector: '.order-list-item-container',
            debounceTime: 500 // 防抖时间调整为500ms
        });
        // 历史记录搜素
        initOrderSearch({
            containerSelector: '.profile-item',
            inputSelector: '.order-search-input',
            itemSelector: '.order-list-item-container',
            debounceTime: 500 // 防抖时间调整为500ms
        });
    });
   </script>
</body>

</html>