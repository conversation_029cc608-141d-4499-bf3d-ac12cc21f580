<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\BaseController;
use Exception;
use think\facade\Filesystem;
use think\exception\ValidateException;
use think\file\UploadedFile;

class Common extends BaseController
{

    public function initialize()
	{
        if(session('adminId')){
            //登录才执行以下操作
            /*
            $module = app("http")->getName();
            $controller = request()->controller();
            $action = request()->action();

            $auth = new Auth();
            if(!$auth->check($module . '/' . $controller . '/' . $action, session('adminId'))){
                return $this->error('你没有权限访问');
            }
            */
        } else {
            $this->redirect('/admin/login');
        }
    }

    public function upload($file)
    {
        if ($file) {
            // 验证文件
            try {
                validate(['file' => [
                    'fileSize' => 5242880,  //5M
                    'fileExt' => 'jpg,gif,png,jpeg,webp,svg'
                ]])->check(['file' => $file]);
            } catch (ValidateException $e) {
                $this->error($e->getMessage());
            }

            $path = Filesystem::disk('public')->putFile("", $file);
            //拼接URL路径,结果是 $picPath = /storage/20200825/***.jpg
            $url = Filesystem::getDiskConfig('public', 'url') . '/' . str_replace('\\', '/', $path);

            return $url;
        } else {
            $this->error('No file uploaded');
        }
    }

    public function uploadFile($file, $dir="")
    {
        if ($file) {
            // 验证文件
            try {
                validate(['file' => [
                    'fileSize' => 30 * 1024 * 1024, // 30M
                    'fileExt'  => 'pdf,doc,docx,ppt,pptx,xls,csv,xlsx'
                ]])->check(['file' => $file]);
            } catch (ValidateException $e) {
                $this->error($e->getMessage());
            }

            // 获取原始文件名（带扩展名）
            $originalName = $file->getOriginalName();

            // 存储文件，获取格式化后的路径
            $path = Filesystem::disk('public')->putFile($dir, $file);

            // 拼接URL路径，如 /storage/20200825/***.pdf
            $url = Filesystem::getDiskConfig('public', 'url') . '/' . str_replace('\\', '/', $path);

            // 返回结果（包含原始文件名和存储路径）
            return [
                'original_name' => $originalName, // 原始文件名（如 "用户手册.pdf"）
                'storage_path'  => $path,         // 存储路径（如 "resource/20200825/abc.pdf"）
                'url'           => $url,          // 完整URL（如 "/storage/resource/20200825/abc.pdf"）
            ];
        } else {
            $this->error('No file uploaded');
        }
    }


    // 上传图片
    public function tinymceImage()
    {
        $file = request()->file('file');
        if (!$file) {
            return json([
                'status' => 'error',
                'message' => 'No file uploaded'
            ]);
        }

        try {
            validate(['file' => [
                'fileSize' => 1024 * 1024 * 5, // 5MB
                'fileExt' => 'jpg,gif,png,jpeg,webp,svg'
            ]])->check(['file' => $file]);
        } catch (ValidateException $e) {
            return json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }

        // 保存文件到public/tinymce目录
        $saveName = Filesystem::disk('tinymce')->putFile('', $file);
        // 返回完整的URL路径
        $url = Filesystem::getDiskConfig('tinymce', 'url') . '/' . str_replace('\\', '/', $saveName);

        return json([
            'location' => $url
        ]);
    }

    // 上传视频/媒体文件
    public function tinymceMedia()
    {
        $file = request()->file('file');
        if (!$file) {
            return json([
                'status' => 'error',
                'message' => 'No file uploaded'
            ]);
        }

        try {
            validate(['file' => [
                'fileSize' => 1024 * 1024 * 50, // 50MB
                'fileExt'  => 'mp4,webm,ogg',
                'fileMime' => 'video/mp4,video/webm,video/ogg'
            ]])->check(['file' => $file]);
        } catch (ValidateException $e) {
            return json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }

        // 保存文件到public/tinymce目录
        $saveName = Filesystem::disk('tinymce')->putFile('', $file);
        // 返回完整的URL路径
        $url = Filesystem::getDiskConfig('tinymce', 'url') . '/' . str_replace('\\', '/', $saveName);

        return json([
            'location' => $url
        ]);
    }

    // 富文本编辑器上传图片
    public function editorImage()
    {
        try {
            $file = request()->file('upload');

            // 验证文件
            validate(['file' => [
                'fileSize' => 1024 * 1024 * 5, // 5MB
                'fileExt'  => 'jpg,jpeg,png,gif,webp'
            ]])->check(['file' => $file]);

            // 保存文件到public/editor目录
            $saveName = Filesystem::disk('editor')->putFile('images', $file);
            // 返回完整的URL路径
            $url = Filesystem::getDiskConfig('editor', 'url') . '/' . str_replace('\\', '/', $saveName);

            return json([
                'uploaded' => true,
                'url' => $url
            ]);
        } catch (\Exception $e) {
            return json([
                'uploaded' => false,
                'error' => [
                    'message' => $e->getMessage()
                ]
            ]);
        }
    }

    // 视频上传
    public function video()
    {
        try {
            $file = request()->file('upload');

            // 验证文件
            validate(['file' => [
                'fileSize' => 1024 * 1024 * 50, // 50MB
                'fileExt'  => 'mp4,mov,avi'
            ]])->check(['file' => $file]);

            // 保存文件到public/editor目录
            $saveName = Filesystem::disk('editor')->putFile('videos', $file);
            // 返回完整的URL路径
            $url = Filesystem::getDiskConfig('editor', 'url') . '/' . str_replace('\\', '/', $saveName);

            return json([
                'uploaded' => true,
                'url' => $url
            ]);

        } catch (\Exception $e) {
            return json([
                'uploaded' => false,
                'error' => [
                    'message' => $e->getMessage()
                ]
            ]);
        }
    }


    //处理数组文件
    public function handleFiles($file_name){
        // 手动将 $_FILES 数据转换为 UploadedFile 对象
        $files = [];
        foreach ($_FILES[$file_name]['name'] as $index => $name) {
            if ($_FILES[$file_name]['error'][$index] === UPLOAD_ERR_OK) {
                $files[$index] = new UploadedFile(
                    $_FILES[$file_name]['tmp_name'][$index],
                    $_FILES[$file_name]['name'][$index],
                    $_FILES[$file_name]['type'][$index],
                    $_FILES[$file_name]['error'][$index]
                );
            } else {
                $files[$index] = null;
            }
        }
        return $files;
    }
}
