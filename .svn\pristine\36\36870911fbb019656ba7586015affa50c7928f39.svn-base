<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Order - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>
<style>
  .grid-cols-{$product_progress|count} {
    grid-template-columns: repeat({$product_progress|count}, minmax(0, 1fr));
  }
</style>
<body>
        <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] min-h-lvh">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>
        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="bg-white p-4 rounded-xl border border-[#dae9ff] md:p-8 md:pt-[2.5rem] md:pb-[6.25rem]">
                <!-- 返回导航 -->
                <div class="mb-3 md:mb-[2.5rem]">
                    <a href="/user/order/" class="text-base flex items-center gap-2 md:text-3xl md:gap-x-5">
                        <img src="__IMG__/icons/changjiantou-zuoshang1.png" alt="" class="w-[1rem] md:w-[1.7rem]">
                        <strong>
                            Order details
                        </strong>
                    </a>
                </div>
                <!-- 产品服务tab -->
                <div class="product-service w-full overflow-x-auto bg-[#f8fdff] mb-5 md:mb-[2.5rem]">
                    <div class="flex flex-nowrap product-list min-w-max text-sm arrow-container md:text-xl">
                        <!-- 产品 -->
                        <!-- 没有开始浅蓝色notstarted，进行中使用proceed橙色，已完成使用completed绿色-->
                        <div class="arrow-step {$product.progress_status}">{$product.name}</div>

                        <!-- 服务 -->
                        {volist name="services" id="vo"}
                        {if !empty($vo.lists)}
                            <!-- 没有开始浅蓝色service-notstarted，服务启用service-proceed，服务完成service-completed-->
                            <div class="arrow-step service-{$vo.lists.progress_status}">{$vo.name}</div>
                        {else }
                            <!-- 没有订购服务 -->
                            <div class="arrow-step">{$vo.name}</div>
                        {/if}
                        {/volist}
                    </div>
                </div>

                <!-- 内容容器 -->
                <div class="product-content">
                    <!-- 产品 -->
                    <div class="product-content-item">
                        <!-- 产品详情，左图右文 -->
                        <div class="product-details border-b border-[#dae9ff] pb-3 md:flex md:gap-x-[2.5rem]">
                            <div class="product-details-img w-[12.5rem] h-[10rem] bg-[#fafbff] border border-[#e0eaff] flex items-center justify-center mb-5 rounded-xl md:w-[18.75rem] md:h-[16.25rem]">
                                <figure>
                                    <img src="{$product.image_detail}" alt="{$product.name}" class="object-cover max-w-[80%] mx-auto " />
                                    <figcaption class="sr-only">product</figcaption>
                                </figure>
                            </div>
                            <div class="">
                                <div class="order-list-item-header-left text-xs mb-5 md:flex md:items-center md:text-[1rem]">
                                    <div class="mb-1 md:mb-0 md:mr-5 ">
                                        <span>Order Date: {:date('j F Y', strtotime($order.create_time))}</span>
                                    </div>
                                    {if $order.tracking_no}
                                    <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                        <span>Tracking number:
                                            <strong class="copy-text">{$order.tracking_no}</strong>
                                        </span>
                                        <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                            <img src="__IMG__/icons/fuzhi.png" class="object-cover w-[1.0625rem] h-[1.0625rem]" alt="" />
                                        </span>
                                    </div>
                                    {/if}
                                </div>

                                <div class="md:mb-5">
                                    <h3 class="text-xl mb-3 font-bold md:text-3xl md:mb-5">
                                        {$product.current_progress.name ?? ''}
                                    </h3>
                                    <div class="w-full overflow-x-auto">
                                        <div class="product-list min-w-max relative pt-3 md:min-w-2xl md:pt-5.5">
                                            <!-- 进度条 -->
                                            <div class="absolute left-0 w-full h-[.3125rem] border border-[#dae9ff] rounded-3xl top-0 md:h-[.625rem]">
                                                <span class="absolute top-0 left-0 h-full bg-[#ec8613] rounded-3xl" style="width: calc({$product.width_percentage} / {$product_progress|count} * 100%)"></span>
                                            </div>
                                            <div class="grid grid-cols-{$product_progress|count} flex-nowrap gap-x-3 text-sm md:text-base text-center">
                                                {volist name="product_progress" id="vo"}
                                                {php}
                                                    $is_current = false;
                                                    $is_past = false;

                                                    // 先检查 $product['current_progress'] 是否存在且非 null
                                                    if (!empty($product['current_progress'])) {
                                                        $is_current = ($vo['id'] == $product['current_progress']['id']);
                                                        $current_index = array_search($product['current_progress'], $product_progress);
                                                        $current_index = $current_index !== false ? $current_index : PHP_INT_MAX;
                                                        $is_past = (array_search($vo, $product_progress) < $current_index);
                                                    }

                                                    $text_color = $is_current || $is_past ? 'text-[#111]' : 'text-[#999]';
                                                {/php}
                                                <span class="{$text_color}">{$vo.name}</span>
                                                {/volist}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-sm md:text-xl md:mt-[2.5rem]">
                                        {$product.current_progress.content ?? ''}
                                    </div>

                                    {if $order.remark}
                                    <div class="mt-2 text-sm flex gap-x-2 bg-[#f8fdff] text-[#999] py-1 px-1.5 md:text-xl md:py-3.5 md:items-center md:px-3 md:mt-3 items-start">
                                        <img src="__IMG__/icons/tis.png" alt="" class="w-[1rem] mt-0.5 md:mt-0 flex-shrink-0">
                                        <p>
                                            <span class="text-[#ec8613]">Remark:</span> {$order.remark}
                                        </p>
                                    </div>
                                    {/if}
                                </div>
                            </div>
                        </div>

                        <!-- 文件区域 -->
                        {notempty name="product_files"}
                        <div class="mt-[1.875rem]">
                            <h3 class="text-xl mb-3 font-bold md:text-[1.5625rem] md:mb-7">Technical documents</h3>
                            <div class="flex items-start gap-x-2 text-sm text-[#f01111] py-1 px-1.5 bg-[#f8fdff] mb-4 md:text-xl md:py-3.5 md:items-center md:px-3">
                                <img src="__IMG__/icons/tishi.png" class="w-[1rem] mt-0.5 md:mt-0" alt="">
                                Note: All materials are valid for 7 days from the date of upload.
                            </div>
                            <div class="">
                                <h4 class="text-lg text-[#155290] mb-2.5 md:text-xl md:mb-3">Download the file</h4>
                                <div class="text-sm download-list md:text-xl">
                                    <ul>
                                        {volist name="product_files" id="vo"}
                                        {if $vo.file}
                                        <li data-id="{$vo.id}" data-type="0" data-main-id="{$product.id}" class="bg-[#f8fdff] border border-[#dae9ff]">
                                            <div class="flex items-center h-full gap-x-3 md:gap-x-2">
                                                <span class="line-clamp-1">{$vo.file_name}</span>
                                                <a href="{$vo.file}" download="{$vo.file_name}" class="w-[2.1875rem] download-link">
                                                    <img src="__IMG__/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-[1.1rem]">
                                                </a>
                                            </div>
                                        </li>
                                        {else }
                                        <li data-id="{$vo.id}" data-type="0" data-main-id="{$product.id}" class="bg-[#f8fdff] border border-[#dae9ff]">
                                            <div class="flex flex-col">
                                                <a href="{$vo.url}" target="_blank" rel="noopener noreferrer" class="text-[#155797] underline download-link">{$vo.url}</a>
                                                <p>
                                                    {$vo.url_describe}
                                                </p>
                                            </div>
                                        </li>
                                        {/if}
                                        {/volist}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {/notempty}
                    </div>

                    <!-- 服务 -->
                    {volist name="services" id="vo"}
                        {if !empty($vo.lists)}
                        <!--已订购该服务-->
                        <div class="product-content-item" style="display: none;">
                            <div class="product-details border-b border-[#dae9ff] pb-3 md:flex md:flex-col md:gap-x-[2.5rem]">
                                <div class="order-list-item-header-left text-xs mb-5 md:flex md:items-center md:text-[1rem]">
                                    <div class="mb-1 md:mb-0 md:mr-5 ">
                                        <span>Order Date: {:date('j F Y', strtotime($vo.lists.order.create_time))}</span>
                                    </div>
                                    {if $vo.lists.order.tracking_no}
                                    <div class="flex gap-x-2 mb-1 items-center md:mb-0">
                                        <span>Tracking number:
                                            <strong class="copy-text">{$vo.lists.order.tracking_no}</strong>
                                        </span>
                                        <span class="cursor-pointer w-[1.25rem] h-[1.25rem]" onclick="copyText(this)">
                                            <img src="__IMG__/icons/fuzhi.png" class="object-cover w-[1.0625rem] h-[1.0625rem]" alt="" />
                                        </span>
                                    </div>
                                    {/if}
                                </div>

                                <div class="md:mb-5">
                                    <h3 class="text-xl mb-3 font-bold md:text-3xl md:mb-5">
                                        {$vo.lists.current_progress.name ?? ''}
                                    </h3>
                                    <div class="w-full overflow-x-auto">
                                        <div class="product-list min-w-max relative pt-3 md:max-w-3xl md:pt-5.5">
                                            <!-- 进度条 -->
                                            <div class="absolute left-0 w-full h-[.3125rem] border border-[#dae9ff] rounded-3xl top-0 md:h-[.625rem]">
                                                <span class="absolute top-0 left-0 h-full w-{$vo.lists.width_percentage}/{$vo.lists.progress|count} bg-[#ec8613] rounded-3xl"></span>
                                            </div>
                                            <div class="grid grid-cols-{$vo.lists.progress|count} flex-nowrap gap-x-3 text-sm md:text-base text-center">
                                                {volist name="vo.lists.progress" id="v"}
                                                {php}
                                                    $is_current = false;
                                                    $is_past = false;

                                                    if (!empty($vo['lists']['current_progress'])) {
                                                        $is_current = ($v['id'] == $vo['lists']['current_progress']['id']);
                                                        $current_index = array_search($vo['lists']['current_progress'], $vo['lists']['progress']);
                                                        $current_index = $current_index !== false ? $current_index : PHP_INT_MAX;
                                                        $is_past = (array_search($v, $vo['lists']['progress']) < $current_index);
                                                    }

                                                    $text_color = $is_current || $is_past ? 'text-[#111]' : 'text-[#999]';
                                                {/php}
                                                <span class="{$text_color}">{$v.name}</span>
                                                {/volist}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-sm md:text-xl md:mt-[2.5rem]">
                                        {$vo.lists.current_progress.content ?? ''}
                                    </div>
                                </div>
                            </div>

                            <!-- 文件区域 -->
                            <div class="mt-[1.875rem]">
                                <h3 class="text-xl mb-3 font-bold md:text-[1.5625rem] md:mb-7">Technical documents</h3>
                                <div class="flex items-start gap-x-2 text-sm text-[#f01111] py-1 px-1.5 bg-[#f8fdff] mb-4 md:text-xl md:py-3.5 md:items-center md:px-3">
                                    <img src="__IMG__/icons/tishi.png" class="w-[1rem] mt-0.5 md:mt-0" alt="">
                                    Note: All materials are valid for 7 days from the date of upload.
                                </div>
                                <!-- 文件上传 -->
                                <div class="flex gap-x-2 flex-col mb-2.5 file-upload-container md:mb-5">
                                    <form id="upload-form" enctype="multipart/form-data">
                                        <input type="hidden" name="order_id" value="{$vo.lists.order.id}" />

                                        <div class="flex flex-col mb-2.5 md:flex-row md:gap-x-5">
                                            <div class="mb-2.5 md:mb-0">
                                                <input type="file" class="hidden file-input" id="upload-file-{{uniqueID}}"
                                                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.zip,.rar" multiple>
                                                <label for="upload-file-{{uniqueID}}"
                                                    class="btn-primary border rounded-md border-[#155797] px-5 py-2 block text-center text-base text-[#155797] cursor-pointer md:px-10 md:rounded-xl">Upload files</label>
                                            </div>
                                            <div class="flex items-start gap-x-2 text-[#999] text-sm md:items-center md:text-xl">
                                                <img src="__IMG__/icons/tishi1.png" alt="" class="w-[.875rem] mt-0.5 md:mt-0">
                                                <span>
                                                    Please upload the DEL Selection Feedback file
                                                </span>
                                            </div>
                                        </div>
                                        <!-- 显示上传的文件名称和提交，取消按钮 -->
                                        <div class="file-upload-actions mb-2.5" style="display: none;">
                                            <div class="file-name-display text-sm mb-2 flex flex-col gap-2.5 text-[#999]">
                                                <p></p>
                                            </div>
                                            <div class="action-buttons text-sm">
                                                <button type="button" class="submit-btn bg-[#155797] cursor-pointer">submit</button>
                                                <button type="button" class="cancel-btn bg-[#f08411] cursor-pointer">cancel</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="flex flex-col gap-y-5">
                                    <!--上传资料-->
                                    {notempty name="vo.lists.user_files"}
                                    <div class="">
                                        <div class="flex gap-x-5 items-center mb-2.5 md:text-xl md:mb-3">
                                            <h4 class="text-lg text-[#f08411]">Upload files</h4>
                                            <span class="text-[#999] text-sm md:text-xl">
                                                {if $vo.lists.last_userfile_time}
                                                {:date('F d, Y, h:i A', strtotime($vo['lists']['last_userfile_time']))}
                                                {/if}
                                            </span>
                                        </div>
                                        <div class="text-sm download-list md:text-xl">
                                            <ul>
                                                {volist name="vo.lists.user_files" id="userfile"}
                                                <li class="bg-[#f8fdff] border border-[#dae9ff]">
                                                    <div class="flex items-center h-full gap-x-3 md:gap-x-2">
                                                        <span class="line-clamp-1">{$userfile.file_name}</span>
                                                        <a href="{$userfile.file}" download="{$userfile.file_name}" class="w-[2.1875rem]">
                                                            <img src="__IMG__/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-[1.1rem]">
                                                        </a>
                                                    </div>
                                                </li>
                                                {/volist}
                                            </ul>
                                        </div>
                                    </div>
                                    {/notempty}

                                    <!--保密文件-->
                                    {notempty name="vo.lists.service_files"}
                                    <div class="">
                                        <div class="flex gap-x-5 items-center mb-2.5 md:mb-3">
                                            <h4 class="text-lg text-[#155290] md:text-xl">Download the file</h4>
                                            <span class="text-[#999] text-sm md:text-xl">
                                                {if $vo.lists.last_servicefile_time}
                                                {:date('F d, Y, h:i A', strtotime($vo['lists']['last_servicefile_time']))}
                                                {/if}
                                            </span>
                                        </div>
                                        <div class="text-sm download-list md:text-xl">
                                            <ul>
                                                {volist name="vo.lists.service_files" id="servicefile"}
                                                {if $servicefile.file}
                                                <li data-id="{$servicefile.id}" data-type="1" data-main-id="{$vo.id}" class="bg-[#f8fdff] border border-[#dae9ff]">
                                                    <div class="flex items-center h-full gap-x-3 md:gap-x-2">
                                                        <span class="line-clamp-1">{$servicefile.file_name}</span>
                                                        <a href="{$servicefile.file}" download="{$servicefile.file_name}" class="w-[2.1875rem] download-link">
                                                            <img src="__IMG__/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-[1.1rem]">
                                                        </a>
                                                    </div>
                                                </li>
                                                {else }
                                                <li data-id="{$servicefile.id}" data-type="1" data-main-id="{$vo.id}" class="bg-[#f8fdff] border border-[#dae9ff]">
                                                    <div class="flex flex-col">
                                                        <a href="{$servicefile.url}" target="_blank" rel="noopener noreferrer" class="text-[#155797] underline download-link">{$servicefile.url}</a>
                                                        <p>
                                                            {$servicefile.url_describe}
                                                        </p>
                                                    </div>
                                                </li>
                                                {/if}
                                                {/volist}
                                            </ul>
                                        </div>
                                    </div>
                                    {/notempty}
                                </div>
                            </div>
                        </div>
                        {else }
                        <!--未订购该服务-->
                        <div class="product-content-item" style="display: none;">
                            <div class="pb-5">
                                <h3 class="text-lg text-[#111111] mb-3 md:text-xl">
                                    {$vo.name}
                                </h3>
                                <!-- 内容 -->
                                <div class="text-[#666] text-base mb-5">
                                    {$vo.description|raw}
                                </div>
                                <!-- 按钮 -->
                                <a class="quote-btn flex min-w-36 justify-around items-center border border-[#f08411] gap-2 text-[#f08411] text-sm py-2.5 cursor-pointer rounded-md relative md:w-[260px] md:h-[60px] md:text-xl md:rounded-lg"
                                    href="{:session('userId') ? '/quote/logged?service_id='. $vo['id'] : 'javascript:;'}"
                                    onclick="return {:session('userId') ? 'true' : 'showQuotePopup(\\'service_id=' . $vo['id'] . '\\')'}">
                                    <span> Quote </span>
                                    <img src="__IMG__/icons/gouwuche-2.png" alt="" class="w-4 right-7 absolute md:w-[1.25rem]">
                                </a>
                            </div>
                        </div>
                        {/if}
                    {/volist}
                </div>
            </div>
        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        // 获取所有tab和内容元素
        const tabs = document.querySelectorAll('.arrow-step');
        const contents = document.querySelectorAll('.product-content-item');

        // 为每个tab添加点击事件
        tabs.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                // 移除所有tab的active类
                tabs.forEach(t => t.classList.remove('active'));
                // 为当前点击的tab添加active类
                tab.classList.add('active');

                // 隐藏所有内容
                contents.forEach(c => c.style.display = 'none');
                // 显示对应下标的内容
                contents[index].style.display = 'block';
            });
        });

        // 默认激活第一个tab
        if (tabs.length > 0) {
            tabs[0].click();
        }

        let uploadCounter = 0;
        document.querySelectorAll('.file-upload-container').forEach(container => {
            const id = `upload-file-${++uploadCounter}`;
            const input = container.querySelector('input[type="file"]');
            const label = container.querySelector('label');

            // 绑定唯一ID
            input.id = id;
            label.htmlFor = id;

            const fileInput = container.querySelector('.file-input');
            const actionsPanel = container.querySelector('.file-upload-actions');
            const fileNameDisplay = container.querySelector('.file-name-display');

            // 文件选择事件
            fileInput.addEventListener('change', function (e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    const fileList = files.map(file =>
                        `<p>${file.name}</p>`
                    ).join('');
                    fileNameDisplay.innerHTML = fileList;
                    actionsPanel.style.display = 'block';
                }
            });

            // 取消按钮事件
            container.querySelector('.cancel-btn').addEventListener('click', () => {
                fileInput.value = '';
                fileNameDisplay.innerHTML = '';
                actionsPanel.style.display = 'none';
            });
        });

        function copyText(buttonElement) {
            // 获取最近的copy-text元素
            const strongElement = buttonElement.closest('.flex').querySelector('strong.copy-text');

            if (!strongElement) {
                console.error('找不到要复制的文本元素');
                return;
            }

            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = strongElement.textContent;
            document.body.appendChild(textarea);

            // 选中并复制文本
            textarea.select();
            document.execCommand('copy');

            // 移除临时元素
            document.body.removeChild(textarea);

            // 可选：显示复制成功的提示
            layer.msg('Copy successfully ', {
                time: 2000,
                icon: 1
            });
        }

        // 上传用户资料，给所有提交按钮添加事件监听（或使用事件委托）
        document.querySelectorAll('.submit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 关键点：通过 this.closest('form') 找到最近的父级表单
                const form = this.closest('form');
                const orderIdInput = form.querySelector('input[name="order_id"]');
                const fileInput = form.querySelector('input[type="file"]');

                const formData = new FormData(form); // 自动包含表单所有字段

                // 手动添加文件（因为 multiple 文件需要通过循环添加）
                Array.from(fileInput.files).forEach(file => {
                    formData.append('files[]', file);
                });

                // 显示加载状态
                this.disabled = true;
                this.textContent = 'uploading...';

                fetch('/upload/userfile', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        // 全部上传成功
                        layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                            location.href = data.url;
                        });
                    } else if (data.code === 2) {
                        // 部分上传成功
                        let errorMsg = data.msg + "<br>";

                        // 显示每个失败文件的错误信息
                        data.failed.forEach(file => {
                            errorMsg += `${file.name}: ${file.error}<br>`;
                        });

                        // 显示成功上传的文件数量
                        if (data.success && data.success.length > 0) {
                            errorMsg += `<br>成功上传 ${data.success.length} 个文件`;
                        }

                        layer.msg(errorMsg, {
                            icon: 2,
                            time: 5000, // 显示5秒
                            anim: 6 // 抖动效果
                        });

                        // 3秒后跳转
                        setTimeout(() => {
                            location.href = data.url;
                        }, 3000);
                    } else {
                        // 完全失败
                        layer.msg('Error: ' + data.msg, { icon: 2 });
                    }
                })
                .catch(error => {
                    layer.msg('上传出错：' + error.message, { icon: 2 });
                })
                .finally(() => {
                    this.disabled = false;
                    this.textContent = 'submit';
                });
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            // 捕获下载链接点击事件
            $('.download-link').on('click', function(e) {
                var li = $(this).closest('li');
                $.ajax({
                    url: '/download/log', // ThinkPHP路由地址
                    type: 'POST',
                    data: {
                        id: li.data('id'),
                        type: li.data('type'),
                        main_id: li.data('main-id')
                    },
                    success: function(response) {
                        // console.log('下载日志记录成功');
                    },
                    error: function(xhr, status, error) {
                        // console.error('下载日志记录失败:', error);
                    }
                });
            });
        });
    </script>
</body>

</html>