<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;
use think\facade\Validate;

class Admins extends Common
{
    //管理员列表
    public function index()
    {
        $List = Db::name('Auth_user')
            ->where("step", 1)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                $group_id = Db::name("Auth_group_access")->where("uid", $item['id'])->value('group_id');
                $item['group_name'] = Db::name("Auth_group")->where('id', $group_id)->value('title');

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

    //添加管理员
    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            $validate = Validate::rule([
                'username' => 'require|min:2|max:10',
                'password' => 'require|min:6|max:20',
            ])->message([
                'username.require' => '用户名不能为空',
                'username.min'    => '用户名长度不能少于2位',
                'username.max'    => '用户名长度不能超过10位',
                'password.require' => '密码不能为空',
                'password.min'    => '密码长度不能少于6位',
                'password.max'    => '密码长度不能超过20位',
            ]);
            if (!$validate->check($data)) {
                $this->error($validate->getError());
            }

            $where = [
                "username" => $data['username'],
                "step" => 1
            ];
            if (Db::name("Auth_user")->where($where)->find()) {
                $this->error("该用户名已存在，请修改！");
            }

            // 生成salt
            $data['salt'] = generateSalt();
            // 明码进行加盐hash解决
            $data['password'] = generateHashPassword(trim($data['password']), $data['salt']);

            $s = Db::name("Auth_user")->insertGetId($data);
            if ($s) {
                $where = [
                    "uid" => $s,
                    "group_id" => $data['group_id']
                ];
                Db::name("Auth_group_access")->insert($where);

                $this->success('添加用户成功');
            } else {
                $this->error("添加用户失败，请重试");
            }
        } else {
            $list = Db::name("Auth_group")->select();

            return view("", [
                "List" => $list
            ]);
        }
    }

    //编辑管理员
    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            $id = $data['id'];

            $validate = Validate::rule([
                'username' => 'require|min:2|max:10',
                'newpassword' => 'min:6|max:20',
            ])->message([
                'username.require' => '用户名不能为空',
                'username.min'    => '用户名长度不能少于2位',
                'username.max'    => '用户名长度不能超过10位',
                'newpassword.min'    => '密码长度不能少于6位',
                'newpassword.max'    => '密码长度不能超过20位',
            ]);
            if (!$validate->check($data)) {
                $this->error($validate->getError());
            }

            $where = [
                ["id", "<>", $id],
                ['username', "=", $data['username']],
                ['step', "=", 1]
            ];
            if (Db::name("Auth_user")->where($where)->find()) {
                $this->error("该用户名已存在，请修改！");
            }

            if(trim($data['newpassword']) != ""){
                // 生成salt
                $data['salt'] = generateSalt();
                // 明码进行加盐hash解决
                $data['password'] = generateHashPassword(trim($data['newpassword']), $data['salt']);
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Auth_user")->strict(false)->save($data);
            if ($s) {
                $where = [
                    "uid" => $id,
                    "group_id" => $data['group_id']
                ];
                if(empty(Db::name("Auth_group_access")->where($where)->find())){
                    //不存在，删除旧的用户角色关系
                    Db::name("Auth_group_access")->where("uid", $id)->delete();
                    Db::name("Auth_group_access")->insert($where);
                }

                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }

        } else {
            $id = input('id');
            $getone = Db::name("Auth_user")->where("id", $id)->find();
            $getone['group_id'] = Db::name("Auth_group_access")->where("uid", $id)->value("group_id");

            $list = Db::name("Auth_group")->select();

            return view("", [
                "getone" => $getone,
                "List" => $list
            ]);
        }
    }

    //删除管理员
    public function del()
    {
        $id = input('id');
        $s = Db::name("Auth_user")->where("id", $id)->update(["step" => 0]);
        if ($s) {
            $this->success("删除成功");
        } else {
            $this->error("删除失败");
        }
    }


    //修改当前登录管理员密码
    public function myeditpwd()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            $validate = Validate::rule([
                'old_password' => 'require',
                'password' => 'require|min:6|max:20|confirm',
            ])->message([
                'old_password.require' => '老密码不能为空',
                'password.require' => '新密码不能为空',
                'password.min'    => '密码长度不能少于6位',
                'password.max'    => '密码长度不能超过20位',
                'password.confirm'    => '两次密码输入不一致',
            ]);
            if (!$validate->check($data)) {
                $this->error($validate->getError());
            }

            $userData = Db::name("Auth_user")->where("id", session('adminId'))->find();
            $password = generateHashPassword($data['old_password'], $userData['salt']);
            if (strcasecmp($password, $userData['password']) != 0) {
                // return $this->error('老密码输入错误，请重新输入！');
            }

            if ($data['password'] === $data['old_password']) {
                $this->error('新密码不能与旧密码相同！');
            }

            // 生成salt
            $data['salt'] = generateSalt();
            // 明码进行加盐hash解决
            $data['password'] = generateHashPassword(trim($data['password']), $data['salt']);

            $s = Db::name("Auth_user")->strict(false)->where("id", session('adminId'))->save($data);
            if ($s) {
                $this->success('修改密码成功');
            } else {
                $this->error("修改密码失败，请重试");
            }
        } else {
            $getone = Db::name("Auth_user")->where("id", session('adminId'))->find();
            return view("", [
                "getone" => $getone
            ]);
        }
    }
}
