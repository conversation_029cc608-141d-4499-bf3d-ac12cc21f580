<?php /*a:3:{s:69:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\order\edit_coupon.html";i:1748938540;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1747812241;s:69:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot_layer.html";i:1748510796;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<!-- tiny编辑器 -->
<script src="/static/admin/js/tinymce/tinymce.min.js"></script>
<script>
    // 通用配置
    const defaultTinyMCEConfig = {
        language: 'zh_CN',
        license_key: 'gpl',
        plugins: 'autoresize code image media link lists',
        height: '250px',
        toolbar: 'code | customMediaUpload image media | undo redo | bold italic | alignleft aligncenter alignright alignjustify | fontsize | link | numlist bullist',

        file_picker_types: 'media image', // 允许选择媒体和图片文件
        images_upload_url: '/admin/tinymceImage', // 图片上传接口

        min_height: 200,      // 最小高度200px
        max_height: 600,      // 最大高度600px
        // autoresize_bottom_margin: 10, // 底部间距（可选）
        // autoresize_overflow_padding: 10, // 溢出内边距（可选）
        promotion: false,  // 禁用推广提示
        content_style: 'img {max-width: 100%; height: auto;}', // 直接内联样式

        setup: function(editor) {
            editor.ui.registry.addButton('customMediaUpload', {
                icon: 'upload',
                tooltip: '上传视频',
                onAction: function() {
                    editor.windowManager.open({
                        title: '上传视频',
                        body: {
                            type: 'panel',
                                items: [{
                                type: 'htmlpanel',
                                html: '<input type="file" id="tinymce-media-upload" accept="video/*,audio/*">'
                            }]
                        },
                        buttons: [
                            {
                                type: 'cancel',
                                name: 'cancel',
                                text: 'Cancel'
                            },
                            {
                                type: 'submit',
                                name: 'save',
                                text: 'Upload',
                                primary: true
                            }
                        ],
                        onSubmit: function(api) {
                            var fileInput = document.getElementById('tinymce-media-upload');
                            if (fileInput.files.length > 0) {
                                var formData = new FormData();
                                formData.append('file', fileInput.files[0]);

                                fetch('/admin/tinymceMedia', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    console.log(data)
                                    if (data.location) {
                                        editor.insertContent(
                                            data.location.match(/\.(mp4|webm|ogg|mov|avi)$/) ?
                                            '<video controls src="' + data.location + '"></video>' :
                                            '<audio controls src="' + data.location + '"></audio>'
                                        );
                                        api.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
    };

    // 封装初始化函数
    function initTinyMCE(selector, customConfig = {}) {
        tinymce.init({
            selector: selector,
            ...defaultTinyMCEConfig, // 通用配置
            ...customConfig, // 自定义配置
        });
    }
</script>
</head>

<body>
    <div class="cnt-basic-list cnt-basic-layer change-files order-info" style="display: block;">
        <form action="<?php echo url('edit_coupon'); ?>" method="post" enctype="multipart/form-data" class="layer-form" autocomplete="off">
            <input type="hidden" name="id" value="<?php echo htmlentities((string) $getone['id']); ?>" />

            <div class="cnt-basic-item">
                <div class="cnt-basic-i class_con">
	                <label>选择订单：</label>
                    <select name="order_id" class="l_xiang">
                        <option value="">请选择</option>
                        <option value="<?php echo htmlentities((string) $order['id']); ?>" <?php if($getone['order_id']==$order['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $order['order_no']); ?></option>
                        <?php if(is_array($order_subs) || $order_subs instanceof \think\Collection || $order_subs instanceof \think\Paginator): $i = 0; $__LIST__ = $order_subs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['order_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['order_no']); ?> / <?php echo htmlentities((string) $vo['service_name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
	            </div>

                <div class="cnt-basic-i class_con">
	                <label>选择试用券：</label>
                    <select name="coupon_id" class="l_xiang">
                        <option value="">请选择</option>
                        <?php if(is_array($coupon) || $coupon instanceof \think\Collection || $coupon instanceof \think\Paginator): $i = 0; $__LIST__ = $coupon;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['coupon_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
	            </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
            </div>
        </form>
    </div>

    <script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script>
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            parent.location.reload();
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    parent.layer.close(window.layerIndex);
                } else {
                    parent.layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

</body>
</html>