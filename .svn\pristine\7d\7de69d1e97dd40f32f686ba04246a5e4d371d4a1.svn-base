document.addEventListener('DOMContentLoaded', function () {
    var select = document.getElementById('Country');
    select.addEventListener('change', function () {
        if (select.value === "") {
            select.style.color = "#ddd";
        } else {
            select.style.color = "#333";
        }
    });
    // 初始化时设置颜色
    if (select.value === "") {
        select.style.color = "#ddd";
    } else {
        select.style.color = "#333";
    }

    // 新增：SendOtp按钮不可点击
    var sendOtpBtn = document.getElementById('SendOtp');
    if (sendOtpBtn) {
        sendOtpBtn.addEventListener('click', function () {
            var input = sendOtpBtn.parentElement.querySelector('input');
            var error = sendOtpBtn.parentElement.parentElement.querySelector('.error');
            var emailReg = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/;
            // 先隐藏错误提示
            if (error) error.classList.add('hidden');
            if (!input || !input.value) {
                if (error) {
                    error.innerText = 'Please enter your email address.';
                    error.classList.remove('hidden');
                }
                input && input.focus();
                return;
            }
            if (!emailReg.test(input.value)) {
                if (error) {
                    error.innerText = 'Please enter the correct email format';
                    error.classList.remove('hidden');
                }
                input.focus();
                return;
            }
            sendOtpBtn.disabled = true;
            sendOtpBtn.classList.add('opacity-50');
        });
    }


    // 新增：表单校验
    var submitBtn = document.getElementById('submitBtn');
    var form = submitBtn.closest('form');
    submitBtn.addEventListener('click', function (e) {
        var valid = true;
        // 先隐藏所有错误提示
        form.querySelectorAll('.error').forEach(function (el) { el.classList.add('hidden'); });
        // 校验必填项
        var requiredIds = ['email', 'Captcha', 'Password', 'ConfirmPassword', 'Country', 'FirstName', 'LastName', 'Organization'];
        requiredIds.forEach(function (id) {
            var input = document.getElementById(id);
            if (!input || (input.value === '' || input.value === null)) {
                var error = input ? input.parentElement.parentElement.querySelector('.error') : null;
                if (error) { error.classList.remove('hidden'); }
                valid = false;
            }
        });

        // 新增：邮箱格式校验
        var emailInput = document.getElementById('email');
        var emailError = emailInput ? emailInput.parentElement.parentElement.querySelector('.error') : null;
        var emailReg = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/;
        if (emailInput && emailInput.value && !emailReg.test(emailInput.value)) {
            if (emailError) {
                emailError.classList.remove('hidden');
                emailError.innerText = 'Please fill in the email information';
            }
            valid = false;
        }
        // 密码复杂性校验
        var pwd = document.getElementById('Password').value;
        var pwdError = document.getElementById('Password').parentElement.parentElement.querySelector('.error');
        var pwdReg = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,16}$/;
        if (!pwdReg.test(pwd)) {
            if (pwdError) { pwdError.classList.remove('hidden'); pwdError.innerHTML = `<img src="/images/icons/tis.png" alt=""> 8-16 characters must contain both digits and letters` }
            valid = false;
        }
        // 确认密码一致性
        var cpwd = document.getElementById('ConfirmPassword').value;
        var cpwdError = document.getElementById('ConfirmPassword').parentElement.parentElement.querySelector('.error');
        if (pwd !== cpwd) {
            if (cpwdError) { cpwdError.classList.remove('hidden'); cpwdError.innerText = '两次输入的密码不一致'; }
            valid = false;
        }
        if (!valid) {
            e.preventDefault();
            return false;
        }

        // 新增：校验是否勾选同意协议
        var agree = document.getElementById('agree');
        var agreeError = agree.parentElement.parentElement.querySelector('.error');
        if (agreeError) agreeError.classList.add('hidden');
        if (!agree.checked) {
            if (agreeError) {
                agreeError.innerText = 'Please check the box to agree to the User Privacy Policy';
                agreeError.classList.remove('hidden');
            }
            valid = false;
        }
        if (!valid) {
            e.preventDefault();
            return false;
        }
    });
    // 密码显示/隐藏及背景切换
    function bindPasswordEye(eyeId) {
        var eye = document.getElementById(eyeId);
        if (eye) {
            eye.addEventListener('click', function () {
                var input = eye.parentElement.querySelector('input');
                if (input) {
                    if (input.type === 'password') {
                        input.type = 'text';
                        eye.style.backgroundImage = "url('/images/icons/yanjing_yincang_1.png')";
                    } else {
                        input.type = 'password';
                        eye.style.backgroundImage = "url('/images/icons/yanjing_yincang_o.png')";
                    }
                }
            });
        }
    }
    bindPasswordEye('passwordEye');
    bindPasswordEye('passwordEye2');
});