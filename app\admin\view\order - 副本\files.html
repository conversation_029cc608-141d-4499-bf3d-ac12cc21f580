<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">文件列表</a>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </p>
                <a href="{:url('add_file', ['order_id'=>$order_id, 'order_type'=>$order_type])}" class="add-button">添加保密文件</a>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">订单编号</td>
                            <td class="mid_one">产品/服务</td>
                            <td class="mid_one">文件/链接</td>
                            <td class="mid_one">类型</td>
                            <td class="mid_one">创建时间</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>

                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one">{$order_no}</td>
                            <td class="mid_one">{$vo.main_name}</td>
                            <td class="mid_one">{if $vo.file_name}{$vo.file_name}{else }{$vo.url}{/if}</td>
                            <td class="mid_one">{$vo.file_type==0?"保密文件":"用户资料"}</td>
                            <td class="mid_one">{$vo.create_time}</td>
                            <td class="mid_s">
                                {if $vo.file_type==0}
                                <a href="{:url('del_file', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit_file', ['id'=>$vo['id']])}" class="basic">修改</a>
                                {/if}
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}

</body>
</html>