<?php
//文件地址
namespace app\admin\route;

//使用Route对象
use think\facade\Route;

Route::get('captcha', 'Login/captcha');

Route::post('tinymceImage', 'Common/tinymceImage');
Route::post('tinymceMedia', 'Common/tinymceMedia');

Route::post('editorImage', 'Common/editorImage');

// 前台菜单管理路由
Route::group('frontend_menu', function () {
    Route::get('', 'FrontendMenu/index');
    Route::get('footer', 'FrontendMenu/footer');
    Route::get('add', 'FrontendMenu/add');
    Route::post('add', 'FrontendMenu/add');
    Route::get('edit', 'FrontendMenu/edit');
    Route::post('edit', 'FrontendMenu/edit');
    Route::post('delete', 'FrontendMenu/delete');
    Route::post('getDynamicData', 'FrontendMenu/getDynamicData');
});
?>
