<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}

    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.1/themes/base/jquery-ui.css">
</head>

<style>
    .class_con{
        width: 600px;
    }
    .class_con span{
        color: inherit;
    }
    .sortable-menu {
        min-height: 20px;
    }
    .manag-radio-item {
        padding: 5px 10px;
        margin: 5px 0;
        background: #f5f5f5;
        border: 1px solid #ddd;
        cursor: move;
        position: relative;
    }
    .parent {
        background: #e9e9e9;
        font-weight: bold;
    }
    .child {
        background: #f0f0f0;
    }
    .child2 {
        background: #f8f8f8;
    }
    .child-container, .child2-container {
        margin-left: 15px;
    }
    .handle {
        cursor: move;
        margin-right: 5px;
    }
    .ui-sortable-helper {
        opacity: 0.8;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;菜单管理
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('index')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <div class="class_con">
                    <div class="manag-radio-list sortable-menu">
                    {volist name="menus" id="vo"}
                        <div class="manag-radio-item parent" data-menu-id="{$vo.id}">
                            <span><i class="handle ui-icon ui-icon-grip-dotted-vertical"></i>{$vo.title}</span>
                        </div>

                        <div class="child-container" data-parent="{$vo.id}">
                            {volist name="vo.son" id="vo2"}
                            <div class="manag-radio-item child" data-menu-id="{$vo2.id}" data-parent="{$vo.id}">
                                <span><i class="handle ui-icon ui-icon-grip-dotted-vertical"></i>{$vo2.title}</span>
                            </div>

                            <div class="child2-container" data-parent="{$vo2.id}">
                                {if isset($vo2['son'])}
                                {volist name="vo2.son" id="vo3"}
                                <div class="manag-radio-item child2" data-menu-id="{$vo3.id}" data-parent="{$vo2.id}">
                                    <span><i class="handle ui-icon ui-icon-grip-dotted-vertical"></i>&nbsp;&nbsp;{$vo3.title}</span>
                                </div>
                                {/volist}
                                {/if}
                            </div>
                            {/volist}
                        </div>
                    {/volist}
                    </div>
                </div>

                <!-- 添加隐藏字段 -->
                <input type="hidden" name="menu_order" id="menuOrderInput" value="{$initialOrder|default='[]'}">

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script src="https://code.jquery.com/ui/1.13.1/jquery-ui.min.js"></script>

    <script>
        $(function() {
            // 初始化隐藏字段的值
            function initializeMenuOrder() {
                var menuOrder = [];
                var sort = 0;

                // 处理一级菜单
                $(".sortable-menu > .parent").each(function() {
                    sort++;
                    var menuId = $(this).data("menu-id");
                    menuOrder.push({
                        id: menuId,
                        parent_id: 0,
                        sort: sort
                    });

                    // 处理二级菜单
                    var childOrder = 0;
                    $(this).next(".child-container").find("> .child").each(function() {
                        childOrder++;
                        var childId = $(this).data("menu-id");
                        menuOrder.push({
                            id: childId,
                            parent_id: menuId,
                            sort: childOrder
                        });

                        // 处理三级菜单
                        var child2Order = 0;
                        $(this).next(".child2-container").find("> .child2").each(function() {
                            child2Order++;
                            var child2Id = $(this).data("menu-id");
                            menuOrder.push({
                                id: child2Id,
                                parent_id: childId,
                                sort: child2Order
                            });
                        });
                    });
                });

                // 设置初始值
                $("#menuOrderInput").val(JSON.stringify(menuOrder));
            }

            // 页面加载时初始化
            initializeMenuOrder();

            // 更新菜单顺序函数
            function updateMenuOrder() {
                var menuOrder = [];
                var sort = 0;

                // 处理一级菜单
                $(".sortable-menu > .parent").each(function() {
                    sort++;
                    var menuId = $(this).data("menu-id");
                    menuOrder.push({
                        id: menuId,
                        parent_id: 0,
                        sort: sort
                    });

                    // 处理二级菜单
                    var childOrder = 0;
                    $(this).next(".child-container").find("> .child").each(function() {
                        childOrder++;
                        var childId = $(this).data("menu-id");
                        menuOrder.push({
                            id: childId,
                            parent_id: menuId,
                            sort: childOrder
                        });

                        // 处理三级菜单
                        var child2Order = 0;
                        $(this).next(".child2-container").find("> .child2").each(function() {
                            child2Order++;
                            var child2Id = $(this).data("menu-id");
                            menuOrder.push({
                                id: child2Id,
                                parent_id: childId,
                                sort: child2Order
                            });
                        });
                    });
                });

                // 更新隐藏字段的值
                $("#menuOrderInput").val(JSON.stringify(menuOrder));
            }

            // 初始化所有可排序的容器
            $(".sortable-menu").sortable({
                items: "> .parent, > .child-container > .child, > .child-container > .child2-container > .child2",
                handle: ".handle",
                placeholder: "ui-state-highlight",
                start: function(e, ui) {
                    // 拖拽开始时可以添加额外逻辑
                },
                change: function(e, ui) {
                    // 拖拽过程中实时更新
                    updateMenuOrder();
                },
                update: function(e, ui) {
                    // 拖拽结束后更新
                    updateMenuOrder();
                }
            });

            // 允许子容器内的元素排序
            $(".child-container").sortable({
                items: "> .child, > .child2-container > .child2",
                handle: ".handle",
                connectWith: ".child-container",
                placeholder: "ui-state-highlight",
                change: function(e, ui) {
                    updateMenuOrder();
                },
                update: function(e, ui) {
                    updateMenuOrder();
                }
            });

            $(".child2-container").sortable({
                items: "> .child2",
                handle: ".handle",
                connectWith: ".child2-container",
                placeholder: "ui-state-highlight",
                change: function(e, ui) {
                    updateMenuOrder();
                },
                update: function(e, ui) {
                    updateMenuOrder();
                }
            });

            // 禁止不同级别之间的拖拽
            $(".sortable-menu, .child-container, .child2-container").on("sortreceive", function(event, ui) {
                var senderParentLevel = $(ui.sender).closest('[class*="container"]').length;
                var receiverParentLevel = $(this).closest('[class*="container"]').length;

                if (senderParentLevel !== receiverParentLevel) {
                    $(ui.sender).sortable("cancel");
                }
            });

        });
    </script>

</body>
</html>