-- 前台菜单表
CREATE TABLE `opd_frontend_menus` (
  `id` int NOT NULL AUTO_INCREMENT,
  `parent_id` int DEFAULT '0' COMMENT '父菜单ID',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `url` varchar(255) DEFAULT NULL COMMENT '菜单URL',
  `type` tinyint(1) DEFAULT '1' COMMENT '菜单类型(1:静态链接,2:产品列表,3:资源分类,4:新闻分类,5:FAQ分类)',
  `target_id` int DEFAULT NULL COMMENT '关联ID(产品ID、分类ID等)',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
  `position` varchar(20) DEFAULT 'header' COMMENT '位置(header:头部,footer:底部)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_position` (`position`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='前台菜单表';

-- 插入示例数据
INSERT INTO `opd_frontend_menus` (`parent_id`, `name`, `url`, `type`, `target_id`, `icon`, `sort`, `status`, `position`) VALUES
(0, 'Home', '/', 1, NULL, 'home', 1, 1, 'header'),
(0, 'DELHunter', '/news/', 1, NULL, 'news', 2, 1, 'header'),
(0, 'Services', '/product/', 2, NULL, 'product', 3, 1, 'header'),
(0, 'Resources', '/resources/', 3, NULL, 'resource', 4, 1, 'header'),
(0, 'iCommunity', '/iCommunity/', 1, NULL, 'community', 5, 1, 'header'),
(0, 'About', '/about', 1, NULL, 'about', 1, 1, 'footer'),
(0, 'Contact Us', '/contact', 1, NULL, 'contact', 2, 1, 'footer'),
(0, 'Terms of Service', '/privacy/terms', 1, NULL, 'terms', 3, 1, 'footer'),
(0, 'Privacy Agreement', '/privacy/', 1, NULL, 'privacy', 4, 1, 'footer');

-- 前台菜单配置表
CREATE TABLE `opd_frontend_menu_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='前台菜单配置表';

-- 插入配置数据
INSERT INTO `opd_frontend_menu_config` (`config_key`, `config_value`, `description`) VALUES
('header_menu_style', 'dropdown', '头部菜单样式(dropdown:下拉菜单,mega:大菜单)'),
('footer_menu_columns', '4', '底部菜单列数'),
('menu_cache_time', '3600', '菜单缓存时间(秒)'),
('dynamic_menu_enabled', '1', '是否启用动态菜单(0:禁用,1:启用)'); 