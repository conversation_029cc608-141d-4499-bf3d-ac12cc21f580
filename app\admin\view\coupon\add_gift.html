<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>
<style>
    .class_con label{
        width: 220px;
    }
    .class_con span{
        color: #464df4;
        margin-left: 0;
    }
    .de_y{
        width: 220px;
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;赠送试用券
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add_gift')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>名称：</label>
                    <span>{$getone.name}</span>
                </div>

                <div class="class_con">
                    <label>说明：</label>
                    <span>{$getone.description}</span>
                </div>

                <div class="class_con">
                    <label>开始时间：</label>
                    <span>{$getone.start_time}</span>
                </div>

                <div class="class_con">
                    <label>结束时间：</label>
                    <span>{$getone.end_time}</span>
                </div>

                {if $getone.scope_type == 0}
                <!-- 单独 -->
                <div class="class_con">
                    <label>选择用户：</label>
                    <div class="multi-select-container">
                        <div class="user-select-header">
                            <button type="button" class="select-all-btn">全选</button>
                            <button type="button" class="deselect-all-btn">取消选择</button>
                            <span class="selected-count">已选择: <span class="count">0</span> 个用户</span>
                        </div>
                        <select name="user_ids[]" class="l_xiang user-multi-select" multiple="multiple" size="10">
                            {volist name="users" id="vo"}
                                <option value="{$vo.id}">{$vo.email}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                {/if}

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">{$getone.scope_type==0?"发放给选择用户":"发放给所有用户"}</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

</body>
</html>