<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;菜单权限
                <a href="{:url('roles')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('roles_menus')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>角色名称：</label>
                    <input type="text" name="name" value="{$getone.name}" readonly class="input-readonly" />
                </div>

                <div class="class_con">
                    <label>菜单权限：</label>
                    <div class="manag-radio-list">
                    {volist name="menus" id="vo"}
                        <div class="manag-radio-item parent" data-menu-id="{$vo.id}">
                            <label>
                                <input type="checkbox" name="menu_ids[]" value="{$vo.id}"
                                    data-level="1" data-parent="0" class="menu-checkbox">
                                {$vo.title}
                            </label>
                        </div>
                        {volist name="vo.son" id="vo2"}
                        <div class="manag-radio-item child" data-menu-id="{$vo2.id}" data-parent="{$vo.id}">
                            <label>
                                <input type="checkbox" name="menu_ids[]" value="{$vo2.id}"
                                    data-level="2" data-parent="{$vo.id}" class="menu-checkbox">
                                {$vo2.title}
                            </label>
                        </div>
                            {volist name="vo2.son" id="vo3"}
                            <div class="manag-radio-item child2" data-menu-id="{$vo3.id}" data-parent="{$vo2.id}">
                                <label>
                                    <input type="checkbox" name="menu_ids[]" value="{$vo3.id}"
                                        data-level="3" data-parent="{$vo2.id}" class="menu-checkbox">
                                    &nbsp;&nbsp;{$vo3.title}
                                </label>
                            </div>
                            {/volist}
                        {/volist}
                    {/volist}
                    </div>
                </div>

                <input type="hidden" name="menus" value="" />

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('roles')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        $(document).ready(function() {
            // 存储当前角色ID和已更新的菜单状态
            let currentRoleId = $('#role_id').val();
            let roleMenus = {$role_menus|raw};
            let updatedMenus = {...roleMenus};

            function initMenuCheckboxes() {
                // 1. 初始化所有复选框状态（原 initCheckboxStates 的逻辑）
                $('.menu-checkbox').each(function() {
                    const menuId = $(this).val();
                    const state = roleMenus[menuId] || 0; // 默认未选中

                    $(this).prop('checked', state === 1); // 1=全选
                    $(this).prop('indeterminate', state === 2); // 2=半选
                });

                // 2. 自底向上更新父级状态
                // 先更新 3 级菜单的父级（2 级菜单）
                $('.menu-checkbox[data-level="3"]').each(function() {
                    updateParentState($(this));
                });

                // 再更新 2 级菜单的父级（1 级菜单）
                $('.menu-checkbox[data-level="2"]').each(function() {
                    updateParentState($(this));
                });
            }
            initMenuCheckboxes();

            // 更新父级复选框状态
            function updateParentState($checkbox) {
                const level = parseInt($checkbox.data('level'));
                const menuId = $checkbox.val();

                if (level === 3) {
                    // 三级菜单 - 更新二级父菜单
                    const parentId = $checkbox.data('parent');
                    updateSpecificParent(parentId);
                } else if (level === 2) {
                    // 二级菜单 - 更新一级父菜单
                    const parentId = $checkbox.data('parent');
                    updateSpecificParent(parentId);

                    // 同时更新自己的子菜单
                    const childCheckboxes = $(`.menu-checkbox[data-parent="${menuId}"]`);
                    const isChecked = $checkbox.prop('checked');
                    const isIndeterminate = $checkbox.prop('indeterminate');

                    if (!isIndeterminate) {
                        childCheckboxes.prop('checked', isChecked);
                        // 记录子菜单状态变化
                        childCheckboxes.each(function() {
                            recordMenuChange($(this));
                        });
                    }
                } else if (level === 1) {
                    // 一级菜单 - 更新所有子菜单
                    const childCheckboxes = $(`.menu-checkbox[data-parent="${menuId}"]`);
                    const isChecked = $checkbox.prop('checked');

                    childCheckboxes.prop('checked', isChecked);
                    childCheckboxes.prop('indeterminate', false);

                    // 记录子菜单状态变化
                    childCheckboxes.each(function() {
                        recordMenuChange($(this));
                    });

                    // 更新二级菜单的子菜单
                    childCheckboxes.filter('[data-level="2"]').each(function() {
                        const subChildCheckboxes = $(`.menu-checkbox[data-parent="${$(this).val()}"]`);
                        subChildCheckboxes.prop('checked', isChecked);
                        // 记录子菜单状态变化
                        subChildCheckboxes.each(function() {
                            recordMenuChange($(this));
                        });
                    });
                }
            }

            // 更新特定父菜单状态
            function updateSpecificParent(parentId) {
                const $parentCheckbox = $(`.menu-checkbox[value="${parentId}"]`);
                if ($parentCheckbox.length === 0) return;

                const childCheckboxes = $(`.menu-checkbox[data-parent="${parentId}"]`);
                const checkedCount = childCheckboxes.filter(':checked').length;
                const indeterminateCount = childCheckboxes.filter(function() {
                    return $(this).prop('indeterminate');
                }).length;
                const totalCount = childCheckboxes.length;

                let newState;
                if (checkedCount === 0 && indeterminateCount === 0) {
                    $parentCheckbox.prop('checked', false);
                    $parentCheckbox.prop('indeterminate', false);
                    newState = 0;
                } else if (checkedCount === totalCount) {
                    $parentCheckbox.prop('checked', true);
                    $parentCheckbox.prop('indeterminate', false);
                    newState = 1;
                } else {
                    $parentCheckbox.prop('checked', false);
                    $parentCheckbox.prop('indeterminate', true);
                    newState = 2;
                }

                // 记录父菜单状态变化
                recordMenuChange($parentCheckbox, newState);

                // 递归更新上级菜单
                const grandParentId = $parentCheckbox.data('parent');
                if (grandParentId != 0) {
                    updateSpecificParent(grandParentId);
                }
            }

            // 记录菜单状态变化
            function recordMenuChange($checkbox, state = null) {
                const menuId = $checkbox.val();
                const currentState = state !== null ? state : ($checkbox.prop('indeterminate') ? 2 : ($checkbox.prop('checked') ? 1 : 0));

                updatedMenus[menuId] = currentState;

                $("input[name='menus']").val(JSON.stringify(updatedMenus));
            }

            // 绑定change事件
            $(document).on('change', '.menu-checkbox', function() {
                $(this).prop('indeterminate', false);
                updateParentState($(this));
                recordMenuChange($(this));
            });

            // 初始化indeterminate状态
            $('[data-indeterminate="true"]').each(function() {
                $(this).prop('indeterminate', true);
            });
        });
    </script>
</body>
</html>