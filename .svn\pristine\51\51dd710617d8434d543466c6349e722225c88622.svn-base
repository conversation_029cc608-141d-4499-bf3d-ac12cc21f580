/**
 * 事件处理模块
 * 管理所有评论相关的交互事件
 *
 * ===== 后端对接说明 =====
 *
 * 1. 用户交互流程：
 *    - 用户点击"Reply"按钮 → 显示编辑器
 *    - 用户输入内容并点击"发布" → 调用CommentSystem.addReply()
 *    - CommentSystem会调用后端API保存数据
 *
 * 2. 编辑器内容处理：
 *    - 子评论编辑器会预填充"@用户名 "
 *    - 发布时会格式化为"@用户名：实际内容"
 *    - HTML实体(&nbsp;等)会被正确处理为普通文本
 *
 * 3. 前端验证：
 *    - 评论长度限制
 *    - 内容不能为空
 *    - HTML标签会被清理
 */
window.EventHandler = (function() {
    'use strict';

    // 当前活动的编辑器
    let activeEditors = new Map();

    // 用户信息卡片隐藏定时器
    let hideTimer = null;

    /**
     * 初始化事件处理器
     */
    function init() {
        bindEvents();
        TemplateManager.init();

        // 默认展开所有回复区域
        setTimeout(autoExpandReplies, 100);
    }

    /**
     * 绑定所有事件
     */
    function bindEvents() {
        // 主评论发送按钮
        bindMainCommentSubmit();

        // 用户信息卡片显示/隐藏
        bindUserInfoCard();

        // 徽章/关于标签切换
        bindBadgeAboutTabs();

        // 回复按钮相关事件
        bindReplyEvents();

        // 编辑器相关事件
        bindEditorEvents();

        // 滚动到回复区域
        bindScrollToReply();
    }

    /**
     * 绑定主评论提交事件
     */
    function bindMainCommentSubmit() {
        $('#send_btn').off('click.commentSystem').on('click.commentSystem', function() {
            const $btn = $(this);
            const $textarea = $('#Textarea');
            const content = $textarea.val().trim();

            // 防止重复提交
            if ($btn.prop('disabled')) {
                return;
            }

            try {
                // 设置加载状态
                setButtonLoading($btn, true);

                // 添加评论
                CommentSystem.addMainComment(content);

                // 清空输入框
                $textarea.val('');

                // 滚动到新评论
                setTimeout(() => {
                    const $lastComment = $('.reply-list').last();
                    if ($lastComment.length) {
                        CommentUtils.scrollToElement($lastComment);
                    }
                }, 100);

            } catch (error) {
                CommentUtils.showMessage(error.message, 'error');
            } finally {
                // 恢复按钮状态
                setButtonLoading($btn, false);
            }
        });
    }

    /**
     * 绑定用户信息卡片事件
     * 支持主评论和子评论的用户头像悬停显示
     * 使用Portal技术避免overflow:hidden问题
     * 支持鼠标在卡片上操作
     */
    function bindUserInfoCard() {
        // 移除旧的事件绑定
        $(document).off('mouseenter.userCard mouseleave.userCard', '.user-avatar-container');
        $(document).off('mouseenter.userCard mouseleave.userCard', '.user-card-portal');

        // 绑定头像容器鼠标进入事件
        $(document).on('mouseenter.userCard', '.user-avatar-container', function() {
            const $container = $(this);
            let $userCard = $container.find('.user-info-card');

            // 清除隐藏定时器
            if (hideTimer) {
                clearTimeout(hideTimer);
                hideTimer = null;
            }

            if ($userCard.length) {
                // 隐藏其他所有用户卡片
                hideAllUserCards();

                // 使用Portal技术：将卡片移动到body层级
                const cardHtml = $userCard.prop('outerHTML');
                $userCard.hide(); // 隐藏原始卡片

                // 在body中创建Portal卡片
                const $portalCard = $(cardHtml).addClass('user-card-portal');
                $('body').append($portalCard);

                // 计算并设置Portal卡片位置
                positionPortalCard($container, $portalCard);

                // 显示Portal卡片
                $portalCard.stop(true, true).fadeIn(200);

                // 存储引用以便清理
                $container.data('portalCard', $portalCard);

                // 绑定Portal卡片的鼠标事件
                bindPortalCardEvents($container, $portalCard);
            }
        });

        // 绑定头像容器鼠标离开事件
        $(document).on('mouseleave.userCard', '.user-avatar-container', function() {
            const $container = $(this);

            // 延迟隐藏，给用户时间移动到卡片上
            hideTimer = setTimeout(() => {
                const $portalCard = $container.data('portalCard');
                if ($portalCard && $portalCard.length && !$portalCard.is(':hover')) {
                    hideUserCard($container, $portalCard);
                }
            }, 100); // 100ms延迟
        });

        // 点击其他地方隐藏所有卡片
        $(document).on('click.userCard', function(e) {
            if (!$(e.target).closest('.user-avatar-container').length &&
                !$(e.target).closest('.user-card-portal').length) {
                hideAllUserCards();
            }
        });

        // 滚动时隐藏卡片
        $(window).on('scroll.userCard resize.userCard', function() {
            hideAllUserCards();
        });
    }

    /**
     * 绑定Portal卡片的鼠标事件
     * @param {jQuery} $container - 头像容器
     * @param {jQuery} $portalCard - Portal卡片
     */
    function bindPortalCardEvents($container, $portalCard) {
        // 鼠标进入卡片时保持显示
        $portalCard.on('mouseenter.portalCard', function() {
            // 清除任何隐藏定时器
            if (hideTimer) {
                clearTimeout(hideTimer);
                hideTimer = null;
            }
        });

        // 鼠标离开卡片时隐藏
        $portalCard.on('mouseleave.portalCard', function() {
            hideTimer = setTimeout(() => {
                hideUserCard($container, $portalCard);
            }, 100); // 100ms延迟
        });
    }

    /**
     * 隐藏指定的用户信息卡片
     * @param {jQuery} $container - 头像容器
     * @param {jQuery} $portalCard - Portal卡片
     */
    function hideUserCard($container, $portalCard) {
        if ($portalCard && $portalCard.length) {
            $portalCard.stop(true, true).fadeOut(200, function() {
                $portalCard.off('.portalCard'); // 移除事件绑定
                $portalCard.remove();
            });
            $container.removeData('portalCard');
        }
    }

    /**
     * 隐藏所有用户信息卡片
     */
    function hideAllUserCards() {
        // 清除所有隐藏定时器
        if (hideTimer) {
            clearTimeout(hideTimer);
            hideTimer = null;
        }

        // 隐藏Portal卡片
        $('.user-card-portal').each(function() {
            const $portalCard = $(this);
            $portalCard.stop(true, true).fadeOut(200, function() {
                $portalCard.off('.portalCard'); // 移除事件绑定
                $portalCard.remove();
            });
        });

        // 清理数据引用
        $('.user-avatar-container').removeData('portalCard');

        // 隐藏原始卡片
        $('.user-info-card').stop(true, true).fadeOut(100);
    }

    /**
     * 计算并设置Portal卡片位置
     * @param {jQuery} $container - 头像容器
     * @param {jQuery} $portalCard - Portal卡片
     */
    function positionPortalCard($container, $portalCard) {
        const containerOffset = $container.offset();
        const containerWidth = $container.outerWidth();
        const containerHeight = $container.outerHeight();
        const cardWidth = $portalCard.outerWidth();
        const cardHeight = $portalCard.outerHeight();
        const windowWidth = $(window).width();
        const windowHeight = $(window).height();
        const scrollTop = $(window).scrollTop();

        let left = containerOffset.left;
        let top = containerOffset.top + containerHeight + 5; // 5px间距

        // 检查右边界
        if (left + cardWidth > windowWidth - 20) {
            left = containerOffset.left + containerWidth - cardWidth;
        }

        // 检查下边界
        if (top + cardHeight > windowHeight + scrollTop - 20) {
            top = containerOffset.top - cardHeight - 5; // 显示在上方
        }

        // 确保不超出左边界
        if (left < 10) {
            left = 10;
        }

        // 确保不超出上边界
        if (top < scrollTop + 10) {
            top = scrollTop + 10;
        }

        $portalCard.css({
            position: 'fixed',
            left: left,
            top: top - scrollTop,
            zIndex: 9999
        });
    }

    /**
     * 调整用户信息卡片位置，避免超出屏幕
     * @param {jQuery} $container - 头像容器
     * @param {jQuery} $userCard - 用户信息卡片
     */
    function adjustCardPosition($container, $userCard) {
        // 重置位置
        $userCard.removeClass('card-right card-top');

        // 获取容器和卡片的位置信息
        const containerOffset = $container.offset();
        const containerWidth = $container.outerWidth();
        const cardWidth = $userCard.outerWidth();
        const cardHeight = $userCard.outerHeight();
        const windowWidth = $(window).width();
        const windowHeight = $(window).height();
        const scrollTop = $(window).scrollTop();

        // 检查是否会超出右边界
        if (containerOffset.left + cardWidth > windowWidth - 20) {
            $userCard.addClass('card-right');
            $userCard.css({
                'left': 'auto',
                'right': '0'
            });
        }

        // 检查是否会超出下边界
        if (containerOffset.top + containerWidth + cardHeight > windowHeight + scrollTop - 20) {
            $userCard.addClass('card-top');
            $userCard.css({
                'top': 'auto',
                'bottom': '100%'
            });
        }
    }

    /**
     * 绑定徽章/关于标签切换事件
     * 支持原始卡片和Portal卡片
     */
    function bindBadgeAboutTabs() {
        // 移除旧的事件绑定
        $(document).off('click.badgeAbout', '.badge-about-btn');

        // 绑定标签切换事件（支持事件委托，包括Portal卡片）
        $(document).on('click.badgeAbout', '.badge-about-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const $btn = $(this);
            const $badgeAbout = $btn.closest('.badge-about');
            const $allBtns = $badgeAbout.find('.badge-about-btn');
            const index = $allBtns.index($btn);

            console.log('标签页切换:', $btn.text(), 'index:', index);

            // 切换激活状态
            $allBtns.removeClass('active');
            $btn.addClass('active');

            // 切换内容显示
            const $tabContent = $badgeAbout.find('.tab-content');
            const $tabItems = $tabContent.find('.tab-content-item');

            if ($tabItems.length > 0) {
                $tabItems.hide();
                if (index >= 0 && index < $tabItems.length) {
                    $tabItems.eq(index).show();
                } else {
                    // 如果索引不匹配，显示第一个
                    $tabItems.first().show();
                }
            }

            console.log('标签页切换完成');
        });
    }

    /**
     * 绑定回复相关事件
     */
    function bindReplyEvents() {
        // 展开/收起回复列表
        bindReplyToggle();

        // 回复按钮
        bindReplyButtons();

        // 楼中楼回复按钮
        bindSubReplyButtons();
    }

    /**
     * 绑定回复展开/收起事件
     */
    function bindReplyToggle() {
        // 展开回复
        $(document).off('click.replyToggle', '.lzl_link_unfold')
            .on('click.replyToggle', '.lzl_link_unfold', function() {
                const $btn = $(this);
                const $container = $btn.closest('.reply-list, .p_postlist_c');
                const $commentReply = $container.find('.comment-reply');
                const replyCount = $commentReply.find('.comment-reply-item').length;

                if (replyCount > 0) {
                    // 有回复，展开显示
                    $btn.hide();
                    $btn.siblings('.pack-reply').show();
                    $commentReply.slideDown(300);
                } else {
                    // 没有回复，显示编辑器
                    toggleReplyEditor($container, 'reply');
                }
            });

        // 收起回复
        $(document).off('click.replyToggle', '.pack-reply')
            .on('click.replyToggle', '.pack-reply', function() {
                const $btn = $(this);
                const $container = $btn.closest('.reply-list, .p_postlist_c');
                const $commentReply = $container.find('.comment-reply');

                $btn.hide();
                $btn.siblings('.lzl_link_unfold').show();
                $commentReply.slideUp(300);
            });
    }

    /**
     * 绑定回复按钮事件
     */
    function bindReplyButtons() {
        $(document).off('click.replyBtn', '.item-reply')
            .on('click.replyBtn', '.item-reply', function() {
                const $btn = $(this);
                const $container = $btn.closest('.reply-comment');
                toggleReplyEditor($container, 'reply');
            });
    }

    /**
     * 绑定楼中楼回复按钮事件
     */
    function bindSubReplyButtons() {
        $(document).off('click.subReplyBtn', '.item-building')
            .on('click.subReplyBtn', '.item-building', function() {
                const $btn = $(this);
                const reply_to_comment = $btn.data("comment-id");
                const $commentItem = $btn.closest('.comment-reply-item');
                const $replyComment = $commentItem.closest('.comment-reply').find('.reply-comment');
                const userName = $commentItem.find('.iCommunity-right-title-name a').text().trim() || '用户';

                toggleReplyEditor($replyComment, 'building', userName, reply_to_comment);
            });
    }

    /**
     * 切换回复编辑器显示状态
     * @param {jQuery} $container - 容器元素
     * @param {string} type - 编辑器类型
     * @param {string} replyToUser - 回复的用户名
     */
    function toggleReplyEditor($container, type, replyToUser = '', replyToComment = '') {
        const containerId = $container.attr('data-container-id') || CommentUtils.generateId();
        $container.attr('data-container-id', containerId);

        // 检查是否已有编辑器
        if (activeEditors.has(containerId)) {
            // 移除现有编辑器
            removeEditor(containerId);
            return;
        }

        // 移除容器内其他编辑器
        $container.find('.lzl_editor_container').remove();

        // 创建编辑器内容
        let placeholder = '';
        if (type === 'building' && replyToUser) {
            placeholder = `<span style="color:#155797;" data-showname="${replyToUser}">@${replyToUser}</span>&nbsp;`;
        }

        // 生成编辑器HTML
        const editorHtml = TemplateManager.generateEditor(type, placeholder, replyToComment);
        $container.append(editorHtml);

        // 记录活动编辑器
        const $editor = $container.find('.lzl_editor_container');
        activeEditors.set(containerId, {
            $container: $container,
            $editor: $editor,
            type: type,
            replyToUser: replyToUser
        });

        // 设置焦点
        const $editableDiv = $editor.find('.edui-container');
        $editableDiv.focus();

        // 如果有@用户名，设置光标位置
        if (placeholder) {
            setTimeout(() => {
                setCursorToEnd($editableDiv[0]);
                bindAtUserProtection($editableDiv, replyToUser);
            }, 0);
        }
    }

    /**
     * 绑定编辑器相关事件
     */
    function bindEditorEvents() {
        // 发布按钮
        $(document).off('click.editorSubmit', '.release-btn')
            .on('click.editorSubmit', '.release-btn', function() {
                const $btn = $(this);
                const $editor = $btn.closest('.lzl_editor_container');
                const $editableDiv = $editor.find('.edui-container');
                const containerId = $editor.closest('[data-container-id]').attr('data-container-id');

                handleEditorSubmit($btn, $editor, $editableDiv, containerId);
            });
    }

    /**
     * 处理编辑器提交
     * @param {jQuery} $btn - 提交按钮
     * @param {jQuery} $editor - 编辑器容器
     * @param {jQuery} $editableDiv - 可编辑区域
     * @param {string} containerId - 容器ID
     */
    function handleEditorSubmit($btn, $editor, $editableDiv, containerId) {
        // 防止重复提交
        if ($btn.prop('disabled')) {
            return;
        }

        try {
            // 设置加载状态
            setButtonLoading($btn, true);

            // 获取编辑器信息
            const editorInfo = activeEditors.get(containerId);
            if (!editorInfo) {
                throw new Error('编辑器信息丢失');
            }

            // 获取内容
            let content = $editableDiv.html().trim();

            // 如果是回复用户，格式化为 @用户名：评论内容
            if (editorInfo.type === 'building') {
                content = CommentUtils.stripHtml(content);
                // 提取@用户名后面的内容
                if (editorInfo.replyToUser) {
                    // 移除@用户名部分，获取纯评论内容
                    const pureContent = content.replace(new RegExp(`^@${editorInfo.replyToUser}\\s*`), '').trim();
                    // 重新格式化为 @用户名：评论内容
                    // content = `@${editorInfo.replyToUser}：${pureContent}`;

                    content = pureContent;
                }
            } else {
                content = CommentUtils.stripHtml(content);
            }

            // 验证内容
            const validation = CommentUtils.validateComment(content);
            if (!validation.valid) {
                throw new Error(validation.message);
            }

            // 获取父评论ID - 改进的查找逻辑
            let $parentComment = $editor.closest('.reply-list');
            let parentId = $parentComment.attr('data-comment-id');

            // 如果在reply-list中没找到，尝试在其他容器中查找
            if (!parentId) {
                // 尝试从最近的评论容器中查找
                $parentComment = $editor.closest('.p_postlist_c').closest('.reply-list');
                parentId = $parentComment.attr('data-comment-id');
            }

            // 如果还是没找到，尝试从DOM结构中推断
            if (!parentId) {
                const $replyContainer = $editor.closest('.comment-reply');
                if ($replyContainer.length) {
                    $parentComment = $replyContainer.closest('.reply-list');
                    parentId = $parentComment.attr('data-comment-id');
                }
            }

            console.log('查找父评论ID:', parentId);

            //查找上一级评论
            const reply_to_comment = $editor.closest('.lzl_editor_container').data("comment-id");
            console.log(reply_to_comment)

            if (parentId) {
                // 添加回复
                $.ajax({
                    type: "POST",
                    url: "/iCommunity/reply",
                    data: {parent_id: parentId, content: content, reply_to_comment: reply_to_comment, mentioned_users: ''},
                    async: false,
                    success: function (data) {
                        if (data.code == 0) {
                            //失败
                            layer.msg(data.msg, { icon: 2 });
                        } else {
                            //成功
                            layer.msg(data.msg, {
                                icon: 1,
                                time: 2000
                            }, function () {
                                window.location.href = data.url;
                            });
                        }
                    },
                    error: function () {
                        alert("异常！");
                        return false;
                    }
                });

            } else {
                throw new Error('无法确定父评论，请检查页面结构');
            }

            // 移除编辑器
            removeEditor(containerId);

            // 显示回复区域（如果之前是隐藏的）
            const $commentReply = $parentComment.find('.comment-reply');
            if ($commentReply.is(':hidden')) {
                $commentReply.slideDown(300);
                $parentComment.find('.lzl_link_unfold').hide();
                $parentComment.find('.pack-reply').show();
            }

        } catch (error) {
            CommentUtils.showMessage(error.message, 'error');
        } finally {
            // 恢复按钮状态
            setButtonLoading($btn, false);
        }
    }

    /**
     * 移除编辑器
     * @param {string} containerId - 容器ID
     */
    function removeEditor(containerId) {
        const editorInfo = activeEditors.get(containerId);
        if (editorInfo) {
            editorInfo.$editor.fadeOut(200, function() {
                $(this).remove();
            });
            activeEditors.delete(containerId);
        }
    }

    /**
     * 绑定@用户名保护
     * @param {jQuery} $editableDiv - 可编辑区域
     * @param {string} userName - 用户名
     */
    function bindAtUserProtection($editableDiv, userName) {
        $editableDiv.off('keydown.protectAt').on('keydown.protectAt', function(e) {
            // 检测删除键
            if (e.keyCode === 8 || e.keyCode === 46) {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const $atSpan = $(this).find(`span[data-showname="${userName}"]`);

                    if ($atSpan.length > 0) {
                        const atSpanNode = $atSpan[0];
                        const startContainer = range.startContainer;

                        // 检查是否要删除@用户名
                        if (startContainer === atSpanNode ||
                            startContainer.parentNode === atSpanNode ||
                            (startContainer.nodeType === Node.TEXT_NODE &&
                             startContainer.parentNode === atSpanNode)) {
                            e.preventDefault();
                            return false;
                        }
                    }
                }
            }
        });
    }

    /**
     * 设置光标到元素末尾
     * @param {Element} element - DOM元素
     */
    function setCursorToEnd(element) {
        if (element) {
            const range = document.createRange();
            const sel = window.getSelection();
            range.selectNodeContents(element);
            range.collapse(false);
            sel.removeAllRanges();
            sel.addRange(range);
        }
    }

    /**
     * 设置按钮加载状态
     * @param {jQuery} $btn - 按钮元素
     * @param {boolean} loading - 是否加载中
     */
    function setButtonLoading($btn, loading) {
        if (loading) {
            $btn.prop('disabled', true)
                .data('original-text', $btn.text())
                .text('发送中...');
        } else {
            $btn.prop('disabled', false)
                .text($btn.data('original-text') || $btn.text().replace('发送中...', 'SEND'));
        }
    }

    /**
     * 绑定滚动到回复区域事件
     */
    function bindScrollToReply() {
        $('.scroll-to-reply').off('click.scrollToReply').on('click.scrollToReply', function(e) {
            e.preventDefault();
            CommentUtils.scrollToElement('#reply');
        });
    }

    /**
     * 自动展开所有回复区域
     */
    function autoExpandReplies() {
        // 查找所有有回复的评论
        $('.reply-list').each(function() {
            const $container = $(this);
            const $commentReply = $container.find('.comment-reply');
            const $unfoldBtn = $container.find('.lzl_link_unfold');
            const $packBtn = $container.find('.pack-reply');
            const replyCount = $commentReply.find('.comment-reply-item').length;

            // 如果有回复，确保展开状态
            if (replyCount > 0) {
                // 隐藏展开按钮，显示收起按钮
                $unfoldBtn.hide();
                $packBtn.show();
                // 确保回复区域是显示的
                $commentReply.show();

                // 更新按钮文字
                const $unfoldSpan = $unfoldBtn.find('span');
                const $packSpan = $packBtn.find('span');
                if ($unfoldSpan.length) {
                    $unfoldSpan.text(`Reply (${replyCount})`);
                }
                if ($packSpan.length) {
                    $packSpan.text(`Fold (${replyCount})`);
                }
            } else {
                // 没有回复时，显示展开按钮，隐藏收起按钮
                $unfoldBtn.show();
                $packBtn.hide();
                // 隐藏回复区域
                $commentReply.hide();

                // 更新按钮文字
                const $unfoldSpan = $unfoldBtn.find('span');
                if ($unfoldSpan.length) {
                    $unfoldSpan.text('Reply (0)');
                }
            }
        });
    }

    /**
     * 清理所有活动编辑器
     */
    function cleanup() {
        activeEditors.forEach((editorInfo, containerId) => {
            removeEditor(containerId);
        });
        activeEditors.clear();
    }

    // 公开API
    return {
        init,
        cleanup,
        removeEditor,
        toggleReplyEditor,
        autoExpandReplies,
        // 调试用
        get activeEditors() {
            return activeEditors;
        }
    };
})();
