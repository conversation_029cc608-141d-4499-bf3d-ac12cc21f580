<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<style>
    .services-row .bor_cen tbody tr{
        height: 20px;
        line-height: 20px;
    }

    .services-row .bor_cen .mid_01, .services-row .bor_cen{
        background-color: #f6f6f6;
        width: 80%;
        margin: 0 auto;
    }
</style>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">订单列表</a></p>
                <a href="{:url('add')}" class="add-button">创建订单</a>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">订单编号</td>
                            <td class="mid_one">Email</td>
                            <td class="mid_one">产品</td>
                            <td class="mid_one">服务</td>
                            <td class="mid_one">创建时间</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>

                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr class="mid_02 toggle-row" data-target="services-{$vo.id}">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one">{$vo.order_no}</td>
                            <td class="mid_one">{$vo.email}</td>
                            <td class="mid_one">{$vo.product_name}</td>
                            <td class="mid_one">{$vo.service_name}</td>
                            <td class="mid_one">{$vo.create_time}</td>
                            <td class="mid_s">
                                <a href="{:url('del', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit', ['id'=>$vo['id']])}" class="basic">修改</a>
                                <a href="{:url('files', ['id'=>$vo['id'], 'type'=>0])}" class="compile">文件</a>
                                <a href="{:url('add_service', ['id'=>$vo['id']])}" class="compile">添加后续服务</a>
                            </td>
                        </tr>

                        <tr class="services-row" style="display: none;">
                            <td colspan="8">
                                <table class="bor_cen services" id="services-{$vo.id}" style="display:none;">
                                    <thead>
                                        <tr class="mid_01" style="height: 20px;line-height: 20px;">
                                            <td class="mid_one"></td>
                                            <td class="mid_one">订单编号</td>
                                            <td class="mid_one">服务</td>
                                            <td class="mid_one">创建时间</td>
                                            <td class="mid_s">操作</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {volist name="vo.services" id="v"}
                                        <tr class="mid_02">
                                            <td class="mid_one">{$k}</td>
                                            <td class="mid_one">{$v.order_no}</td>
                                            <td class="mid_one">{$v.service_name}</td>
                                            <td class="mid_one">{$v.create_time}</td>
                                            <td class="mid_s">
                                                <a href="{:url('del_service', ['id'=>$v['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                <a href="{:url('edit_service', ['id'=>$v['id']])}" class="basic">修改</a>
                                                <a href="{:url('files', ['id'=>$v['id'], 'type'=>1])}" class="compile">文件</a>
                                            </td>
                                        </tr>
                                    {/volist}
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                {if $List->hasPages()}
                <div class="interpret">
                    {$List|raw}
                </div>
                {/if}
            </div>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        $(document).ready(function() {
            $('.toggle-row').click(function(e) {
                if($(e.target).closest('.mid_s').length) return;

                const targetId = $(this).data('target');
                $('#' + targetId).toggle();
                $('#' + targetId).parents(".services-row").toggle();
            });
        });
    </script>
</body>
</html>