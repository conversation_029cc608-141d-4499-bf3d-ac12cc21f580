<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>

<style>
    .class_con label{
        color: #2160ee
    }
    .class_con img{
        height: auto;
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;审核回复
                <a href="{:url('reply')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('examine_reply')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />
                <input type="hidden" name="post_id" value="{$getone.post_id}" />

                <div class="class_con">
                    <label>内容：</label>
                    <div>{$getone.content|raw}</div>
                </div>

                <div class="class_con">
                    <label>原因：</label>
                    <textarea name="fail_reason" placeholder="审核不通过时填写原因"></textarea>
                </div>

                <div class="de_y" style="width: 310px;">
                    <button class="de_y_l" type="submit" data-status="1">审核通过</button>
                    <button class="de_y_l" type="submit" data-status="2" style="margin-left: 15px;background: #bf4d4d;border: 1px solid #bf4d4d;">审核不通过</button>
                    <a href="{:url('reply')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        $(function() {
            const $form = $('#formId');

            // 为所有提交按钮绑定点击事件
            $('.de_y_l[type="submit"]').on('click', async function(e) {
                e.preventDefault(); // 阻止默认表单提交

                const $btn = $(this);
                const status = $btn.data('status');

                // 禁用所有提交按钮
                $('.de_y_l[type="submit"]').prop('disabled', true);

                try {
                    const formData = new FormData($form[0]);
                    formData.append("status", status);

                    const response = await fetch($form.attr('action'), {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'Accept': 'application/json',
                        },
                    });
                    const data = await response.json();

                    if (data.code === 1) {
                        layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                            window.location.href = data.url;
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                } catch (error) {
                    console.error('Error:', error);
                    layer.msg("提交失败，请重试", { icon: 2 });
                } finally {
                    // 恢复按钮状态
                    $('.de_y_l[type="submit"]').prop('disabled', false);
                }
            });
        });
    </script>

</body>
</html>