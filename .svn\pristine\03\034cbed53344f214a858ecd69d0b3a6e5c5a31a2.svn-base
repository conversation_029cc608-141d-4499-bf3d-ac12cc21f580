<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Banner extends Common
{
    public function index()
    {
        $List = Db::name('Banner')
            ->order("sort asc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$_FILES['image']['name']){
                $this->error("必填项未填！");
            }

            $data['image'] = $this->upload(request()->file("image"));
            if($_FILES['smt_image']['name']) $data['smt_image'] = $this->upload(request()->file("smt_image"));

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Banner")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));
            if($_FILES['smt_image']['name']) $data['smt_image'] = $this->upload(request()->file("smt_image"));

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Banner")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Banner")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Banner")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

}
