<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Contact extends Common
{
    public function index()
    {
        $List = Db::name('Contact')
            ->order("sort asc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("必填项未填！");
            }

            $data['icon'] = $this->upload(request()->file("icon"));

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Contact")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("必填项未填！");
            }

            if($_FILES['icon']['name']) $data['icon'] = $this->upload(request()->file("icon"));

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Contact")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Contact")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Contact")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

}
