<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加试用券
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="type" value="{$type}" />

                <div class="class_con">
                    <label>名称：</label>
                    <input type="text" name="name" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>说明：</label>
                    <input type="text" name="description" />
                    <span class="must-input">*</span>
                </div>

                {if $type==0}
                <div class="class_con">
                    <label>产品：</label>
                    <select name="main_id" class="l_xiang">
                        {volist name="list" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
                    <span class="must-input">*</span>
                </div>
                {else }
                <div class="class_con">
                    <label>服务：</label>
                    <select name="main_id" class="l_xiang">
                        {volist name="list" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
                    <span class="must-input">*</span>
                </div>
                {/if}

                <div class="class_con">
                    <label>开始时间：</label>
                    <input type="text" name="start_time" id="start_time" value="{:date('Y-m-d H:i:s')}" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>结束时间：</label>
                    <input type="text" name="end_time" id="end_time" value="" />
                    <span class="must-input">*</span>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        laydate.render({
            elem: '#start_time',  // 绑定元素
            type: 'datetime',     // 选择日期和时间
            format: 'yyyy-MM-dd HH:mm:ss'  // 自定义格式：年-月-日 时:分
        });

        laydate.render({
            elem: '#end_time',  // 绑定元素
            type: 'datetime',     // 选择日期和时间
            format: 'yyyy-MM-dd HH:mm:ss'  // 自定义格式：年-月-日 时:分
        });
    </script>

</body>
</html>