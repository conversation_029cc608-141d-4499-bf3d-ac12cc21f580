<?php
// 应用公共文件

use think\facade\Db;

/**
 * 随机生成四位字符串的salt
 * 也能够依据理论状况应用6位或更长的salt
 */
function generateSalt()
{
    $str = "";
    // 应用随机形式生成一个四位字符
    $chars = array_merge(range('A', 'Z'), range('a', 'z'), range('0', '9'));
    for ($i = 0; $i < 4; $i++) {
        $str .= $chars[mt_rand(0, count($chars) - 1)];
    }
    return $str;
}

/**
 * 明码生成
 * 应用两层hash，将salt加在第二层
 * sha1后再加salt而后再md5
 */
function generateHashPassword($password, $salt)
{
    return md5(sha1($password) . $salt);
}

//获取全球所有国家
function getCountry(){
    $country = Db::name("Country")->column("id, en_name, cn_name");
    return $country;
}