<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

use think\facade\Cache;

use app\validate\Quote as QuoteValidate;
use think\exception\ValidateException;

class Quote extends Common
{
    //未登录咨询
    public function index()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            //验证数据
            try {
                validate(QuoteValidate::class)->scene('front-quote')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            //验证邮箱验证码
            $cacheCaptcha = Cache::get('mail_captcha_' . $data['email']);
            if (!empty($cacheCaptcha) && $cacheCaptcha == $data['captcha']) {
                // 验证成功后删除验证码
                // Cache::set('mail_captcha_' . $data['email'], null);
            } else {
                $this->error("Verification code error or expired!");
            }

            $data['ip'] = $this->request->ip();
            $data['create_time'] = date("Y-m-d H:i:s");
            $id = Db::name("Quote")->strict(false)->insertGetId($data);
            if ($id) {
                //提交成功后删除验证码
                Cache::set('mail_captcha_' . $data['email'], null);

                $this->success('submit success', "/");
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }

        if(session("userId")) {
            return redirect('/quote/logged');
        }

        $country = getCountry();
        $product = Db::name("Product")->column("id, name");

        $product_id = input("product_id", 0);
        $product_services = [];
        if($product_id){
            $product_services = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["product_id"=>$product_id, "type"=>1])
                ->order("pr.sort asc")
                ->select();
        }

        $service_id = input("service_id", 0);
        if($service_id){
            $product_id = Db::name("Product_relation")->alias("pr")
                ->field("p.id, p.name")
                ->leftjoin("Product p", "pr.related_id=p.id")
                ->where(["related_id"=>$service_id, "type"=>1])
                ->order("pr.sort asc")
                ->value("product_id");

            $product_services = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["product_id"=>$product_id, "type"=>1])
                ->order("pr.sort asc")
                ->select();
        }

        return view("", [
            'country' => $country,
            'product' => $product,
            "product_services" => $product_services,
            "product_id" => $product_id,
            "service_id" => $service_id,
        ]);

    }

    /**
     * 根据 product_id 获取 service 列表
     */
    public function getServices($product_id)
    {
        $service = Db::name("Product_relation")->alias("pr")
            ->field("s.id, s.name")
            ->leftjoin("Service s", "pr.related_id=s.id")
            ->where(["product_id"=>$product_id, "type"=>1])
            ->order("pr.sort asc")
            ->select();

        return json([
            "code" => 1,
            "data" => $service
        ]);
    }


    //已登录咨询
    public function logged(){
        if ($this->request->isPost()) {
            $data = $this->request->post();

            if(!session("userId")){
                $this->error("Login first");
            }

            // if (empty($data['agree'])) {
            //     $this->error('Please agree to the terms');
            // }

            //将用户信息填充到quote表中
            $data['user_id'] = session("userId");

            $userData = Db::name("User")->field("email,first_name,last_name,country,organization,title,phone,express_address,city,express_postcode")->where("id", $data['user_id'])->find();
            $data = array_merge($data, $userData);  //如果有重复键名，$userData 的值会覆盖 $data 的值

            $data['ip'] = $this->request->ip();
            $data['create_time'] = date("Y-m-d H:i:s");
            $id = Db::name("Quote")->strict(false)->insertGetId($data);
            if ($id) {
                $this->success('submit success', "/");
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }

        if(!session("userId")) {
            return redirect('/quote');
        }

        $product = Db::name("Product")->column("id, name");

        $product_id = input("product_id", 0);
        $product_services = [];
        if($product_id){
            $product_services = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["product_id"=>$product_id, "type"=>1])
                ->order("pr.sort asc")
                ->select();
        }

        $service_id = input("service_id", 0);
        if($service_id){
            $product_id = Db::name("Product_relation")->alias("pr")
                ->field("p.id, p.name")
                ->leftjoin("Product p", "pr.related_id=p.id")
                ->where(["related_id"=>$service_id, "type"=>1])
                ->order("pr.sort asc")
                ->value("product_id");

            $product_services = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["product_id"=>$product_id, "type"=>1])
                ->order("pr.sort asc")
                ->select();
        }

        return view("", [
            'product' => $product,
            "product_services" => $product_services,
            "product_id" => $product_id,
            "service_id" => $service_id,
        ]);
    }

}
