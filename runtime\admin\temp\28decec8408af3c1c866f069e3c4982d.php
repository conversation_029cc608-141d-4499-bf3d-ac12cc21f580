<?php /*a:3:{s:65:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\order\service.html";i:1748315620;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1747812241;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<!-- tiny编辑器 -->
<script src="/static/admin/js/tinymce/tinymce.min.js"></script>
<script>
    // 通用配置
    const defaultTinyMCEConfig = {
        language: 'zh_CN',
        license_key: 'gpl',
        plugins: 'autoresize code image media link lists',
        height: '250px',
        toolbar: 'code | customMediaUpload image media | undo redo | bold italic | alignleft aligncenter alignright alignjustify | fontsize | link | numlist bullist',

        file_picker_types: 'media image', // 允许选择媒体和图片文件
        images_upload_url: '/admin/tinymceImage', // 图片上传接口

        min_height: 200,      // 最小高度200px
        max_height: 600,      // 最大高度600px
        // autoresize_bottom_margin: 10, // 底部间距（可选）
        // autoresize_overflow_padding: 10, // 溢出内边距（可选）
        promotion: false,  // 禁用推广提示
        content_style: 'img {max-width: 100%; height: auto;}', // 直接内联样式

        setup: function(editor) {
            editor.ui.registry.addButton('customMediaUpload', {
                icon: 'upload',
                tooltip: '上传视频',
                onAction: function() {
                    editor.windowManager.open({
                        title: '上传视频',
                        body: {
                            type: 'panel',
                                items: [{
                                type: 'htmlpanel',
                                html: '<input type="file" id="tinymce-media-upload" accept="video/*,audio/*">'
                            }]
                        },
                        buttons: [
                            {
                                type: 'cancel',
                                name: 'cancel',
                                text: 'Cancel'
                            },
                            {
                                type: 'submit',
                                name: 'save',
                                text: 'Upload',
                                primary: true
                            }
                        ],
                        onSubmit: function(api) {
                            var fileInput = document.getElementById('tinymce-media-upload');
                            if (fileInput.files.length > 0) {
                                var formData = new FormData();
                                formData.append('file', fileInput.files[0]);

                                fetch('/admin/tinymceMedia', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    console.log(data)
                                    if (data.location) {
                                        editor.insertContent(
                                            data.location.match(/\.(mp4|webm|ogg|mov|avi)$/) ?
                                            '<video controls src="' + data.location + '"></video>' :
                                            '<audio controls src="' + data.location + '"></audio>'
                                        );
                                        api.close();
                                    }
                                });
                            }
                        }
                    });
                }
            });
        }
    };

    // 封装初始化函数
    function initTinyMCE(selector, customConfig = {}) {
        tinymce.init({
            selector: selector,
            ...defaultTinyMCEConfig, // 通用配置
            ...customConfig, // 自定义配置
        });
    }
</script>
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="<?php echo url('Public/main'); ?>">首页</a>&nbsp;>&nbsp; <a href="">后续服务列表</a>
                    <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
                </p>
                <a href="<?php echo url('add_service', ['order_id'=>$order_id]); ?>" class="add-button">添加后续服务</a>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">订单编号</td>
                            <td class="mid_one">服务</td>
                            <td class="mid_one">创建时间</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>
                    <tbody>
                    <?php if(is_array($List) || $List instanceof \think\Collection || $List instanceof \think\Paginator): $k = 0; $__LIST__ = $List;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                        <tr  class="mid_02">
                            <td class="mid_one"><?php echo htmlentities((string) $k); ?></td>
                            <td class="mid_one"><?php echo htmlentities((string) $vo['order_no']); ?></td>
                            <td class="mid_one"><?php echo htmlentities((string) $vo['service_name']); ?></td>
                            <td class="mid_one"><?php echo htmlentities((string) $vo['create_time']); ?></td>
                            <td class="mid_s">
                                <a href="<?php echo url('del_service', ['id'=>$vo['id']]); ?>" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="<?php echo url('edit_service', ['id'=>$vo['id']]); ?>" class="basic">修改</a>
                            </td>
                        </tr>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                    </tbody>
                </table>

                <div class="interpret">
                    <?php echo $List; ?>
                </div>
            </div>
        </div>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script>
    // 初始化编辑器
    initTinyMCE('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const form = document.getElementById('formId');
    const submitBtn = document.getElementById('submitBtn');

    // 初始绑定提交事件
    if (form) {
        form.addEventListener('submit', handleSubmit);
    }

    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 解绑提交事件（避免重复提交）
        form.removeEventListener('submit', handleSubmit);

        // 禁用提交按钮（防止重复点击）
        submitBtn.disabled = true;
        submitBtn.textContent = '提交中...';

        try {
            // 同步 TinyMCE 内容（如果有）
            if (typeof tinymce !== 'undefined') {
                tinymce.triggerSave();
            }

            const formData = new FormData(form);

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });

            const data = await response.json();
            // console.log(data)
            // return false
            if (data.code === 1) {
                //提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                //提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.textContent = '确定';
            form.addEventListener('submit', handleSubmit);
        }
    }
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const deselectAllBtn = document.getElementById('deselectAllBtn');
        const userSelect = document.querySelector('.user-multi-select');
        const selectedCount = document.getElementById('selectedCount');

        // 全选按钮
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                Array.from(userSelect.options).forEach(option => {
                    option.selected = true;
                });
                updateSelectedCount();
            });
        }

        // 取消全选按钮
        if(deselectAllBtn){
            deselectAllBtn.addEventListener('click', function() {
                Array.from(userSelect.options).forEach(option => {
                    option.selected = false;
                });
                updateSelectedCount();
            });
        }

        // 修改选择行为 - 不需要按Ctrl键
        if(userSelect){
            userSelect.addEventListener('mousedown', function(e) {
                if (e.target.tagName === 'OPTION') {
                    e.preventDefault();
                    e.target.selected = !e.target.selected;

                    // 触发更新
                    const event = new Event('change');
                    userSelect.dispatchEvent(event);
                }
            });

            // 用户选择变化时更新计数
            userSelect.addEventListener('change', updateSelectedCount);

            // 初始化计数
            updateSelectedCount();

            function updateSelectedCount() {
                const selectedOptions = Array.from(userSelect.selectedOptions);
                selectedCount.textContent = selectedOptions.length;
            }
        }

    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>
</body>
</html>