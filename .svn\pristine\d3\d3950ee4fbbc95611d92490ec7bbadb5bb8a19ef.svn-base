
document.addEventListener('DOMContentLoaded', function () {
    const termsService = document.getElementById('terms_service');
    const countdownElement = document.getElementById('Countdown');
    const agreeButton = document.getElementById('btn_agree');
    const closeButton = document.querySelector('.close_terms_service');
    const agree_privacy = document.getElementById('agree');
    const privacyLink = document.getElementById('privacy_link');
    const btnDisagree = document.getElementById('btn_disagree');
    let seconds = 5;
    let countdown;

    // 根据当前页面路径生成唯一的缓存键
    const currentPage = window.location.pathname;
    const cacheKey = 'hasAgreed_' + currentPage.replace(/[^a-zA-Z0-9]/g, '_');

    // 检查缓存中是否有记录
    let hasCachedAgreement = localStorage.getItem(cacheKey) === 'true';

    // 页面加载时根据缓存设置复选框状态
    if (hasCachedAgreement) {
        agree_privacy.checked = true;
    }

    // 默认隐藏条款弹窗
    termsService.style.display = 'none';

    // 点击隐私协议链接时弹出条款弹窗
    privacyLink.addEventListener('click', function (e) {
        e.preventDefault();
        termsService.style.display = 'block';

        // 检查是否有缓存记录
        hasCachedAgreement = localStorage.getItem(cacheKey) === 'true';

        if (hasCachedAgreement) {
            // 有缓存记录：不启用倒计时，同意按钮不可点击
            agreeButton.textContent = 'Already Agreed';
            agreeButton.disabled = true;
            agreeButton.classList.remove('active');
            countdownElement.style.display = 'none';
        } else {
            // 没有缓存记录：启用倒计时
            startCountdown();
        }
    });

    // 直接点击checkbox时弹出条款弹窗
    agree_privacy.addEventListener('click', function (e) {
        e.preventDefault();
        termsService.style.display = 'block';

        // 检查是否有缓存记录
        hasCachedAgreement = localStorage.getItem(cacheKey) === 'true';

        if (hasCachedAgreement) {
            // 有缓存记录：不启用倒计时，同意按钮不可点击
            agreeButton.textContent = 'Already Agreed';
            agreeButton.disabled = true;
            agreeButton.classList.remove('active');
            countdownElement.style.display = 'none';
        } else {
            // 没有缓存记录：启用倒计时
            startCountdown();
        }
    });

    function startCountdown() {
        seconds = 5;
        countdownElement.textContent = seconds;
        countdownElement.style.display = 'inline';
        agreeButton.disabled = true;
        agreeButton.classList.remove('active');
        agreeButton.textContent = 'Agree and Continue (' + seconds + 's)';

        countdown = setInterval(() => {
            seconds--;
            countdownElement.textContent = seconds;
            agreeButton.textContent = 'Agree and Continue (' + seconds + 's)';

            if (seconds <= 0) {
                clearInterval(countdown);
                countdownElement.style.display = 'none';
                agreeButton.disabled = false;
                agreeButton.classList.add('active');
                agreeButton.textContent = 'Agree and Continue';
            }
        }, 1000);
    }

    // 点击同意按钮
    agreeButton.addEventListener('click', function () {
        if (this.disabled) return;

        clearInterval(countdown);

        // 勾选复选框
        agree_privacy.checked = true;

        // 保存到缓存
        localStorage.setItem(cacheKey, 'true');
        hasCachedAgreement = true;

        // 关闭弹窗
        termsService.style.display = 'none';
    });

    // 关闭弹窗时停止倒计时
    closeButton.addEventListener('click', function () {
        clearInterval(countdown);
        termsService.style.display = 'none';
    });

    // 点击取消按钮
    btnDisagree.addEventListener('click', function () {
        clearInterval(countdown);
        termsService.style.display = 'none';

        // 取消勾选复选框
        agree_privacy.checked = false;

        // 清除缓存记录，恢复之前状态
        localStorage.removeItem(cacheKey);
        hasCachedAgreement = false;
    });
});