<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改页面</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改页面
                <a href="{:url('index')}" class="de_y_r">返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit')}" method="post" id="formId" enctype="multipart/form-data">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>父页面：</label>
                    <select class="l_xiang" name="page_id">
                        <option value="0">请选择</option>
                        {volist name="pages" id="vo"}
                        <option value="{$vo.id}" {if condition="$getone.page_id eq $vo.id"}selected{/if}>{$vo.name}</option>
                        {/volist}
                    </select>
                </div>

                <div class="class_con">
                    <label>页面名称：</label>
                    <input type="text" name="name" value="{$getone.name}" />
                </div>

                <div class="class_con">
                    <label>图片：</label>
                    <input type="file" name="image" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽1662px 高502px。格式：png、jpg、jpeg）
                </div>
                {if condition="$getone.image"}
                <div class="class_con">
                    <img src="{$getone.image}">
                </div>
                {/if}

                <div class="class_con">
                    <label>手机端图片：</label>
                    <input type="file" name="image_smt" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽690px 高348px。格式：png、jpg、jpeg）
                </div>
                {if condition="$getone.image_smt"}
                <div class="class_con">
                    <img src="{$getone.image_smt}">
                </div>
                {/if}

                <div class="class_con editor-container">
                    <label>描述：</label>
                    <textarea name="content" class="tiny-editor" style="height: 300px;">{$getone.content}</textarea>
                </div>

                <div class="class_con">
                    <label>SEO页面标题：</label>
                    <textarea name="seo_title">{$getone.seo_title}</textarea>
                </div>
                <div class="class_con">
                    <label>SEO页面描述：</label>
                    <textarea name="seo_description">{$getone.seo_description}</textarea>
                </div>
                <div class="class_con">
                    <label>SEO页面关键词：</label>
                    <textarea name="seo_keywords">{$getone.seo_keywords}</textarea>
                </div>

                <div class="class_con">
                    <label>排序：</label>
                    <input type="text" name="sort" value="{$getone.sort}" />
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

</body>
</html>