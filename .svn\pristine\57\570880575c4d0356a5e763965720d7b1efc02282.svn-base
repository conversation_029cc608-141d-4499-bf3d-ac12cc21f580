<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>{$tdk.seo_title?$tdk.seo_title:$tdk.name}</title>
    <meta name="keywords" content="{$tdk.seo_keywords}" />
    <meta name="description" content="{$tdk.seo_description}" />

    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        {$tdk.top_title}
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="Resources-banner">
                <h1 class="text-2xl md:text-5xl mb-5 md:mb-8 Roboto_Bold">{$tdk.title}</h1>
                <div class="rounded-tl-xl rounded-tr-xl overflow-hidden">
                    <picture>
                        <source media="(max-width: 600px)" srcset="{$tdk.image_smt}">
                        <source media="(min-width: 601px)" srcset="{$tdk.image}">
                        <img src="{$tdk.image}" alt="">
                    </picture>
                </div>
            </div>
        </div>
    </section>

    <main class="w-11/12 mx-auto md:w-10/12 pb-10">
        <div class="discover-news pt-5 md:pt-10">
            <!-- 头部分类 -->
            <div class="w-full overflow-x-auto mb-5 md:mb-10">
                <div class="resources flex items-center flex-nowrap min-w-max gap-x-2.5 text-sm md:gap-x-5 md:text-2xl">
                    {volist name="resources" id="vo" key="k"}
                    <!-- <span {if $k==1}class="active"{/if}> -->
                    <span data-tab="{$k}" class="tab-item">
                        {$vo.name}
                    </span>
                    {/volist}
                </div>
            </div>

            <!-- 内容 -->
            <div class="resources-content-all">
                {volist name="resources" id="vo" key="k"}
                <div data-tab="{$k}" class="resources-item bg-[#f8fdff] border border-[#c4d7ff] rounded-xl px-5 pb-5 md:p-[2.5rem]">
                    <ul role="list" class="grid grid-cols-1 mb-5 md:mb-11 md:grid-cols-2 md:gap-x-[5rem] relative resources-list">
                        {volist name="vo.resource" id="v"}
                        <li class="border-[#e0eaff] border-b py-4 flex items-center justify-between gap-x-2 md:py-6">
                            <div class="flex items-center flex-1">
                                <span class="uppercase text-xs border border-[#155797] text-[#155797] rounded-4xl px-2 py-0.5 mr-2 scale-75 md:scale-100">
                                    {$v.file_type}
                                </span>
                                <h3 class="text-[#666] text-sm md:text-xl md:ml-2.5">
                                    <a href="{$v.file}" download="{$v.file_name}" class="line-clamp-1">{$v.name}</a>
                                </h3>
                            </div>
                            <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-[rgba(21,87,151,0.1)] border border-[#e0eaff] flex items-center justify-center scale-75 md:scale-100">
                                <a href="{$v.file}" download="{$v.file_name}">
                                    <img src="__IMG__/icons/xiazai1.png" alt="" class="w-3.5 md:w-[1.0625rem]">
                                </a>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                    <div class="text-base">
                        <button type="button" class="rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:py-5">
                            SHOW MORE
                        </button>
                    </div>
                </div>
                {/volist}
            </div>
        </div>
    </main>

    {include file="public:footer"}

    {include file="public:foot"}
    <script src="__JS__/TabSwitch.js"></script>

    <script>
        initTabSwitch('.tab-item', '.resources-item');

        // 获取所有新闻内容项
        const newsItems = document.querySelectorAll('.resources-item');

        // 处理每个新闻内容项
        newsItems.forEach(item => {
            const listItems = item.querySelectorAll('li');
            const showMoreBtn = item.querySelector('button');

            // 初始只显示4条
            if(listItems.length > 14) {
                for(let i = 14; i < listItems.length; i++) {
                    listItems[i].style.display = 'none';
                }
                showMoreBtn.style.display = 'block';
            } else {
                showMoreBtn.style.display = 'none';
            }

            // 点击按钮显示所有并隐藏自身
            showMoreBtn.addEventListener('click', () => {
                listItems.forEach(li => li.style.display = '');
                showMoreBtn.style.display = 'none';
            });
        });
    </script>

</body>

</html>