<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Coupon extends Common
{
    public function index()
    {
        $coupon_type = input('coupon_type') ?? 0;

        $where = [
            "del_status" => 0,
            "coupon_type" => $coupon_type,
        ];
        $List = Db::name('Coupon')
            ->where($where)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "coupon_type" => $coupon_type,
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if($data['coupon_type'] == 0){
                //普通试用券
                if(!$data['name'] || !$data['description'] || !$data['start_time'] || !$data['end_time']){
                    $this->error("必填项未填！");
                }

                $data['product_ids'] = isset($data['product_ids'])?json_encode($data['product_ids']):"";
                $data['service_ids'] = isset($data['service_ids'])?json_encode($data['service_ids']):"";

                $data['scope_type'] = isset($data['scope_type'])?1:0;
            } else {
                //灰色试用券
                if(!$data['name'] || !$data['description'] || !$data['points']){
                    $this->error("必填项未填！");
                }
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Coupon")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            $coupon_type = input('coupon_type') ?? 0;

            //产品列表
            $product = Db::name("Product")->column("id, name");
            //服务列表
            $service = Db::name("Service")->column("id, name");

            return view("", [
                "coupon_type" => $coupon_type,
                "product" => $product,
                "service" => $service,
            ]);
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if($data['coupon_type'] == 0){
                //普通试用券
                if(!$data['name'] || !$data['description'] || !$data['start_time'] || !$data['end_time']){
                    $this->error("必填项未填！");
                }

                $data['product_ids'] = isset($data['product_ids'])?json_encode($data['product_ids']):"";
                $data['service_ids'] = isset($data['service_ids'])?json_encode($data['service_ids']):"";

                $data['scope_type'] = isset($data['scope_type'])?1:0;
            } else {
                //灰色试用券
                if(!$data['name'] || !$data['description'] || !$data['points']){
                    $this->error("必填项未填！");
                }
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Coupon")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Coupon")->where("id", $id)->find();
            $getone['product_ids'] = $getone['product_ids']?json_decode($getone['product_ids']):[];
            $getone['service_ids'] = $getone['service_ids']?json_decode($getone['service_ids']):[];

            //产品列表
            $product = Db::name("Product")->column("id, name");
            //服务列表
            $service = Db::name("Service")->column("id, name");

            return view("", [
                "getone" => $getone,
                "product" => $product,
                "service" => $service,
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Coupon")->where("id", $id)->save(["del_status"=>1]);
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }


    //赠送试用券
    public function add_gift(){
        if ($this->request->isPost()) {
            $data = input('post.');

            $coupon = Db::name("Coupon")->where("id", $data['id'])->find();
            if(empty($coupon)){
                $this->error("优惠券不存在！");
            }
            if($coupon['end_time'] < date("Y-m-d H:i:s")) {
                $this->error("优惠券已过期，不能赠送！");
            }

            if($coupon['scope_type'] == 0){
                //单独，发放给选择用户
                if(empty($data['user_ids'])){
                    $this->error("请先选择用户！");
                }

                $user_ids = $data['user_ids'];
            } else {
                //全局，发放给全部用户
                $user_ids = Db::name("User")->where("status", 1)->column("id");
            }

            $to_users = [];
            foreach($user_ids as $val){
                $where = [
                    "coupon_id" => $data['id'],
                    "user_id" => $val,
                ];
                $user_coupon = Db::name("User_coupon")->where($where)->find();
                if(!empty($user_coupon) && $user_coupon['is_used']==0){
                    //已分发，并且未使用，则更新开始结束时间
                    Db::name("User_coupon")->where("id", $user_coupon['id'])->update([
                        "start_time" => $coupon['start_time'],
                        "end_time" => $coupon['end_time'],
                        "create_time" => date("Y-m-d H:i:s"),
                    ]);
                } else {
                    //未分发，或者已使用，则添加
                    $coupon_data = [
                        "coupon_id" => $data['id'],
                        "user_id" => $val,
                        "start_time" => $coupon['start_time'],
                        "end_time" => $coupon['end_time'],
                        "create_time" => date("Y-m-d H:i:s"),
                    ];
                    Db::name("User_coupon")->insert($coupon_data);

                    $to_users[] = $val;
                }
            }

            //发送邮件通知
            if(!empty($to_users)) {
                $failures = []; // 用于存储发送失败的记录
                foreach($to_users as $val){
                    $to_email = Db::name("User")->where("id", $val)->value("email");
                    $data = [
                        'username' => strstr($to_email, '@', true),
                        'coupon_name' => $coupon['name'],
                        'start_time' => $coupon['start_time'],
                        'end_time' => $coupon['end_time'],
                        "site_url" => config('app.site_url')."user/coupon"
                    ];
                    $result = \app\services\MailService::sendEmail("grant-coupon", $to_email, $data);
                    if($result !== true) {
                        $failures[] = [
                            'email' => $to_email,
                            'error' => $result // 这里存储错误信息
                        ];
                    }
                }

                if(!empty($failures)) {
                    // 如果有发送失败的，可以记录日志或返回失败信息
                    \think\facade\Log::error('部分优惠券邮件发送失败', $failures);
                    $this->success("发放成功，但有部分邮件发送失败！", null, ['failures' => $failures]);
                } else {
                    $this->success("发放成功！");
                }
            }

            $this->success("发放成功！");
        } else {
            $id = input('id');
            $getone = Db::name("Coupon")->where("id", $id)->find();

            if($getone['end_time'] < date("Y-m-d H:i:s")){
                $this->error("优惠券已过期，不能赠送！");
            }

            //未获得当前优惠券、已用完、优惠券已过期 的用户列表
            $users = Db::name('User')->alias("u")
                ->leftJoin('User_coupon uc', 'u.id = uc.user_id and uc.coupon_id = ' . $id)
                ->where(function($query) {
                    $query->whereNull('uc.id')  //未领取该优惠券的用户
                        ->whereOr('uc.is_used', 1)  //已使用完该优惠券的用户
                        ->whereOr('uc.end_time', '<', date('Y-m-d H:i:s'));  //已过期的用户
                })
                ->field('u.*')
                ->group('u.id')  // 避免重复用户
                ->select();

            return view("", [
                "getone" => $getone,
                "users" => $users,
            ]);
        }
    }

    //赠送列表
    public function gift(){
        $params = request()->param();
        $where = [["c.coupon_type", "=", 0]];  //普通试用券

        if (!empty($params['keyword'])) {
            $where[] = ["u.email|u.first_name|u.last_name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('User_coupon')->alias("uc")
            ->field("c.name, c.description, u.email, uc.*")
            ->where($where)
            ->leftJoin('Coupon c', 'c.id = uc.coupon_id')
            ->leftJoin('User u', 'u.id = uc.user_id')
            ->order("uc.id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    //兑换记录
    public function exchange(){
        $params = request()->param();
        $where = [["c.coupon_type", "=", 1]];  //灰色试用券

        if (!empty($params['keyword'])) {
            $where[] = ["u.email|u.first_name|u.last_name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('User_coupon')->alias("uc")
            ->field("c.name, c.description, u.email, uc.*")
            ->where($where)
            ->leftJoin('Coupon c', 'c.id = uc.coupon_id')
            ->leftJoin('User u', 'u.id = uc.user_id')
            ->order("uc.id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

}
