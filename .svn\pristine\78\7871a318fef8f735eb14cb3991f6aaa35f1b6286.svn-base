<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<title>后台管理登录</title>
	<link type="text/css" rel="stylesheet" href="__CSS__/index.css"/>
	<link type="text/css" rel="stylesheet" href="__CSS__/public.css?v=1.0"/>
</head>

<style>
	.send-captcha-btn:hover {
		background: #0056b3 !important;
	}
	.send-captcha-btn:disabled {
		background: #6c757d !important;
		cursor: not-allowed !important;
	}
	.opacity-50 {
		opacity: 0.5;
	}
</style>

<body >
	<div class="login_bg">
		<div class="login_con">
			<form name="form1" method="post" id="login_form" onsubmit="return checksubmit();">
				<h1><img src="__IMG__/logo.png" alt="" /></h1>
				<input type="email" value="邮箱" onfocus="if(this.value == '邮箱') this.value = ''" onblur="if(this.value =='') this.value = '邮箱'"  name="email" id="email" class="names" />
				<div style="margin-top: 25px;margin-left: 15%;">
					<input type="text" name="captcha" id="captcha" class="yzheng" placeholder="邮箱验证码" style="flex: 1;" />
					<button type="button" id="SendOtp" class="send-captcha-btn" style="width: 100px; height: 45px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;margin-left: 8px;">发送验证码</button>
				</div>
				<input type="text" value="密码"  name="password" id="password" class="names" onfocus="if(this.value == '密码'){ this.value = ''; this.type='password';}" onblur="if(this.value ==''){ this.value = '密码'; this.type='text';}"  />
				<button type="button" class="login" onclick="checksubmit()">登 录</button>
			</form>
		</div>
	</div>
</body>

<script src="__JS__/jquery.min.js"></script>
<script src="__STATIC__/layer/layer.js"></script>

<script type="text/javascript">
	$(function() {
		// 发送邮件验证码
		$('#SendOtp').click(function() {
			var $btn = $(this); // 保存按钮引用

			var $emailInput = $btn.closest('form').find('input[name="email"]'); // 获取邮箱输入框
			var emailReg = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/; // 邮箱正则

			// 验证邮箱是否为空
			if (!$emailInput.val() || $emailInput.val() == '邮箱') {
				layer.msg('请填写邮箱信息', { icon: 2 });
				$emailInput.focus();
				return;
			}

			// 验证邮箱格式
			if (!emailReg.test($emailInput.val())) {
				layer.msg('请输入有效的邮箱地址', { icon: 2 });
				$emailInput.focus();
				return;
			}

			// 禁用发送按钮
			$btn.prop('disabled', true).addClass('opacity-50');

			// 发送AJAX请求
			$.ajax({
				url: '/send_captcha',
				type: 'POST',
				data: {email: $emailInput.val(), type: 'login'},
				dataType: 'json',
				success: function(data) {
					if (data.code === 1) {
						//发送成功
						layer.msg(data.msg, { icon: 1, time: 2000 });
					} else {
						//发送失败
						layer.msg('错误: ' + data.msg, { icon: 2 });
					}
				},
				error: function(xhr, status, error) {
					layer.msg('发生错误: ' + error, { icon: 2 });
				},
				complete: function() {
					// 无论成功失败，都重新启用按钮
					$btn.prop('disabled', false).removeClass('opacity-50');
				}
			});
		});
	});

	function checksubmit() {
		if ($("#email").val().trim() == "" || $("#email").val() == "邮箱") {
			layer.msg("邮箱不能为空", { icon: 2 })
			$("#email").focus();
			return false;
		}
		if ($("#password").val().trim() == "" || $("#password").val() == "密码") {
			layer.msg("密码不能为空", { icon: 2 })
			$("#password").focus();
			return false;
		}
		if ($("#captcha").val().trim() == "") {
			layer.msg("验证码不能为空", { icon: 2 })
			$("#captcha").focus();
			return false;
		}

		$.ajax({
			type: "POST",
			url: "/admin/Login/index",
			data: $('#login_form').serialize(),
			async: false,
			success: function (data) {
				if (data.code == 0) {
					layer.msg(data.msg, { icon: 2 });
				} else {
					layer.msg(data.msg, {
						icon: 1,
						time: 2000
					}, function () {
						window.location.href = data.url;
					});
				}
			},
			error: function () {
				alert("异常！");
				return false;
			}
		});

		return false;
	}
</script>
</html>
