<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class Product extends Common
{
    //产品页面
    public function index(){
        $this->setTdk(2);

        $product = Db::name("Product")->select();

        return view("", [
            "product" => $product,
        ]);
    }

    //产品-服务页
    public function service()
    {
        $seo_url = input("url");

        $getone = Db::name("Product")->where("seo_url", $seo_url)->find();

        if(empty($getone)) {
            return view("error/404")->code(404);
        }

        //所有产品
        $product = Db::name("Product")->field("id, name, seo_url")->select();

        //服务
        $service = Db::name("Product_relation")->alias("pr")
        ->field("s.*")
        ->leftjoin("Service s", "pr.related_id=s.id")
        ->where(["product_id"=>$getone['id'], "type"=>1])
        ->order("pr.sort asc")
        ->select();

        //资源
        $resource = Db::name("Product_relation")->alias("pr")
        ->field("s.*")
        ->leftjoin("Resource s", "pr.related_id=s.id")
        ->where(["product_id"=>$getone['id'], "type"=>3])
        ->order("pr.sort asc")
        ->select();

        return view("", [
            "getone" => $getone,
            "product" => $product,
            "service" => $service,
            "resource" => $resource,
        ]);
    }

    //产品详情页
    public function detail()
    {
        $seo_url = input("url");

        $getone = Db::name("Product")->where("seo_url", $seo_url)->find();

        if(empty($getone)) {
            return view("error/404")->code(404);
        }

        $getone["testimonial"] = $getone["testimonial"] ?? '{}'; // 如果为空，默认设置为空对象
        $testimonial = json_decode($getone["testimonial"], true);

        $getone["case"] = $getone["case"] ?? '{}'; // 如果为空，默认设置为空对象
        $case = json_decode($getone["case"], true);

        //资源
        $resource = Db::name("Product_relation")->alias("pr")
        ->field("s.*")
        ->leftjoin("Resource s", "pr.related_id=s.id")
        ->where(["product_id"=>$getone['id'], "type"=>3])
        ->order("pr.sort asc")
        ->select();

        return view("", [
            "getone" => $getone,
            "testimonial" => $testimonial,
            "case" => $case,
            "resource" => $resource,
        ]);
    }

}
