<?php
namespace app\home\controller;

use app\home\BaseController;
use think\facade\Db;
use think\facade\View;

use think\facade\Filesystem;
use think\exception\ValidateException;

class Publics extends BaseController
{
    public function setTdk($id){
        $tdk = Db::name("Pages")->where("id", $id)->find();
        $tdk['seo_title'] = $tdk['seo_title']?$tdk['seo_title']:$tdk['name'];

        View::assign("tdk", $tdk);
    }

    //上传文件
    public function upload($file, $dir="")
    {
        if ($file) {
            // 验证文件
            try {
                validate(['file' => [
                    'fileSize' => 5242880,  //5M
                    'fileExt' => 'jpg,gif,png,jpeg,webp,svg'
                ]])->check(['file' => $file]);
            } catch (ValidateException $e) {
                $this->error($e->getMessage());
            }

            $path = Filesystem::disk('public')->putFile($dir, $file);
            //拼接URL路径,结果是 $picPath = /storage/20200825/***.jpg
            $url = Filesystem::getDiskConfig('public', 'url') . '/' . str_replace('\\', '/', $path);

            return $url;
        } else {
            $this->error('No file uploaded');
        }
    }

    public function uploadFile($file, $dir="")
    {
        if ($file) {
            // 验证文件
            try {
                validate(['file' => [
                    'fileSize' => 10*1024*1024,  //10M
                ]])->check(['file' => $file]);
            } catch (ValidateException $e) {
                $this->error($e->getMessage());
            }

            $path = Filesystem::disk('public')->putFile($dir, $file);
            //拼接URL路径,结果是 $picPath = /storage/20200825/***.jpg
            $url = Filesystem::getDiskConfig('public', 'url') . '/' . str_replace('\\', '/', $path);

            return $url;
        } else {
            $this->error('No file uploaded');
        }
    }

 }