<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>{$getone.seo_title?$getone.seo_title:$getone.name}</title>
    <meta name="keywords" content="{$getone.seo_keywords}" />
    <meta name="description" content="{$getone.seo_description}" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet" />
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-7 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]"> Home </a>
                    </li>
                    <li>
                        <span class="text-[#155797]">Services</span>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">{$getone.name}</li>
                </ul>
            </div>
            <div class="flex flex-col gap-5 overflow-hidden">
                <div data-aos="fade-up"
                    class="bg-white rounded-xl flex flex-col gap-y-5 pb-5 border border-[#dae9ff] md:flex-row md:gap-x-5 md:min-h-[35rem] md:pb-0"
                    style="box-shadow: 0 0 10px rgba(0, 0, 0, 0.1)">
                    <figure
                        class="md:order-2 md:w-2/5 md:h-1vh md:bg-gradient-to-r md:from-white md:via-white/0 md:to-white/0 md:relative">
                        <img src="__IMG__/services/04.jpg" alt=""
                            class="object-cover w-full rounded-tr-xl rounded-tl-xl md:absolute md:-z-20 md:h-full md:rounded-br-xl" />
                        <figcaption class="sr-only">{$getone.name}</figcaption>
                    </figure>
                    <div class="px-5 md:order-1 md:w-3/5 md:py-14 md:px-16 md:pr-0">
                        <header class="mb-2.5 px-3 md:mb-6 md:px-0">
                            <h1 class="text-xl Roboto_Bold mb-2 md:text-4xl md:mb-5">
                                {$getone.name}
                            </h1>
                        </header>
                        <div
                            class="text-[#666] text-sm leading-6 mb-6 px-3 md:text-xl md:leading-10 md:min-h-[12.5rem] md:mb-12 md:px-0">
                            <p class="md:line-clamp-6">
                                {$getone.description|raw}
                            </p>
                        </div>

                        <div class="flex flex-col gap-3 md:flex-row md:items-center md:gap-8">
                            <a href="#technical-support"
                                class="flex justify-center border border-[#155797] py-2.5 text-[#155797] text-sm rounded-md relative md:w-[25rem] md:h-[3.75rem] md:items-center md:text-xl">
                                <span> Documents & Downloads </span>
                                <img src="__IMG__/icons/xiazai1.png" alt="箭头图标"
                                    class="w-3 right-7 top-1/2 -translate-1/2 absolute md:w-[1.0625rem]" />
                            </a>
                            <a class="quote-btn cursor-pointer flex justify-center border border-[#f08411] text-[#f08411] text-sm py-2.5 rounded-md relative md:w-[25rem] md:h-[3.75rem] md:items-center md:text-xl"
                                href="{:session('userId') ? '/quote/logged?service_id='. $getone['id'] : 'javascript:;'}"
                                onclick="return {:session('userId') ? 'true' : 'showQuotePopup(\\'service_id=' . $getone['id'] . '\\')'}">
                                <span> Quote </span>
                                <img src="__IMG__/icons/gouwuche-2.png" alt="箭头图标"
                                    class="w-4 right-7 top-1/2 -translate-1/2 absolute md:w-[1.1875rem]" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="w-full">
        <div data-aos="fade-up" class="w-11/12 mx-auto py-10 md:w-10/12 md:pb-20 md:pt-10">
            <div class="text-[#666] text-sm content-txt md:text-xl">
                {$getone.content|raw}
            </div>
        </div>

        <section data-aos="fade-up" class="bg-[#f6f9ff]
        md:bg-[#e0eaff]">
            <div class="w-11/12 mx-auto
            md:w-10/12">
                <header class=" pt-8 pb-6 md:p-0 md:flex md:justify-between md:items-center md:pt-20 md:pb-14">
                    <h2 id="technical-support" class="Roboto_Bold text-2xl text-[#030000]
                   md:text-5xl">Technical Support</h2>
                </header>
                <div class="flex flex-col gap-y-3 pb-8
                md:flex-row md:grid md:grid-cols-3 md:gap-5 md:pb-20">
                    {volist name="resource" id="vo"}
                    <div
                        class="border border-[#dae9ff] bg-white rounded-xl px-5 py-10 flex items-center justify-between gap-x-2">
                        <figure>
                            <div class="w-10 h-10 rounded-full bg-[rgba(21,87,151,0.1)] flex items-center justify-center
                            md:w-14 md:h-14">
                                <img src="__IMG__/icons/diyinwangicon-OL-06.png" alt="" class="w-4
                                md:w-6">
                            </div>
                            <figcaption class="sr-only">Open color page</figcaption>
                        </figure>
                        <p class="text-[#666] text-sm md:text-xl md:ml-2.5">
                            <a href="{$vo.file}" download="{$vo.file_name}" class="line-clamp-1"> Latest Progress of DELLatest Progress of DELL</a>
                        </p>
                        <div class="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center scale-75">
                            <a href="{$vo.file}" download="{$vo.file_name}">
                                <img src="__IMG__/icons/xiazai2.png" alt="" class="w-full md:w-auto">
                            </a>
                        </div>
                    </div>
                    {/volist}
                </div>
            </div>
        </section>
    </main>

    {include file="public:footer"}

    <!-- 图片弹窗 -->
    <div id="img-modal" style="
        display: none;
        position: fixed;
        z-index: 999999;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);
        justify-content: center;
        align-items: center;
      ">
        <div style="position: relative; max-width: 90vw; max-height: 90vh">
            <img id="modal-img" src="" alt="放大图片" style="
            max-width: 100%;
            max-height: 80vh;
            display: block;
            margin: auto;
            border-radius: 10px;
          " />
            <button id="close-modal" style="
            position: absolute;
            top: 10px;
            right: 10px;
            background: #fff;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            font-size: 20px;
            cursor: pointer;
          ">
                ×
            </button>
        </div>
    </div>

    {include file="public:foot"}

    <script>
        document.querySelectorAll(".click-open").forEach(function (btn) {
            btn.addEventListener("click", function () {
                var img = this.parentElement.querySelector("img");
                if (img) {
                    document.getElementById("modal-img").src = img.src;
                    document.getElementById("img-modal").style.display = "flex";
                }
            });
        });
        document.getElementById("close-modal").onclick = function () {
            document.getElementById("img-modal").style.display = "none";
        };
        // 点击遮罩层关闭
        document.getElementById("img-modal").onclick = function (e) {
            if (e.target === this) {
                this.style.display = "none";
            }
        };
    </script>
</body>

</html>