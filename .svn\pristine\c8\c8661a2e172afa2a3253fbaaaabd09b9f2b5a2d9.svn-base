<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class News extends Common
{
    public function index()
    {
        $year = input("year")?input("year"):"";

        //所有新闻
        $news = Db::name('News')->alias("n")
            ->field("n.id, n.title, n.seo_url, n.content, n.publish_date, c.name, c.color, c.backgroud_color")
            ->when($year, function($query) use ($year) {
                $query->whereYear('n.publish_date', $year);
            })
            ->leftjoin("News_category c", "n.category_id=c.id")
            ->order("n.publish_date desc")
            ->select();

        //新闻分类
        $news_category = Db::name("News_category")
            ->select()
            ->each(function ($item) use ($year) {
                $query = Db::name('News')
                    ->where('category_id', $item['id'])
                    ->order('publish_date desc');

                if ($year) {
                    $query->whereYear('publish_date', $year);
                }

                $item['news'] = $query->select();
                return $item;
            });

        //所有年份
        $years = Db::name('News')
            ->field('DISTINCT YEAR(publish_date) as year')
            ->order('year DESC')
            ->select();

        return view("", [
            "news" => $news,
            "news_category" => $news_category,
            "years" => $years,
            "year" => $year,
        ]);
    }

    public function details() {
        $seo_url = input("url");

        $getone = Db::name("News")->where("seo_url", $seo_url)->find();

        if(empty($getone)) {
            return view("error/404")->code(404);
        }

        return view("", [
            "getone" => $getone,
        ]);
    }

}
