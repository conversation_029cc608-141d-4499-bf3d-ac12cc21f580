<?php /*a:2:{s:70:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\frontend_menu\edit.html";i:1754538661;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;}*/ ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>编辑前台菜单</title>
    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;前台菜单管理&nbsp;>&nbsp;编辑菜单
                <a href="<?php echo url('index'); ?>" class="de_y_r">返回列表</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="<?php echo url('edit', ['id' => $menu['id']]); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <!-- 隐藏的菜单位置字段 -->
                <input type="hidden" name="position" value="<?php echo htmlentities((string) $menu['position']); ?>">

                <div class="class_con">
                    <label>菜单名称：</label>
                    <input type="text" name="name" value="<?php echo htmlentities((string) $menu['name']); ?>">
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>菜单类型：</label>
                    <select name="type" class="l_xiang" id="menuType" onchange="onMenuTypeChange()">
                        <option value="">请选择菜单类型</option>
                        <?php if(is_array($typeList) || $typeList instanceof \think\Collection || $typeList instanceof \think\Paginator): $type = 0; $__LIST__ = $typeList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$name): $mod = ($type % 2 );++$type;?>
                        <option value="<?php echo htmlentities((string) $type); ?>" <?php if($menu['type'] == $type): ?>selected<?php endif; ?>><?php echo htmlentities((string) $name); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="class_con" id="urlGroup" style="display:none;">
                    <label>链接地址：</label>
                    <input type="text" name="url" value="<?php echo htmlentities((string) $menu['url']); ?>" placeholder="请输入链接地址，如：/about">
                </div>

                <div class="class_con" id="targetGroup" style="display:none;">
                    <label>关联内容：</label>
                    <select name="target_id" class="l_xiang" id="targetSelect">
                        <option value="">请选择关联内容</option>
                    </select>
                </div>

                <div class="class_con">
                    <label>父级菜单：</label>
                    <select name="parent_id" class="l_xiang">
                        <option value="0" <?php if($menu['parent_id'] == 0): ?>selected<?php endif; ?>>顶级菜单</option>
                        <?php if(is_array($parentMenus) || $parentMenus instanceof \think\Collection || $parentMenus instanceof \think\Paginator): $i = 0; $__LIST__ = $parentMenus;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$parentMenu): $mod = ($i % 2 );++$i;?>
                        <option value="<?php echo htmlentities((string) $parentMenu['id']); ?>" <?php if($menu['parent_id'] == $parentMenu['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $parentMenu['name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
                </div>

                <div class="class_con">
                    <label>排序：</label>
                    <input type="text" name="sort" value="<?php echo htmlentities((string) $menu['sort']); ?>">
                </div>

                <div class="class_con">
                    <label>状态：</label>
                    <select name="status" class="l_xiang">
                        <option value="1" <?php if($menu['status'] == 1): ?>selected<?php endif; ?>>启用</option>
                        <option value="0" <?php if($menu['status'] == 0): ?>selected<?php endif; ?>>禁用</option>
                    </select>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">保存</button>
                    <a href="<?php echo url('index'); ?>" class="de_y_r">返回</a>
                </div>
            </form>
        </div>
    </div>

    <script src="/static/admin/js/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // 页面加载时初始化菜单类型
            onMenuTypeChange();

            // 如果有关联内容，加载对应的数据
            var menuType = '<?php echo htmlentities((string) $menu['type']); ?>';
            var targetId = '<?php echo htmlentities((string) $menu['target_id']); ?>';
            if (menuType > 1 && targetId) {
                loadDynamicData(menuType, targetId);
            }
        });

        function onMenuTypeChange() {
            var type = $('#menuType').val();
            var urlGroup = $('#urlGroup');
            var targetGroup = $('#targetGroup');

            // 隐藏所有选项
            urlGroup.hide();
            targetGroup.hide();

            if (type == '1') { // 静态链接
                urlGroup.show();
            } else if (type == '2' || type == '3' || type == '4' || type == '5') { // 动态菜单
                targetGroup.show();
                loadDynamicData(type);
            }
        }

        function loadDynamicData(type, selectedId = null) {
            $.ajax({
                url: '<?php echo url("frontend_menu/getDynamicData"); ?>',
                type: 'POST',
                data: {type: type},
                dataType: 'json',
                success: function(res) {
                    if (res.code == 1) {
                        var select = $('#targetSelect');
                        select.empty();
                        select.append('<option value="">请选择关联内容</option>');

                        $.each(res.data, function(index, item) {
                            var selected = (selectedId && selectedId == item.id) ? 'selected' : '';
                            select.append('<option value="' + item.id + '" ' + selected + '>' + item.name + '</option>');
                        });
                    } else {
                        alert(res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', xhr, status, error);
                    alert('加载数据失败，请重试。错误信息：' + error);
                }
            });
        }
    </script>
</body>
</html>