<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\BaseController;
use think\facade\Db;
use think\facade\Cache;
use app\validate\Users as UsersValidate;
use think\exception\ValidateException;

class Login extends BaseController
{

    public function index()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            //验证数据
            try {
                validate(UsersValidate::class)->scene('admin-login')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            //验证邮箱验证码
            $cacheCaptcha = Cache::get('mail_captcha_' . $data['email']);
            if (!empty($cacheCaptcha) && $cacheCaptcha == $data['captcha']) {
                // 验证成功后删除验证码
                // Cache::set('mail_captcha_' . $data['email'], null);
            } else {
                $this->error('验证码错误或已过期！');
            }

            //获取用户信息
            $where = [
                ["email", "=", $data['email']],
                ["status", "=", 1],
            ];
            $userData = Db::name("User")->where($where)->find();
            if(empty($userData)){
                $this->error('用户不存在或被禁用！');
            }

            $user_role = Db::name("Roles")->field("status, is_admin")->where("id", $userData['role_id'])->find();
            if($user_role['status'] == 0 || $user_role['is_admin'] == 0){
                return $this->error('您没有后台登录权限！');
            }

            // 依据用户表中的salt字段生成hash明码
            $password = generateHashPassword($data['password'], $userData['salt']);
            if (strcasecmp($password, $userData['password']) != 0) {
                return $this->error('密码错误，请重新输入！');
            }

            session('adminId', $userData['id']);
            session('adminUsername', $userData['email']);

            //登录成功后删除验证码
            Cache::set('mail_captcha_' . $data['email'], null);

            $this->success('登录成功');
        }
        if(session('adminId')){
            $this->redirect('/admin');
        }

        return view();
    }



    //退出登录
    public function logout()
    {
        //清除后台登录信息
        session('adminId', null);
        session('adminUsername', null);

        //$this->redirect('/admin/login');
        echo "<script language=\"javascript\">window.open('/admin/login"."','_top');</script>";
    }

}
