<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加状态
                <a href="{:url('progress', ['id'=>$main_id, 'type'=>$type])}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add_progress')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="main_id" value="{$main_id}" />
                <input type="hidden" name="type" value="{$type}" />

                <div class="class_con">
                    <label>状态名称：</label>
                    <input type="text" name="name" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>状态说明：</label>
                    <textarea name="content"></textarea>
                </div>

                <div class="class_con">
                    <label>排序：</label>
                    <input type="text" name="sort" value="50" />
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('progress', ['id'=>$main_id, 'type'=>$type])}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>