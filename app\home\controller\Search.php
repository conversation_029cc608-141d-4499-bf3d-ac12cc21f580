<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;
use think\Request;

class Search extends Common
{
    public function index(Request $request)
    {
        // 获取搜索关键词
        $keyword = $request->param('q', '');

        // 如果没有搜索词，显示空页面
        if (empty($keyword)) {
            return view('', [
                'keyword' => '',
                'results' => []
            ]);
        }

        $faqs = Db::name('Faq')
            ->field("question as title, seo_url, 'faq' as type")
            ->where('question|answer', 'like', "%{$keyword}%")
            ->select();

        $news = Db::name('News')
            ->field("title, seo_url, 'news' as type")
            ->where('title|content', 'like', "%{$keyword}%")
            ->select();

        $product = Db::name('Product')
            ->field("title, seo_url, 'product' as type")
            ->where('name|title|description|content', 'like', "%{$keyword}%")
            ->select();

        $service = Db::name("Service")
            ->field("name as title, seo_url, 'service' as type")
            ->where('name|title|description|description2|content', 'like', "%{$keyword}%")
            ->select();

        $resource = Db::name("Resource")
            ->field("name as title, file, file_name, 'resource' as type")
            ->where('name|file_name', 'like', "%{$keyword}%")
            ->select();

        // 合并所有结果
        $results = array_merge($faqs->toArray(), $news->toArray(), $product->toArray(), $service->toArray(), $resource->toArray());

        // 处理结果，添加链接和高亮关键词
        foreach ($results as &$item) {
            // 高亮标题中的关键词
            $item['title'] = preg_replace("/(" . preg_quote($keyword, '/') . ")/i", '<span class="text-[#f61e1e]">$1</span>', $item['title']);
        }
        unset($item); // 销毁引用

        // 在控制器中
        $groupedResults = [
            'news' => [],
            'faq' => [],
            'product' => [],
            'service' => [],
            'resource' => []
        ];

        foreach ($results as $item) {
            $groupedResults[$item['type']][] = $item;
        }

        return view('', [
            'keyword' => $keyword,
            'groupedResults' => $groupedResults,
            "results" => $results
        ]);
    }

}
