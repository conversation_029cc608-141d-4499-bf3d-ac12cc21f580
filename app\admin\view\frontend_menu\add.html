<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加前台菜单</title>
    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;前台菜单管理&nbsp;>&nbsp;添加菜单
                <a href="{:url('index')}" class="de_y_r">返回列表</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <!-- 隐藏的菜单位置字段 -->
                <input type="hidden" name="position" value="{$position}">

                <div class="class_con">
                    <label>菜单名称：</label>
                    <input type="text" name="name" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>菜单类型：</label>
                    <select name="type" class="l_xiang" id="menuType" onchange="onMenuTypeChange()">
                        <option value="">请选择菜单类型</option>
                        {volist name="typeList" id="name" key="type"}
                        <option value="{$type}">{$name}</option>
                        {/volist}
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="class_con" id="urlGroup" style="display:none;">
                    <label>链接地址：</label>
                    <input type="text" name="url" placeholder="请输入链接地址，如：/about">
                </div>

                <div class="class_con" id="targetGroup" style="display:none;">
                    <label>关联内容：</label>
                    <select name="target_id" class="l_xiang" id="targetSelect">
                        <option value="">请选择关联内容</option>
                    </select>
                </div>

                <div class="class_con">
                    <label>父级菜单：</label>
                    <select name="parent_id" class="l_xiang">
                        <option value="0">顶级菜单</option>
                        {volist name="parentMenus" id="menu"}
                        <option value="{$menu.id}">{$menu.name}</option>
                        {/volist}
                    </select>
                </div>

                <div class="class_con">
                    <label>排序：</label>
                    <input type="text" name="sort" value="0">
                </div>

                <div class="class_con">
                    <label>状态：</label>
                    <select name="status" class="l_xiang">
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r">返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        function onMenuTypeChange() {
            var type = $('#menuType').val();
            var urlGroup = $('#urlGroup');
            var targetGroup = $('#targetGroup');

            // 隐藏所有选项
            urlGroup.hide();
            targetGroup.hide();

            if (type == '1') { // 静态链接
                urlGroup.show();
            } else if (type == '2' || type == '3' || type == '4' || type == '5') { // 动态菜单
                targetGroup.show();
                loadDynamicData(type);
            }
        }

        function loadDynamicData(type) {
            console.log('Loading dynamic data for type:', type);
            $.ajax({
                url: '{:url("frontend_menu/getDynamicData")}',
                type: 'POST',
                data: {type: type},
                dataType: 'json',
                success: function(res) {
                    if (res.code == 1) {
                        var select = $('#targetSelect');
                        select.empty();
                        select.append('<option value="">请选择关联内容</option>');

                        $.each(res.data, function(index, item) {
                            select.append('<option value="' + item.id + '">' + item.name + '</option>');
                        });
                    } else {
                        alert(res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', xhr, status, error);
                    alert('加载数据失败，请重试。错误信息：' + error);
                }
            });
        }
    </script>
</body>
</html>