<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改问答
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>分类：</label>
                    <select name="category_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="category" id="vo"}
                            <option value="{$vo.id}" {$getone.category_id == $vo.id ? 'selected' : ''}>{$vo.name}</option>
                            {volist name="vo['son']" id="v" key="k"}
                            <option value="{$v.id}" {$getone.category_id == $v.id ? 'selected' : ''}>&nbsp;&nbsp;&nbsp;&nbsp;--{$v.name}</option>
                            {/volist}
                        {/volist}
                    </select>
                </div>

                <div class="class_con">
                    <label>SEO链接：</label>
                    <input type="text" name="seo_url" value="{$getone.seo_url}" />
                </div>

                <div class="class_con">
                    <label>问题：</label>
                    <input type="text" name="question" value="{$getone.question}" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con editor-container">
                    <label>回答：</label>
                    <textarea name="answer" class="tiny-editor" style="height: 300px;">{$getone.answer}</textarea>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>