<?php /*a:3:{s:67:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\order\edit_file.html";i:1750235106;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1749186186;s:69:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot_layer.html";i:1748510796;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="https://cdn.ckeditor.com/ckeditor5/45.2.0/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/admin/css/ckeditor5.css">
</head>

<body>
    <div class="cnt-basic-list cnt-basic-layer change-files order-info" style="display: block;">
        <form action="<?php echo url('edit_file'); ?>" method="post" enctype="multipart/form-data" class="layer-form" autocomplete="off">
            <input type="hidden" name="id" value="<?php echo htmlentities((string) $getone['id']); ?>" />

            <div class="cnt-basic-item">
                <?php if($order['parent_id'] == 0): ?>
                <input type="hidden" name="order_id" value="<?php echo htmlentities((string) $getone['order_id']); ?>" />
                <div class="cnt-basic-i class_con">
                    <label>选择主体：</label>
                    <span class="selecttype type <?php if($getone['type']==0): ?>active<?php endif; ?>" data-type="0"><?php echo htmlentities((string) $order['product_name']); ?></span>
                    <?php if($order['service_id']): ?>
                    <span class="selecttype type <?php if($getone['type']==1): ?>active<?php endif; ?>" data-type="1"><?php echo htmlentities((string) $order['service_name']); ?></span>
                    <?php endif; ?>
                    <input type="hidden" name="type" value="<?php echo htmlentities((string) $getone['type']); ?>" />
                </div>
                <?php else: ?>
                <div class="cnt-basic-i class_con">
                    <label>选择子订单：</label>
                    <select name="order_id" class="l_xiang">
                        <option value="">请选择</option>
                        <?php if(is_array($order_subs) || $order_subs instanceof \think\Collection || $order_subs instanceof \think\Paginator): $i = 0; $__LIST__ = $order_subs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['order_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['order_no']); ?> / <?php echo htmlentities((string) $vo['service_name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
                    <input type="hidden" name="type" value="1" />
                </div>
                <?php endif; ?>

                <div class="cnt-basic-i class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type <?php if($getone['url']!=''): ?>active<?php endif; ?>" data-value="url">网址</span>
                    <span class="selecttype up_type <?php if($getone['file']!=''): ?>active<?php endif; ?>" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="<?php echo $getone['url']!='' ? 'url' : 'file'; ?>" />
                </div>

                <div class="url" <?php if($getone['url']!=''): ?>style="display:block;<?php else: ?>style="display:none;"<?php endif; ?>">
                    <div class="cnt-basic-i class_con">
                        <label>网址：</label>
                        <input type="text" name="url" value="<?php echo htmlentities((string) $getone['url']); ?>" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="cnt-basic-i class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" value="<?php echo htmlentities((string) $getone['url_describe']); ?>" />
                    </div>
                </div>

                <div class="file" <?php if($getone['file']!=''): ?>style="display:block;<?php else: ?>style="display:none;"<?php endif; ?>">
                    <div class="cnt-basic-i class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="cnt-basic-i class_con">
                        <span class="input-tips">（建议PDF，不超过30M）</span>
                    </div>
                    <?php if($getone['file']): ?>
                    <div class="cnt-basic-i class_con">
                        <a href="<?php echo htmlentities((string) $getone['file']); ?>" target="_blank" class="url-address"><?php echo htmlentities((string) $getone['file_name']); ?></a>
                        <input type="hidden" name="file_path" value="<?php echo htmlentities((string) $getone['file']); ?>" />
                    </div>
                    <?php endif; ?>
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
            </div>
        </form>
    </div>

    <script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script>
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            parent.location.reload();
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    parent.layer.close(window.layerIndex);
                } else {
                    parent.layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

    <script>
    $(document).ready(function() {
        // 主体选择
        $('.type').click(function() {
            $('.type').removeClass('active');
            $(this).addClass('active');
            $('input[name="type"]').val($(this).data('type'));
        });

        // 类型选择
        $('.up_type').click(function() {
            $('.up_type').removeClass('active');
            $(this).addClass('active');
            var upType = $(this).data('value');
            $('input[name="up_type"]').val(upType);

            // 显示/隐藏对应的输入区域
            if(upType === 'url') {
                $('.url').show();
                $('.file').hide();
            } else {
                $('.url').hide();
                $('.file').show();
            }
        });
    });
    </script>

</body>
</html>