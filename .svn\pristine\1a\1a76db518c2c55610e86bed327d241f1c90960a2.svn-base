/**
 * 封装数字分页
 * @param {Swiper} swiperInstance - Swiper实例
 * @param {HTMLElement} container - 显示数字分页的容器
 */
function setupNumberPagination(swiperInstance, container) {
    function render(activeIndex) {
        // 兼容不同Swiper版本，优先用wrapperEl
        var wrapper = swiperInstance.wrapperEl || (swiperInstance.$wrapperEl && swiperInstance.$wrapperEl[0]);
        var slideCount = wrapper
            ? wrapper.querySelectorAll('.swiper-slide:not(.swiper-slide-duplicate)').length
            : document.querySelectorAll('.swiper-wrapper .swiper-slide:not(.swiper-slide-duplicate)').length;
        var current = (activeIndex + 1).toString().padStart(2, '0');
        var total = slideCount.toString().padStart(2, '0');
        container.innerHTML = `<span class="Digital-l Roboto_Bold"> ${current} </span> / <span class="Digital-r"> ${total} </span>`;
    }
    swiperInstance.on('init', function(){
        setTimeout(function() {
            render(swiperInstance.realIndex || 0);
        }, 0);
    });
    swiperInstance.on('slideChange', function(){
        render(swiperInstance.realIndex || 0);
    });
    // 若swiper已初始化，立即渲染
    if (swiperInstance.initialized) {
        render(swiperInstance.realIndex || 0);
    }
}

/**
 * 封装无缝滚动，可多次调用
 * @param {HTMLElement} scrollContainer 滚动容器（overflow:hidden，内部ul）
 * @param {Object} options 配置项
 *   direction: "up" | "down"，默认"up"
 *   speed: 滚动速度（px/step），默认1
 *   delay: 间隔时间（ms），默认20
 */
function seamlessScroll(scrollContainer, options) {
    options = options || {};
    var direction = options.direction || "up";
    var speed = options.speed || 1;
    var delay = options.delay || 20;

    var ul = scrollContainer.querySelector('ul');
    if (!ul || ul.children.length === 0) return;

    // 克隆内容实现无缝
    ul.innerHTML += ul.innerHTML;

    var timer = null;
    function scrollStep() {
        if (direction === "up") {
            if (scrollContainer.scrollTop >= ul.scrollHeight / 2) {
                scrollContainer.scrollTop = 0;
            } else {
                scrollContainer.scrollTop += speed;
            }
        } else {
            if (scrollContainer.scrollTop <= 0) {
                scrollContainer.scrollTop = ul.scrollHeight / 2;
            } else {
                scrollContainer.scrollTop -= speed;
            }
        }
    }

    function start() {
        if (timer) clearInterval(timer);
        timer = setInterval(scrollStep, delay);
    }
    function stop() {
        clearInterval(timer);
    }

    scrollContainer.addEventListener('mouseenter', stop);
    scrollContainer.addEventListener('mouseleave', start);

    // 初始化
    start();
}