<?php
namespace app\services;

use think\facade\Log;
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
use think\facade\Config;

use think\facade\Db;

class MailService
{
    /**
     * 发送模板邮件
     * @param string $email_identifier 邮件模板标识
     * @param string $to_email 接收邮箱
     * @param array $data 变量数据
     * @return boolen|string true或错误信息
     */
    public static function sendEmail($email_identifier, $to_email, $data){
        try {
            $where = [
                "identifier" => $email_identifier,  //邮件模板表示
                "status" => 1,  //启用状态
            ];
            $template = Db::name("Email_template")->where($where)->find();
            if (!$template) {
                throw new \Exception('邮件模板不存在或已禁用');
            }

            $subject = self::replaceVariables($template['subject'], $data);
            $content = self::replaceVariables($template['content'], $data);
            $result = MailService::send($to_email, $subject, $content);
            if ($result['status']) {
                return true;
            } else {
                throw new \Exception($result['message']);
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('邮件发送失败: ' . $e->getMessage());
            return $e->getMessage(); // 返回错误信息
        }
    }

    /**
     * 发送邮件
     * @param string|array $email 收件邮箱（字符串或数组）
     * @param string $subject 邮件标题
     * @param array $body 邮件内容
     * @return array ['status' => bool, 'message' => string, 'data' => array]
     */
    public static function send($email, string $subject, string $body): array
    {
        // 获取ThinkPHP的邮件配置
        $mailConfig = Config::get('mail.smtp');

        // 验证配置
        if (empty($mailConfig) || !isset($mailConfig['host'], $mailConfig['username'], $mailConfig['password'])) {
            return [
                'status' => false,
                'message' => '邮件服务配置不完整！',
                'data' => []
            ];
        }

        // 验证收件人格式
        if (empty($email) || (!is_string($email) && !is_array($email))) {
            return [
                'status' => false,
                'message' => '收件邮箱格式不正确！',
                'data' => []
            ];
        }

        // 如果是字符串，转为数组统一处理
        $emails = is_string($email) ? [$email] : $email;

        $mail = new PHPMailer(true);
        $successCount = 0;
        $failedEmails = [];

        try {
            // 服务器配置
            $mail->isSMTP();
            $mail->Host       = $mailConfig['host'];
            $mail->SMTPAuth   = true;
            $mail->Username   = $mailConfig['username'];
            $mail->Password   = $mailConfig['password'];
            $mail->SMTPSecure = $mailConfig['encryption'] ?? 'tls';
            $mail->Port       = $mailConfig['port'] ?? 587;
            $mail->CharSet = 'UTF-8';
            $mail->Encoding = 'base64';
            $mail->Timeout = 30;

            // 发件人
            $from = Config::get('mail.smtp.from');
            $mail->setFrom(key($from), current($from));

            // 收件人
            foreach ($emails as $email) {
                try {
                    $mail->clearAddresses(); // 清除之前的收件人
                    $mail->addAddress($email);

                    $mail->isHTML(true);
                    $mail->Subject = $subject;
                    $mail->Body = $body;

                    if ($mail->send()) {
                        $successCount++;
                    } else {
                        $failedEmails[$email] = '发送失败';
                    }
                } catch (Exception $e) {
                    $failedEmails[$email] = $e->getMessage();
                    Log::error("邮件发送失败($email): " . $e->getMessage());
                }
            }

            if ($successCount === count($emails)) {
                return [
                    'status' => true,
                    'message' => '所有邮件发送成功',
                    'data' => [
                        'success_count' => $successCount
                    ]
                ];
            } else {
                return [
                    'status' => false,
                    'message' => '部分邮件发送失败',
                    'data' => [
                        'success_count' => $successCount,
                        'failed_emails' => $failedEmails
                    ]
                ];
            }
        } catch (Exception $e) {
            Log::error('邮件服务异常: ' . $e->getMessage());
            return [
                'status' => false,
                'message' => '邮件服务异常: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 替换模板变量
     */
    private static function replaceVariables($content, $data)
    {
        foreach ($data as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }
        return $content;
    }
}