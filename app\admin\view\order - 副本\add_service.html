<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>创建订单</title>

    {include file="common:head"}
</head>

<style>
    .class_con label{
        width: 220px;
    }
    .class_con span{
        color: #464df4;
        margin-left: 0;
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加后续服务
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add_service')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="order_id" value="{$getone.id}" />

                <div class="class_con">
                    <label>父订单编号：</label>
                    <span>{$getone.order_no}</span>
                </div>

                <div class="class_con">
                    <label>Email：</label>
                    <span>{$getone.email}</span>
                </div>

                <div class="class_con">
                    <label>service：</label>
                    <select name="service_id" id="service" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="service" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
                </div>

                <div class="class_con">
                    <label>服务状态：</label>
                    <select name="service_progress_id" id="service_progress_id" class="l_xiang">
                        <option value="">请选择</option>
                    </select>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        $('#service').on('change', function() {
            var serviceId = $(this).val();
            if (!serviceId) return;
            $.ajax({
                url: '/admin/Order/getServicesProgress/',
                type: 'GET',
                data: {id: serviceId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        var $serviceSelect = $('#service_progress_id');
                        $serviceSelect.empty(); // 清空现有选项
                        // 添加默认选项
                        $serviceSelect.append('<option value="" disabled selected>请选择</option>');
                        // 动态添加 service 选项
                        $.each(response.data, function(index, progress) {
                            $serviceSelect.append(
                                '<option value="' + progress.id + '">' + progress.name + '</option>'
                            );
                        });
                        // 更新 UI 颜色（如果之前有样式逻辑）
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>

</body>
</html>