<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>{$tdk.seo_title?$tdk.seo_title:"HitGen OpenDEL™"}</title>
    <meta name="keywords" content="{$tdk.seo_keywords}" />
    <meta name="description" content="{$tdk.seo_description}" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        DELHunter
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="contact">
                <h1 class="text-2xl md:text-5xl md:mb-10 mb-5 Roboto_Bold">DELHunter</h1>
                <div class="flex flex-col gap-5 overflow-hidden">
                    <div data-aos="fade-up"
                        class="bg-white rounded-xl flex flex-col gap-y-5 pb-5 border border-[#dae9ff] md:flex-row md:gap-x-5 md:min-h-[30rem] md:pb-0 aos-init aos-animate">
                        <figure
                            class="md:order-2 md:w-3/6 md:h-1vh md:bg-gradient-to-r md:from-white md:via-white/0 md:to-white/0 md:relative">
                            <img src="__IMG__/news/1.jpg" alt=""
                                class="object-cover w-full rounded-tr-xl rounded-tl-xl md:absolute md:-z-20 md:h-full md:rounded-br-xl">
                            <figcaption class="sr-only">OpenDEL™&nbsp;Sequencing</figcaption>
                        </figure>
                        <div class="px-5 flex flex-col justify-center md:order-1 md:w-3/6 md:py-14 md:px-16 md:pr-0">
                            <header class="mb-2.5 px-3 md:mb-6 md:px-0">
                                <h2 class="text-xl Roboto_Bold mb-2 md:text-4xl md:mb-0">
                                    Your Gateway to Cutting-Edge DEL Innovations& Early Drug Discovery
                                </h2>
                            </header>
                            <div class="text-[#666] text-sm leading-6 px-3 md:text-xl md:leading-10 md:px-0">
                                <p class="md:line-clamp-6">
                                    Welcome to DELhunter, a dedicated knowledge hub by OpenDELclub that tracks the
                                    latest advancements in DNA-Encoded Library (DEL) technology and breakthroughs
                                    in early-stage drug discovery.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="w-11/12 mx-auto md:w-10/12 pb-10">
        <div class="py-5 md:py-9">
            <h2 class="text-xl Roboto_Bold md:text-3xl">What You’ll Discover:</h2>
        </div>
        <div class="discover-list grid grid-cols-1 gap-2.5 mb-10 md:grid-cols-3 md:mb-[4.0625rem]">
            <div class="discover-item bg-[#f8fdff] border border-[#c4d7ff] rounded-xl py-4 px-5 md:px-10 md:py-7">
                <div
                    class="discover-item-img w-[3.125rem] h-[3.125rem] bg-white border border-[#e0eaff] rounded-full flex items-center justify-center mb-2.5 md:w-[5rem] md:h-[5rem] md:mb-8">
                    <img src="__IMG__/icons/xianweijing.png" class="w-[1.125rem] md:w-[1.95rem]" alt="">
                </div>
                <div class="discover-item-text">
                    <h3 class="text-lg Roboto_Bold mb-1.5 md:text-3xl md:mb-6">DEL Technology Updates</h3>
                    <p class="text-sm md:text-xl">Stay ahead with curated insights on novel screening methods, library design
                        innovations, and emerging applications.</p>
                </div>
            </div>
            <div class="discover-item bg-[#f8fdff] border border-[#c4d7ff] rounded-xl py-4 px-5">
                <div
                    class="discover-item-img w-[3.125rem] h-[3.125rem] bg-white border border-[#e0eaff] rounded-full flex items-center justify-center mb-2.5  md:w-[5rem] md:h-[5rem] md:mb-8">
                    <img src="__IMG__/icons/jiaonang.png" class="w-[1.25rem] md:w-[2.25rem]" alt="">
                </div>
                <div class="discover-item-text">
                    <h3 class="text-lg Roboto_Bold mb-1.5 md:text-3xl md:mb-6">Early Drug Discovery Spotlight</h3>
                    <p class="text-sm md:text-xl">Explore case studies, target identification strategies, and hit-to-lead
                        optimization success stories.</p>
                </div>
            </div>
            <div class="discover-item bg-[#f8fdff] border border-[#c4d7ff] rounded-xl py-4 px-5">
                <div
                    class="discover-item-img w-[3.125rem] h-[3.125rem] bg-white border border-[#e0eaff] rounded-full flex items-center justify-center mb-2.5  md:w-[5rem] md:h-[5rem] md:mb-8">
                    <img src="__IMG__/icons/diqiu.png" class="w-[1.375rem] md:w-[2.35rem]" alt="">
                </div>
                <div class="discover-item-text">
                    <h3 class="text-lg Roboto_Bold mb-1.5 md:text-3xl md:mb-6">Industry-Academia Trends</h3>
                    <p class="text-sm md:text-xl">Learn about collaborative research, DEL-based therapeutics in development, and
                        expert perspectives.
                    </p>
                </div>
            </div>
        </div>

        <div class="discover-news">
            <!-- 头部分类和时间筛选 -->
            <div class="flex items-center flex-wrap justify-between mb-5 md:mb-10 gap-y-2.5 md:gap-y-0">
                <div class="uppercase discover-news-screening flex items-center gap-x-1 text-sm md:text-xl">
                    <span data-tab="all" class="news-tab-item">
                        all
                    </span>

                    {volist name="news_category" id="vo"}
                    <span data-tab="{$vo.id}" class="news-tab-item">{$vo.name}</span>
                    {/volist}
                </div>

                <div class="w-[6.25rem]  md:w-[16.25rem]">
                    <select name="year" id="year" class="w-full appearance-none py-1.5 border border-[#dae9ff] rounded-md text-[#155797] text-sm bg-[url(/static/home/<USER>/icons/mti-jiantouyou1.png)] bg-no-repeat bg-[85%_center] bg-size-[.875rem] pl-3 cursor-pointer md:py-3 md:px-10 md:text-xl md:bg-size-[1.3rem]">
                        <option value="">year</option>
                        {volist name="years" id="vo"}
                        <option value="{$vo.year}" {if $year==$vo.year}selected{/if}>{$vo.year}</option>
                        {/volist}
                    </select>
                </div>
            </div>

            <!-- 内容 -->
            <div class="news-content-all">
                <div data-tab="all" class="news-content-item">
                    <ul role="list" class="grid grid-cols-1 gap-4 mb-5 md:mb-11">
                        {volist name="news" id="vo"}
                        <li class="flex group flex-col bg-white px-5 py-4 gap-3 border border-[#e0eaff] md:px-10 md:py-7 md:gap-0 md:relative">
                            <div class="text-sm md:mb-6 md:text-base">
                                <span class="text-[{$vo.color}] min-w-12 bg-[{$vo.backgroud_color}] text-center py-0.5 px-1.5 rounded-sm mr-1" style="color: {$vo.color}; background-color: {$vo.backgroud_color}">{$vo.name}</span>
                                <time>
                                    {:date('j F Y', strtotime($vo.publish_date))}
                                </time>
                            </div>
                            <div class=" md:max-w-[85%]">
                                <h3 class="text-xl Roboto_Bold mb-2.5 line-clamp-1 md:text-3xl md:mb-5">
                                    <a href="/news/{$vo.seo_url}">
                                        {$vo.title}
                                    </a>
                                </h3>
                                <p class="line-clamp-1 text-[#999] text-sm md:text-xl">
                                   {$vo.content|strip_tags}
                                </p>
                            </div>
                            <div class="flex justify-start mt-2.5 md:absolute md:bottom-7 md:right-10 transition-all group-hover:right-5 md:top-1/2 md:-translate-y-1/2">
                                <a href="/news/{$vo.seo_url}" class="flex items-center gap-1.5 md:gap-x-3">
                                    <span class="text-[#333] text-sm md:text-base">Learn More</span>
                                    <img src="__IMG__/icons/changjiantou-r.png" alt="Learn More" class="w-[.875rem] inline-block md:w-[1.8125rem] md:opacity-30 group-hover:opacity-100">
                                </a>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                    <div class="text-base">
                        <button type="button" class="rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer">
                            SHOW MORE
                        </button>
                    </div>
                </div>

                {volist name="news_category" id="vo"}
                <div data-tab="{$vo.id}" class="news-content-item" style="display: none;">
                    <ul role="list" class="grid grid-cols-1 gap-4 mb-5 md:mb-11">
                        {volist name="vo.news" id="v"}
                        <li class="flex group flex-col bg-white px-5 py-4 gap-3 border rounded-xl border-[#e0eaff] md:px-10 md:py-7 md:gap-0 md:relative">
                            <div class="text-sm md:mb-6 md:text-base">
                                <span class="text-[{$vo.color}] min-w-12 bg-[{$vo.backgroud_color}] text-center py-0.5 px-1.5 rounded-sm mr-1" style="color: {$vo.color}; background-color: {$vo.backgroud_color}">{$vo.name}</span>
                                <time>
                                    {:date('j F Y', strtotime($v.publish_date))}
                                </time>
                            </div>
                            <div class=" md:max-w-[85%]">
                                <h3 class="text-xl Roboto_Bold mb-2.5 line-clamp-1 md:text-3xl md:mb-5">
                                    <a href="/news/{$v.seo_url}">
                                        {$v.title}
                                    </a>
                                </h3>
                                <p class="line-clamp-1 text-[#999] text-sm md:text-xl">
                                    {$v.content|strip_tags}
                                </p>
                            </div>
                            <div class="flex justify-start mt-2.5 md:absolute md:bottom-7 md:right-10 transition-all group-hover:right-5 md:top-1/2 md:-translate-y-1/2">
                                <a href="/news/{$v.seo_url}" class="flex items-center gap-1.5 md:gap-x-3">
                                    <span class="text-[#333] text-sm md:text-base">Learn More</span>
                                    <img src="__IMG__/icons/changjiantou-r.png" alt="Learn More" class="w-[.875rem] inline-block md:w-[1.8125rem] md:opacity-30 group-hover:opacity-100">
                                </a>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                    <div class="text-base">
                        <button type="button" class="rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer">
                            SHOW MORE
                        </button>
                    </div>
                </div>
                {/volist}
            </div>
        </div>
    </main>

    {include file="public:footer"}

    {include file="public:foot"}
    <script src="__JS__/TabSwitch.js"></script>

    <script>

        initTabSwitch('.news-tab-item', '.news-content-item');
        // 获取所有新闻内容项
        const newsItems = document.querySelectorAll('.news-content-item');

        // 处理每个新闻内容项
        newsItems.forEach(item => {
            const listItems = item.querySelectorAll('li');
            const showMoreBtn = item.querySelector('button');

            // 初始只显示4条
            if(listItems.length > 4) {
                for(let i = 4; i < listItems.length; i++) {
                    listItems[i].style.display = 'none';
                }
                showMoreBtn.style.display = 'block';
            } else {
                showMoreBtn.style.display = 'none';
            }

            // 点击按钮显示所有并隐藏自身
            showMoreBtn.addEventListener('click', () => {
                listItems.forEach(li => li.style.display = '');
                showMoreBtn.style.display = 'none';
            });
        });
    </script>

    <script>
        $(function() {
            // 监听年份选择变化
            $('#year').change(function() {
                var year = $(this).val();
                var url = '/news';

                // 如果有选择年份，则附加到URL
                if(year) {
                    url += '/year/' + year;
                }

                // 跳转页面
                window.location.href = url;
            });
        });
    </script>

</body>

</html>