<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class Resources extends Common
{
    public function index()
    {
        $this->setTdk(7);

        //资源分类
        $resources = Db::name("Resource_category")
            ->select()
            ->each(function ($item) {
                $item['resource'] = Db::name("Resource")
                    ->where("category_id", $item['id'])
                    ->order("create_time desc")
                    ->select()
                    ->each(function ($i) {
                        $i['file_type'] = getFileType($i['file_name']);

                        return $i;
                    });

                return $item;
            });

        return view("", [
            "resources" => $resources,
        ]);
    }

}
