/**
 * 初始化订单搜索功能（带防抖）
 * @param {Object} options 配置项
 *   - containerSelector: 容器选择器 (默认 '.order-section')
 *   - inputSelector: 输入框选择器 (默认 '.order-search-input')
 *   - itemSelector: 订单项选择器 (默认 '.order-list-item-container')
 *   - debounceTime: 防抖延迟时间(ms) (默认 300ms)
 */
function initOrderSearch(options = {}) {
  const {
    containerSelector = '.order-section',
    inputSelector = '.order-search-input',
    itemSelector = '.order-list-item-container',
    debounceTime = 300
  } = options;

  // 防抖函数
  const debounce = (fn, delay) => {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn.apply(this, args), delay);
    };
  };

  // 筛选订单函数
  const filterOrders = (inputElement, items) => {
    const term = inputElement.value.toLowerCase().trim();
    items.forEach(item => {
      const isMatch = item.textContent.toLowerCase().includes(term);
      item.style.display = isMatch ? 'block' : 'none';
      
    });
    // 显示/隐藏空状态提示
    const emptyState = inputElement.closest(containerSelector)
                        .querySelector('.no-results');
    if (emptyState) {
        const visibleCount = Array.from(items).filter(item => item.style.display !== 'none').length;
        emptyState.style.display = visibleCount ? 'none' : 'block';
    }
  };

  // 初始化所有符合条件的容器
  document.querySelectorAll(containerSelector).forEach(container => {
    const input = container.querySelector(inputSelector);
    const items = container.querySelectorAll(itemSelector);
    
    if (input && items.length) {
      const debouncedFilter = debounce(() => filterOrders(input, items), debounceTime);
      input.addEventListener('input', debouncedFilter);
      
      // 首次加载时触发一次筛选（处理初始值）
      debouncedFilter();
    }
  });
}

