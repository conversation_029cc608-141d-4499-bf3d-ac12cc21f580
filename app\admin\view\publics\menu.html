<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
	<title>Document</title>

    <script>
        // 从父窗口获取 CSS，并插入到当前子框架
        if (window.parent && window.parent.getParentCSS) {
            document.write(window.parent.getParentCSS());
        }
    </script>
</head>

<body style="background:#191f26 none repeat scroll;">
    <div class="menus">
     	<div class="menus_l" id="notemenu">
            <ul>
                {volist name="menus" id="vo"}
                {if isset($role_menus[$vo.id]) && $role_menus[$vo.id]!==0}
                <li class="cur">
                    <div class="list_ca list_back {$vo.icon}"><i></i><a href="javascript:void(0)">{$vo.title}</a></div>
                </li>

                    {volist name="vo.son" id="vo2"}
                    {if isset($role_menus[$vo2.id]) && $role_menus[$vo2.id]!==0}
                    <li class="tab">
                        <div class="list_cg list_back">
                            <i class="icon-new icon-{$vo2.icon}"></i>
                            <a href="{if $vo2.url}/admin/{$vo2.url}{else /}javascript:void(0){/if}" target="mainframe">{$vo2.title} </a>
                            {if !$vo2.url}<em></em>{/if}
                        </div>

                        {if count($vo2['son']) !== 0}
                        <ul class="menu-child" style="display:none;">
                            {volist name="vo2.son" id="vo3"}
                            {if isset($role_menus[$vo3.id]) && $role_menus[$vo3.id]!==0}
                            <li><a href="/admin/{$vo3.url}" target="mainframe">{$vo3.title}</a></li>
                            {/if}
                            {/volist}
                        </ul>
                        <hr>
                        {/if}
                    </li>
                    {/if}
                    {/volist}
                {/if}
                {/volist}
            </ul>
        </div>
    </div>

    <script src="__JS__/jquery.min.js"></script>
    <script type="text/javascript" src="__JS__/menu.js"></script>

</body>
</html>