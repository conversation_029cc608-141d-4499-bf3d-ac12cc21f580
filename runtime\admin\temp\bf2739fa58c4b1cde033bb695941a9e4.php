<?php /*a:3:{s:62:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\menu\index.html";i:1752041297;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1751613673;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">

    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.1/themes/base/jquery-ui.css">
</head>

<style>
    .class_con{
        width: 600px;
    }
    .class_con span{
        color: inherit;
    }
    .sortable-menu {
        min-height: 20px;
    }
    .manag-radio-item {
        padding: 5px 10px;
        margin: 5px 0;
        background: #f5f5f5;
        border: 1px solid #ddd;
        cursor: move;
        position: relative;
    }
    .parent {
        background: #e9e9e9;
        font-weight: bold;
    }
    .child {
        background: #f0f0f0;
    }
    .child2 {
        background: #f8f8f8;
    }
    .child-container, .child2-container {
        margin-left: 15px;
    }
    .handle {
        cursor: move;
        margin-right: 5px;
    }
    .ui-sortable-helper {
        opacity: 0.8;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;菜单管理
                <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="<?php echo url('index'); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <div class="class_con">
                    <div class="manag-radio-list sortable-menu">
                    <?php if(is_array($menus) || $menus instanceof \think\Collection || $menus instanceof \think\Paginator): $i = 0; $__LIST__ = $menus;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <div class="manag-radio-item parent" data-menu-id="<?php echo htmlentities((string) $vo['id']); ?>">
                            <span><i class="handle ui-icon ui-icon-grip-dotted-vertical"></i><?php echo htmlentities((string) $vo['title']); ?></span>
                        </div>

                        <div class="child-container" data-parent="<?php echo htmlentities((string) $vo['id']); ?>">
                            <?php if(is_array($vo['son']) || $vo['son'] instanceof \think\Collection || $vo['son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo2): $mod = ($i % 2 );++$i;?>
                            <div class="manag-radio-item child" data-menu-id="<?php echo htmlentities((string) $vo2['id']); ?>" data-parent="<?php echo htmlentities((string) $vo['id']); ?>">
                                <span><i class="handle ui-icon ui-icon-grip-dotted-vertical"></i><?php echo htmlentities((string) $vo2['title']); ?></span>
                            </div>

                            <div class="child2-container" data-parent="<?php echo htmlentities((string) $vo2['id']); ?>">
                                <?php if(isset($vo2['son'])): if(is_array($vo2['son']) || $vo2['son'] instanceof \think\Collection || $vo2['son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo2['son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo3): $mod = ($i % 2 );++$i;?>
                                <div class="manag-radio-item child2" data-menu-id="<?php echo htmlentities((string) $vo3['id']); ?>" data-parent="<?php echo htmlentities((string) $vo2['id']); ?>">
                                    <span><i class="handle ui-icon ui-icon-grip-dotted-vertical"></i>&nbsp;&nbsp;<?php echo htmlentities((string) $vo3['title']); ?></span>
                                </div>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                    </div>
                </div>

                <!-- 添加隐藏字段 -->
                <input type="hidden" name="menu_order" id="menuOrderInput" value="<?php echo htmlentities((string) (isset($initialOrder) && ($initialOrder !== '')?$initialOrder:'[]')); ?>">

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
<script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
<script src="/static/ckeditor5/ckeditor5.js"></script>

<script>
    var customEditor = new CKEditorManager();
    customEditor.initAll('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const $form = $('#formId');
    const $submitBtn = $('#submitBtn');
    // 初始绑定提交事件
    if ($form.length) {
        $form.on('submit', handleSubmit);
    }
    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 确保所有编辑器内容已同步到 textarea
        customEditor.editors.forEach((editor, index) => {
            editor.updateSourceElement();
        });

        // 解绑提交事件（避免重复提交）
        $form.off('submit', handleSubmit);
        // 禁用提交按钮（防止重复点击）
        $submitBtn.prop('disabled', true);
        try {
            const formData = new FormData($form[0]);
            const response = await fetch($form.attr('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });
            const data = await response.json();
            if (data.code === 1) {
                // 提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                // 提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            $submitBtn.prop('disabled', false);
            $form.on('submit', handleSubmit);
        }
    }
</script>

<script>
    //弹窗
    $(document).on('click', '.layer-trigger', function() {
        const $btn = $(this);
        const contentId = $btn.data('layer');
        const title = $btn.data('title');
        const width = $btn.data('width');
        const height = $btn.data('height');

        layer.open({
            type: 1,
            title: [title, 'font-size:18px; background-color: #0f2950; color: #fff;'],
            closeBtn: 1,
            shadeClose: true,
            area: [width, height],
            content: $("#" + contentId),
            resize: false,
            move: false
        });
    });

    //弹窗"提交"和"取消"事件
    // 表单初始化函数
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            location.reload();

                            // 优先使用保存的索引
                            // if(window.layerIndex !== undefined) {
                            //     layer.close(window.layerIndex);
                            // }
                            // // 其次尝试获取当前索引
                            // else if(layer.getFrameIndex && layer.getFrameIndex(window.name)) {
                            //     layer.close(layer.getFrameIndex(window.name));
                            // }
                            // // 最后全部关闭
                            // else {
                            //     layer.closeAll();
                            // }

                            // // 如果需要刷新
                            // if(response.reload) {
                            //     location.reload();
                            // }
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    layer.close(window.layerIndex);
                } else {
                    layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    $(document).ready(function() {
        // 遍历每一个多选框容器
        $(".multi-select-container").each(function() {
            const $container = $(this);
            const $select = $container.find(".user-multi-select");
            const $selectAllBtn = $container.find(".select-all-btn");
            const $deselectAllBtn = $container.find(".deselect-all-btn");
            const $countDisplay = $container.find(".count");

            // 初始化计数
            updateSelectedCount();

            // 全选
            $selectAllBtn.on("click", function() {
                $select.find("option").prop("selected", true);
                updateSelectedCount();
            });

            // 取消全选
            $deselectAllBtn.on("click", function() {
                $select.find("option").prop("selected", false);
                updateSelectedCount();
            });

            // 点击选项（无需按Ctrl）
            $select.on("mousedown", "option", function(e) {
                e.preventDefault();
                $(this).prop("selected", !$(this).prop("selected"));
                $select.trigger("change"); // 触发change事件更新计数
            });

            // 选择变化时更新计数
            $select.on("change", updateSelectedCount);

            // 更新已选项数量
            function updateSelectedCount() {
                const selectedCount = $select.find("option:selected").length;
                $countDisplay.text(selectedCount);
            }
        });
    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>

    <script src="https://code.jquery.com/ui/1.13.1/jquery-ui.min.js"></script>

    <script>
        $(function() {
            // 初始化隐藏字段的值
            function initializeMenuOrder() {
                var menuOrder = [];
                var sort = 0;

                // 处理一级菜单
                $(".sortable-menu > .parent").each(function() {
                    sort++;
                    var menuId = $(this).data("menu-id");
                    menuOrder.push({
                        id: menuId,
                        parent_id: 0,
                        sort: sort
                    });

                    // 处理二级菜单
                    var childOrder = 0;
                    $(this).next(".child-container").find("> .child").each(function() {
                        childOrder++;
                        var childId = $(this).data("menu-id");
                        menuOrder.push({
                            id: childId,
                            parent_id: menuId,
                            sort: childOrder
                        });

                        // 处理三级菜单
                        var child2Order = 0;
                        $(this).next(".child2-container").find("> .child2").each(function() {
                            child2Order++;
                            var child2Id = $(this).data("menu-id");
                            menuOrder.push({
                                id: child2Id,
                                parent_id: childId,
                                sort: child2Order
                            });
                        });
                    });
                });

                // 设置初始值
                $("#menuOrderInput").val(JSON.stringify(menuOrder));
            }

            // 页面加载时初始化
            initializeMenuOrder();

            // 更新菜单顺序函数
            function updateMenuOrder() {
                var menuOrder = [];
                var sort = 0;

                // 处理一级菜单
                $(".sortable-menu > .parent").each(function() {
                    sort++;
                    var menuId = $(this).data("menu-id");
                    menuOrder.push({
                        id: menuId,
                        parent_id: 0,
                        sort: sort
                    });

                    // 处理二级菜单
                    var childOrder = 0;
                    $(this).next(".child-container").find("> .child").each(function() {
                        childOrder++;
                        var childId = $(this).data("menu-id");
                        menuOrder.push({
                            id: childId,
                            parent_id: menuId,
                            sort: childOrder
                        });

                        // 处理三级菜单
                        var child2Order = 0;
                        $(this).next(".child2-container").find("> .child2").each(function() {
                            child2Order++;
                            var child2Id = $(this).data("menu-id");
                            menuOrder.push({
                                id: child2Id,
                                parent_id: childId,
                                sort: child2Order
                            });
                        });
                    });
                });

                // 更新隐藏字段的值
                $("#menuOrderInput").val(JSON.stringify(menuOrder));
            }

            // 初始化所有可排序的容器
            $(".sortable-menu").sortable({
                items: "> .parent, > .child-container > .child, > .child-container > .child2-container > .child2",
                handle: ".handle",
                placeholder: "ui-state-highlight",
                start: function(e, ui) {
                    // 拖拽开始时可以添加额外逻辑
                },
                change: function(e, ui) {
                    // 拖拽过程中实时更新
                    updateMenuOrder();
                },
                update: function(e, ui) {
                    // 拖拽结束后更新
                    updateMenuOrder();
                }
            });

            // 允许子容器内的元素排序
            $(".child-container").sortable({
                items: "> .child, > .child2-container > .child2",
                handle: ".handle",
                connectWith: ".child-container",
                placeholder: "ui-state-highlight",
                change: function(e, ui) {
                    updateMenuOrder();
                },
                update: function(e, ui) {
                    updateMenuOrder();
                }
            });

            $(".child2-container").sortable({
                items: "> .child2",
                handle: ".handle",
                connectWith: ".child2-container",
                placeholder: "ui-state-highlight",
                change: function(e, ui) {
                    updateMenuOrder();
                },
                update: function(e, ui) {
                    updateMenuOrder();
                }
            });

            // 禁止不同级别之间的拖拽
            $(".sortable-menu, .child-container, .child2-container").on("sortreceive", function(event, ui) {
                var senderParentLevel = $(ui.sender).closest('[class*="container"]').length;
                var receiverParentLevel = $(this).closest('[class*="container"]').length;

                if (senderParentLevel !== receiverParentLevel) {
                    $(ui.sender).sortable("cancel");
                }
            });

        });
    </script>

</body>
</html>