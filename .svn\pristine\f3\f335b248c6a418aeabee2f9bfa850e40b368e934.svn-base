/**
 * 评论系统核心模块
 * 负责评论的增删改查和状态管理
 *
 * ===== 后端对接说明 =====
 *
 * 1. 数据结构说明：
 *    - 主评论：直接回复帖子的评论
 *    - 子评论：回复主评论的评论（楼中楼）
 *
 * 2. 需要的后端API接口：
 *    - GET  /api/comments/{postId}     - 获取帖子的所有评论
 *    - POST /api/comments              - 添加主评论
 *    - POST /api/comments/{commentId}/replies - 添加子评论
 *    - DELETE /api/comments/{commentId} - 删除评论
 *
 * 3. 数据库表结构建议：
 *    comments表字段：
 *    - id (主键)
 *    - post_id (帖子ID)
 *    - parent_id (父评论ID，主评论为NULL，子评论为父评论ID)
 *    - user_id (用户ID)
 *    - content (评论内容，支持@用户名：格式)
 *    - created_at (创建时间)
 *    - updated_at (更新时间)
 */
window.CommentSystem = (function() {
    'use strict';

    // 评论数据存储
    let comments = [];
    let commentCounter = 0;

    /**
     * ===== 后端数据格式说明 =====
     *
     * 评论对象结构 (Comment Object):
     * {
     *   id: number,              // 评论ID (后端生成)
     *   content: string,         // 评论内容 (支持@用户名：格式)
     *   user: {                  // 用户信息对象
     *     id: number,            // 用户ID
     *     username: string,      // 用户名
     *     avatar: string,        // 头像URL
     *     userLevel: string      // 用户等级 (如: "普通用户", "高级用户")
     *   },
     *   time: string,            // 发布时间 (格式: "YYYY-MM-DD HH:mm:ss")
     *   parentId: number|null,   // 父评论ID (主评论为null，子评论为父评论ID)
     *   replies: Array,          // 子回复数组 (主评论包含，子评论为空数组)
     *   replyToUser: string|null // 回复的目标用户名 (仅子评论有值)
     * }
     *
     * API响应格式建议:
     * {
     *   "success": true,
     *   "data": {
     *     "comments": [...],     // 评论数组
     *     "total": 10           // 总评论数
     *   },
     *   "message": "操作成功"
     * }
     */

    // 配置选项
    const config = {
        maxCommentLength: 1000,
        minCommentLength: 2,
        autoSave: false,
        enableNotifications: true
    };

    // 事件回调
    const callbacks = {
        onCommentAdded: [],
        onCommentDeleted: [],
        onCommentUpdated: [],
        onReplyAdded: []
    };

    /**
     * 初始化评论系统
     * @param {object} options - 配置选项
     */
    function init(options = {}) {
        Object.assign(config, options);

        // 只有在评论数组为空时才加载现有评论
        if (comments.length === 0) {
            // console.log('首次初始化，加载现有评论...');
            loadExistingComments();
        } else {
            // console.log(`评论系统已初始化，当前有 ${comments.length} 个评论`);
        }

        updateCommentCount();
    }

    /**
     * 加载现有评论（从DOM中提取）
     */
    function loadExistingComments() {
        // 查找所有主评论容器
        $('.reply-list[data-comment-id]').each(function() {
            const $replyList = $(this);
            const commentId = parseInt($replyList.attr('data-comment-id'));
            const $item = $replyList.find('.reply-list-item').first();

            if ($item.length) {
                const comment = extractCommentFromDOM($item, commentId);
                if (comment) {
                    comments.push(comment);
                    commentCounter = Math.max(commentCounter, comment.id);

                    // 加载该评论的回复
                    loadRepliesForComment(comment, $replyList);
                }
            }
        });

        // console.log('已加载现有评论:', comments);
    }

    /**
     * 从DOM元素提取评论数据
     * @param {jQuery} $element - DOM元素
     * @param {number} commentId - 评论ID（可选）
     * @returns {object|null} 评论数据
     */
    function extractCommentFromDOM($element, commentId = null) {
        try {
            const $userLink = $element.find('.iCommunity-right-title-name a');
            const $content = $element.find('.iCommunity-right-content-info p');
            const $time = $element.find('.iCommunity-right-title-time');
            const $avatar = $element.find('.iCommunity-left img');

            if (!$userLink.length || !$content.length) {
                return null;
            }

            const id = commentId || ++commentCounter;

            // 查找用户等级 - 使用更安全的方法
            let userLevel = '普通用户';
            try {
                // 方法1: 使用属性选择器
                let $levelElement = $element.find('[class*="text-[#999]"]').first();
                if ($levelElement.length) {
                    userLevel = $levelElement.text().trim();
                } else {
                    // 方法2: 查找常见的用户等级容器
                    $levelElement = $element.find('.iCommunity-right-info .text-gray-500, .iCommunity-right-info [class*="text-gray"]').first();
                    if ($levelElement.length) {
                        userLevel = $levelElement.text().trim();
                    } else {
                        // 方法3: 遍历查找包含灰色文本样式的元素
                        $element.find('*').each(function() {
                            const $this = $(this);
                            const className = $this.attr('class') || '';
                            const text = $this.text().trim();

                            // 检查是否是用户等级相关的类名和文本
                            if ((className.includes('text-[#999]') ||
                                 className.includes('text-gray-500') ||
                                 className.includes('text-gray-400')) &&
                                text &&
                                text !== $userLink.text().trim() &&
                                text !== $time.text().trim() &&
                                text.length < 50) { // 用户等级通常比较短
                                userLevel = text;
                                return false; // 跳出循环
                            }
                        });
                    }
                }
            } catch (levelError) {
                console.warn('获取用户等级失败，使用默认值:', levelError);
            }

            return {
                id: id,
                content: $content.text().trim(),
                user: {
                    username: $userLink.text().trim(),
                    avatar: $avatar.attr('src') || '/images/user.jpg',
                    userLevel: userLevel
                },
                time: $time.text().trim() || CommentUtils.formatTime(),
                replies: [],
                parentId: null
            };
        } catch (error) {
            console.error('提取评论数据失败:', error);
            return null;
        }
    }

    /**
     * 加载指定评论的回复
     * @param {object} comment - 父评论对象
     * @param {jQuery} $replyList - 回复列表容器
     */
    function loadRepliesForComment(comment, $replyList) {
        const $replyItems = $replyList.find('.comment-reply-item');

        $replyItems.each(function() {
            const $replyItem = $(this);
            const reply = extractCommentFromDOM($replyItem, ++commentCounter);

            if (reply) {
                reply.parentId = comment.id;
                comment.replies.push(reply);
            }
        });

        // console.log(`评论 ${comment.id} 加载了 ${comment.replies.length} 个回复`);
    }

    /**
     * 添加主评论
     *
     * ===== 后端对接说明 =====
     * 需要调用: POST /api/comments
     * 请求参数: {
     *   post_id: number,    // 帖子ID
     *   content: string,    // 评论内容
     *   user_id: number     // 用户ID (从session获取)
     * }
     *
     * @param {string} content - 评论内容
     * @param {object} user - 用户信息
     * @returns {object} 评论数据
     */
    function addMainComment(content, user = null) {
        // 验证评论内容
        const validation = CommentUtils.validateComment(content);
        if (!validation.valid) {
            throw new Error(validation.message);
        }

        // 获取用户信息
        const currentUser = user || CommentUtils.getCurrentUser();
        
        // 创建评论对象
        const comment = {
            id: ++commentCounter,
            content: content.trim(),
            user: currentUser,
            time: CommentUtils.formatTime(),
            replies: [],
            parentId: null,
            createdAt: new Date()
        };

        // 添加到评论列表
        comments.push(comment);

        // 生成HTML并插入DOM
        const commentHtml = TemplateManager.generateMainComment(comment);
        $('.item-reply-list').append(commentHtml);

        // 更新评论计数（会自动处理"暂无评论"提示的显示/隐藏）
        updateCommentCount();

        // 触发回调
        triggerCallback('onCommentAdded', comment);

        // 显示成功提示
        if (config.enableNotifications) {
            CommentUtils.showMessage('评论发布成功！', 'success');
        }

        return comment;
    }

    /**
     * 添加回复评论 (子评论/楼中楼)
     *
     * ===== 后端对接说明 =====
     * 需要调用: POST /api/comments/{commentId}/replies
     * 请求参数: {
     *   parent_id: number,     // 父评论ID
     *   content: string,       // 回复内容 (格式: "@用户名：回复内容")
     *   user_id: number,       // 回复用户ID (从session获取)
     *   reply_to_user: string  // 被回复的用户名 (可选)
     * }
     *
     * 注意: content格式为 "@用户名：回复内容"，后端需要解析这个格式
     *
     * @param {number} parentId - 父评论ID
     * @param {string} content - 回复内容
     * @param {object} user - 用户信息
     * @param {string} replyToUser - 回复的目标用户
     * @returns {object} 回复数据
     */
    function addReply(parentId, content, user = null, replyToUser = null) {
        // 验证评论内容
        const validation = CommentUtils.validateComment(content);
        if (!validation.valid) {
            throw new Error(validation.message);
        }

        // 查找父评论
        const parentComment = findCommentById(parentId);
        if (!parentComment) {
            throw new Error('未找到父评论');
        }

        // 获取用户信息
        const currentUser = user || CommentUtils.getCurrentUser();

        // 创建回复对象
        const reply = {
            id: ++commentCounter,
            content: content.trim(),
            user: currentUser,
            time: CommentUtils.formatTime(),
            parentId: parentId,
            replyToUser: replyToUser,
            createdAt: new Date()
        };

        // 添加到父评论的回复列表
        parentComment.replies.push(reply);

        // 生成HTML并插入DOM
        const replyHtml = TemplateManager.generateSubComment(reply);
        const $parentElement = $(`.reply-list[data-comment-id="${parentId}"]`);
        const $replyContainer = $parentElement.find('.l_comment_list');
        
        if ($replyContainer.length) {
            $replyContainer.append(replyHtml);
        } else {
            console.error('未找到回复容器');
            throw new Error('无法添加回复');
        }

        // 更新回复计数
        updateReplyCount(parentId);

        // 触发回调
        triggerCallback('onReplyAdded', reply, parentComment);

        // 显示成功提示
        if (config.enableNotifications) {
            CommentUtils.showMessage('回复发布成功！', 'success');
        }

        return reply;
    }

    /**
     * 根据ID查找评论
     * @param {number} commentId - 评论ID
     * @returns {object|null} 评论对象
     */
    function findCommentById(commentId) {
        for (let comment of comments) {
            if (comment.id === commentId) {
                return comment;
            }
            // 在回复中查找
            for (let reply of comment.replies) {
                if (reply.id === commentId) {
                    return reply;
                }
            }
        }
        return null;
    }

    /**
     * 删除评论
     * @param {number} commentId - 评论ID
     * @returns {boolean} 是否删除成功
     */
    function deleteComment(commentId) {
        // 查找并删除主评论
        const commentIndex = comments.findIndex(c => c.id === commentId);
        if (commentIndex !== -1) {
            const deletedComment = comments.splice(commentIndex, 1)[0];
            $(`.reply-list[data-comment-id="${commentId}"]`).remove();
            
            updateCommentCount();
            triggerCallback('onCommentDeleted', deletedComment);
            
            if (config.enableNotifications) {
                CommentUtils.showMessage('评论已删除', 'info');
            }
            return true;
        }

        // 查找并删除回复
        for (let comment of comments) {
            const replyIndex = comment.replies.findIndex(r => r.id === commentId);
            if (replyIndex !== -1) {
                const deletedReply = comment.replies.splice(replyIndex, 1)[0];
                $(`.comment-reply-item[data-comment-id="${commentId}"]`).remove();
                
                updateReplyCount(comment.id);
                triggerCallback('onCommentDeleted', deletedReply);
                
                if (config.enableNotifications) {
                    CommentUtils.showMessage('回复已删除', 'info');
                }
                return true;
            }
        }

        return false;
    }

    /**
     * 更新评论计数
     */
    function updateCommentCount() {
        const totalComments = comments.length;
        const $header = $('.p_postlist header');
        if ($header.length) {
            $header.text(`${totalComments} replies`);
        }

        // 根据评论数量显示或隐藏"暂无评论"提示
        const $noCommentTip = $('.no-comment-tip');

        if (totalComments === 0) {
            // 没有评论时显示提示
            if ($noCommentTip.length === 0) {
                $('.item-reply-list').append('<div class="no-comment-tip py-10 text-center">暂无评论</div>');
            }
        } else {
            // 有评论时移除提示
            $noCommentTip.remove();
        }
    }

    /**
     * 更新回复计数
     * @param {number} parentId - 父评论ID
     */
    function updateReplyCount(parentId) {
        const parentComment = findCommentById(parentId);
        if (parentComment) {
            const replyCount = parentComment.replies.length;
            const $replyButton = $(`.reply-list[data-comment-id="${parentId}"] .lzl_link_unfold span`);
            $replyButton.text(`Reply(${replyCount})`);
        }
    }

    /**
     * 获取所有评论
     * @returns {array} 评论列表
     */
    function getAllComments() {
        return [...comments];
    }

    /**
     * 获取评论统计信息
     * @returns {object} 统计信息
     */
    function getStats() {
        const totalReplies = comments.reduce((sum, comment) => sum + comment.replies.length, 0);
        return {
            totalComments: comments.length,
            totalReplies: totalReplies,
            totalItems: comments.length + totalReplies
        };
    }

    /**
     * 注册事件回调
     * @param {string} event - 事件名称
     * @param {function} callback - 回调函数
     */
    function on(event, callback) {
        if (callbacks[event]) {
            callbacks[event].push(callback);
        }
    }

    /**
     * 触发事件回调
     * @param {string} event - 事件名称
     * @param {...any} args - 参数
     */
    function triggerCallback(event, ...args) {
        if (callbacks[event]) {
            callbacks[event].forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`回调执行失败 [${event}]:`, error);
                }
            });
        }
    }

    /**
     * 清空所有评论
     */
    function clearAll() {
        comments = [];
        commentCounter = 0;
        $('.item-reply-list').empty();
        updateCommentCount();
        
        if (config.enableNotifications) {
            CommentUtils.showMessage('所有评论已清空', 'info');
        }
    }

    // 公开API
    return {
        init,
        addMainComment,
        addReply,
        deleteComment,
        findCommentById,
        getAllComments,
        getStats,
        clearAll,
        on,
        config
    };
})();
