<?php
// +----------------------------------------------------------------------
// | 后台管理Session配置
// +----------------------------------------------------------------------

return [
    // session name - 使用不同的session名称实现前后台分离
    'name'           => 'ADMIN_PHPSESSID',
    // SESSION_ID的提交变量,解决flash上传跨域
    'var_session_id' => '',
    // 驱动方式 支持file cache
    'type'           => 'file',
    // 存储连接标识 当type使用cache的时候有效
    'store'          => null,
    // 过期时间 - 后台可以设置更长的过期时间
    'expire'         => 4*3600,  // 4小时
    // 前缀 - 使用不同的前缀避免冲突
    'prefix'         => 'admin_',
];
