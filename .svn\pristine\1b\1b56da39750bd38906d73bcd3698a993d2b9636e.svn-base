/**
 * 工具函数模块
 * 提供通用的工具函数，如时间格式化、DOM操作等
 *
 * ===== 后端对接说明 =====
 *
 * 1. 数据验证：
 *    - validateComment(): 验证评论内容长度和格式
 *    - 前端验证不能替代后端验证，后端仍需要验证
 *
 * 2. HTML处理：
 *    - stripHtml(): 清理HTML标签和实体字符
 *    - escapeHtml(): 转义HTML特殊字符防止XSS
 *    - 后端存储前也应该进行相同的处理
 *
 * 3. 用户信息：
 *    - getCurrentUser(): 获取当前登录用户信息
 *    - 实际应该从后端session或token获取
 *
 * 4. 时间格式：
 *    - formatTime(): 格式化时间显示
 *    - 建议后端返回标准时间格式，前端负责显示格式化
 */
window.CommentUtils = (function() {
    'use strict';

    /**
     * 格式化时间为指定格式
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的时间字符串
     */
    function formatTime(date = new Date()) {
        const timeString = date.toLocaleString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }) + ' on ' + date.toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric'
        });
        return timeString;
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    function generateId() {
        return 'comment_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 清理HTML标签，只保留文本内容
     * @param {string} html - HTML字符串
     * @returns {string} 纯文本内容
     */
    function stripHtml(html) {
        // 创建临时DOM元素来正确解析HTML实体
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // 获取纯文本内容，这会自动处理HTML实体
        let textContent = tempDiv.textContent || tempDiv.innerText || '';

        // 清理多余的空白字符
        textContent = textContent.replace(/\s+/g, ' ').trim();

        return textContent;
    }

    /**
     * 转义HTML特殊字符
     * @param {string} text - 需要转义的文本
     * @returns {string} 转义后的文本
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示提示消息
     * @param {string} message - 提示消息
     * @param {string} type - 消息类型 (success, error, warning, info)
     */
    function showMessage(message, type = 'info') {
        // 简单的提示实现，可以根据需要替换为更复杂的提示组件
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 创建临时提示元素
        const alertDiv = $(`
            <div class="fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white ${getAlertClass(type)} animate-fade-in">
                ${escapeHtml(message)}
            </div>
        `);
        
        $('body').append(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            alertDiv.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    /**
     * 获取提示样式类
     * @param {string} type - 消息类型
     * @returns {string} CSS类名
     */
    function getAlertClass(type) {
        const classes = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        return classes[type] || classes.info;
    }

    /**
     * 验证评论内容
     * @param {string} content - 评论内容
     * @returns {object} 验证结果 {valid: boolean, message: string}
     */
    function validateComment(content) {
        if (!content || !content.trim()) {
            return {
                valid: false,
                message: '请输入评论内容'
            };
        }

        if (content.trim().length < 2) {
            return {
                valid: false,
                message: '评论内容至少需要2个字符'
            };
        }

        if (content.trim().length > 1000) {
            return {
                valid: false,
                message: '评论内容不能超过1000个字符'
            };
        }

        return {
            valid: true,
            message: ''
        };
    }

    /**
     * 防抖函数
     * @param {Function} func - 需要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 需要节流的函数
     * @param {number} limit - 时间限制（毫秒）
     * @returns {Function} 节流后的函数
     */
    function throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 平滑滚动到指定元素
     * @param {string|jQuery} target - 目标元素选择器或jQuery对象
     * @param {number} duration - 动画持续时间（毫秒）
     */
    function scrollToElement(target, duration = 500) {
        const $target = typeof target === 'string' ? $(target) : target;
        if ($target.length) {
            $('html, body').animate({
                scrollTop: $target.offset().top - 20 // 留一点间距
            }, duration);
        }
    }

    /**
     * 获取用户数据（模拟函数，实际项目中应该从后端获取）
     * @returns {object} 用户数据
     */
    function getCurrentUser() {
        // 这里应该从实际的用户系统获取数据
        return {
            id: 'user_' + Math.random().toString(36).substr(2, 9),
            avatar: '/images/user-1.jpg',
            username: '当前用户',
            userLevel: '普通用户'
        };
    }

    /**
     * 检查元素是否在视口中
     * @param {jQuery} $element - jQuery元素对象
     * @returns {boolean} 是否在视口中
     */
    function isElementInViewport($element) {
        if (!$element.length) return false;
        
        const elementTop = $element.offset().top;
        const elementBottom = elementTop + $element.outerHeight();
        const viewportTop = $(window).scrollTop();
        const viewportBottom = viewportTop + $(window).height();
        
        return elementBottom > viewportTop && elementTop < viewportBottom;
    }

    // 公开API
    return {
        formatTime,
        generateId,
        stripHtml,
        escapeHtml,
        showMessage,
        validateComment,
        debounce,
        throttle,
        scrollToElement,
        getCurrentUser,
        isElementInViewport
    };
})();
