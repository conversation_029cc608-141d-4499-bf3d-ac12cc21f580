<?php /*a:2:{s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\pages\index.html";i:1742277739;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;}*/ ?>
<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>
<body>
<div class="menus_r">
    <div id="con_two_1" style="display: block;">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：<a href="<?php echo url('Public/main'); ?>">首页</a>&nbsp;>&nbsp; <a href="">页面列表</a></p>

            <a href="<?php echo url('add'); ?>" class="add-button">添加页面</a>
        </div>
        <!--column end-->
        <div class="form_con">
            <table class="bor_cen">
                <thead>
                    <tr class="mid_01">
                        <td class="mid_one"></td>
                        <td class="mid_t">页面名称</td>
                        <td class="mid_one">排序</td>
                        <td class="mid_s">操作</td>
                    </tr>
                </thead>

                <tbody>
                <?php if(is_array($List) || $List instanceof \think\Collection || $List instanceof \think\Paginator): $key = 0; $__LIST__ = $List;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($key % 2 );++$key;?>
                    <tr class="mid_02">
                        <td class="mid_one"><?php echo htmlentities((string) $key); ?></td>
                        <td class="mid_t" style="text-align: left;padding-left: 15px;"><?php echo htmlentities((string) $vo['name']); ?></td>
                        <td class="mid_one"><?php echo htmlentities((string) $vo['sort']); ?></td>
                        <td class="mid_s">
                            <a href="<?php echo url('del', ['id'=>$vo['id']]); ?>" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                            <a href="<?php echo url('edit', ['id'=>$vo['id']]); ?>" class="basic">修改</a>
                        </td>
                    </tr>

                    <?php if(is_array($vo['son']) || $vo['son'] instanceof \think\Collection || $vo['son'] instanceof \think\Paginator): $k = 0; $__LIST__ = $vo['son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($k % 2 );++$k;?>
                    <tr class="mid_02">
                        <td class="mid_one"><?php echo htmlentities((string) $k); ?></td>
                        <td class="mid_t" style="text-align: left;">　|——<?php echo htmlentities((string) $v['name']); ?></td>
                        <td class="mid_one"><?php echo htmlentities((string) $v['sort']); ?></td>
                        <td class="mid_s">
                            <a href="<?php echo url('del', ['id'=>$v['id']]); ?>" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                            <a href="<?php echo url('edit', ['id'=>$v['id']]); ?>" class="basic">修改</a>
                        </td>
                    </tr>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                <?php endforeach; endif; else: echo "" ;endif; ?>
                </tbody>
            </table>

            <div class="interpret">
                <div class="page">

                </div>
            </div>
        </div>
        <!-- form_con end -->
    </div>
</div>
</body>
</html>