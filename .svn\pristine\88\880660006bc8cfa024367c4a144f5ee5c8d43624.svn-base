<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>测试邮件</title>
</head>

<body>
    <div>
        <form>
            <input name="send_email" type="text" value="<EMAIL>" />
            <button type="button" id="send">发送邮箱验证码</button>
        </form>
    </div>

    <div style="margin-top: 50px;">
        <form>
            <input name="verify_email" type="text" value="<EMAIL>" />
            <input name="captcha" type="text" value="123456" />
            <button type="button" id="verify">验证邮箱验证码</button>
        </form>
    </div>

    <script src="__JS__/jquery.min.js"></script>

    <script>
        //发送邮箱验证码
        $("#send").click(function(){
            let email = $("input[name=send_email]").val();

            // 前端调用示例
            fetch('/send_captcha', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => console.log(data));
        })

        //验证邮箱验证码
        $("#verify").click(function(){
            let email = $("input[name=verify_email]").val();
            let captcha = $("input[name=captcha]").val();

            // 前端调用示例
            fetch('/email/verify_captcha', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    captcha: captcha
                })
            })
            .then(response => response.json())
            .then(data => console.log(data));
        })

    </script>
</body>

</html>