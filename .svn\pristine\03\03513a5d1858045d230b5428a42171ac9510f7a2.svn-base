<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Pages extends Common
{
    public function index(){
        $where = [
            "page_id" => 0
        ];

        $List = Db::name('Pages')
        ->where($where)
        ->order("sort asc")
        ->select()
        ->each(function($item){
            //子分类
            $where = [
                "page_id" => $item['id']
            ];
            $son = Db::name('Pages')
            ->where($where)
            ->order("sort asc")
            ->select();
            $item['son'] = $son;

            return $item;
        });

        return view("", [
            "List" => $List
        ]);
    }

    public function add(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("页面名称不能为空！");
            }

            if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));
            if($_FILES['image_smt']['name']) $data['image_smt'] = $this->upload(request()->file("image_smt"));

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Pages")->insertGetId($data);
            if($s){
                $this->success('添加成功');
            } else {
                $this->error("添加失败，请重试");
            }
        } else {
            //提示词一级分类
            $where = [
                "page_id" => 0
            ];
            $List = Db::name('Pages')
            ->where($where)
            ->order("sort asc")
            ->select();

            return view("", [
                "pages" => $List
            ]);
        }
    }

    public function edit(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("页面名称不能为空！");
            }

            if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));
            if($_FILES['image_smt']['name']) $data['image_smt'] = $this->upload(request()->file("image_smt"));

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Pages")->where("id", $data['id'])->save($data);
            if($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }

        } else {
            $id = input('id');
            $getone = Db::name("Pages")->where("id", $id)->find();

            //提示词一级分类
            $where = [
                "page_id" => 0
            ];
            $List = Db::name('Pages')
            ->where($where)
            ->order("sort asc")
            ->select();

            return view("", [
                "getone" => $getone,
                "pages" => $List
            ]);
        }
    }

    public function del(){
        $id = input('id');
        $s = Db::name("Pages")->where("id", $id)->delete();
        if($s){
            $this->success("删除成功");
        }else{
            $this->error("删除失败");
        }
    }

}
