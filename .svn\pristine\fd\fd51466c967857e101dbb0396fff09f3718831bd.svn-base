<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">FAQ分类</a></p>
                <a href="{:url('add_category')}" class="add-button">添加分类</a>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">分类名称</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="key" id="vo"}
                        <tr class="mid_02">
                            <td class="mid_one">{$key}</td>
                            <td class="mid_t" style="text-align: left;padding-left: 15px;">{$vo.name}</td>
                            <td class="mid_s">
                                <a href="{:url('del_category', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit_category', ['id'=>$vo['id']])}" class="basic">修改</a>
                            </td>
                        </tr>

                        {volist name="vo['son']" id="v" key="k"}
                        <tr class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_t" style="text-align: left;">　|——{$v.name}</td>
                            <td class="mid_s">
                                <a href="{:url('del_category', ['id'=>$v['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit_category', ['id'=>$v['id']])}" class="basic">修改</a>
                            </td>
                        </tr>
                        {/volist}
                    {/volist}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>