$(function(){
    // 主菜单点击事件
    $(".menus_l > ul > li.tab").each(function(m){
        $(this).click(function(){
            const childMenu = $(this).find(".menu-child");

            if (childMenu.is(":visible")) {
                // 收缩菜单
                childMenu.slideUp();
                $(this).removeClass("active");
                $(this).find("i").removeClass("cur-icon-"+m);
            } else {
                // 展开菜单
                $(this).siblings().find(".menu-child").slideUp();
                $(this).addClass("active").siblings().removeClass("active");
                $(this).find("i").addClass("cur-icon-"+m);
                childMenu.slideDown();
            }

            if(childMenu.length < 1){
                //没有子菜单，则将子菜单的active移除
                $(".menu-child li").removeClass("active");
            }
        });
    });

    // 子菜单点击事件
    $(".menu-child li").click(function(e){
        // 阻止事件冒泡到父级
        e.stopPropagation();

        // 移除所有子菜单项的active类
        $(".menu-child li").removeClass("active");
        // 给当前点击的子菜单项添加active类
        $(this).addClass("active");

        // 如果需要保持父菜单的active状态
        $(this).closest(".tab").addClass("active");
    });

    // (可选)点击页面其他区域收缩所有菜单
    $(document).click(function(e) {
        if (!$(e.target).closest('.menus_l > ul > li.tab').length) {
            $(".menu-child").slideUp();
            $(".menus_l > ul > li.tab").removeClass("active");
        }
    });
});