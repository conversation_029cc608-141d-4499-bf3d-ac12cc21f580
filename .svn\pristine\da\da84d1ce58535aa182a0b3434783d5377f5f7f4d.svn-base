<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

class Service extends Common
{
    public function index()
    {
        $seo_url = input("url");

        $getone = Db::name("Service")->where("seo_url", $seo_url)->find();

        if(empty($getone)) {
            return view("error/404")->code(404);
        }

        //资源
        $resource = Db::name("Product_relation")->alias("pr")
        ->field("s.*")
        ->leftjoin("Resource s", "pr.related_id=s.id")
        ->where(["product_id"=>$getone['id'], "type"=>4])
        ->order("pr.sort asc")
        ->select();

        return view("", [
            "getone" => $getone,
            "resource" => $resource,
        ]);
    }

}
