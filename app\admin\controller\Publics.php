<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Publics extends Common
{
    public function main()
    {
        return view();
    }

    public function header()
    {
        return view();
    }

    public function menu()
    {
        //后台菜单列表
        $menus = Db::name("Backend_menus")
            ->field("id, title, icon, url, parent_id")
            ->where(["parent_id"=>0, "status"=>1])
            ->order("sort asc")
            ->select()
            ->each(function ($item) {
                $item['son'] = Db::name("Backend_menus")
                    ->field("id, title, icon, url, parent_id")
                    ->where(["parent_id"=>$item['id'], "status"=>1])
                    ->order("sort asc")
                    ->select()
                    ->each(function ($i) {
                        $i['son'] = Db::name("Backend_menus")
                            ->field("id, title, icon, url, parent_id")
                            ->where(["parent_id"=>$i['id'], "status"=>1])
                            ->order("sort asc")
                            ->select();

                        return $i;
                    });

                return $item;
            });

        $role_id = Db::name("User")->where("id", session('adminId'))->value("role_id");
        $role_menus = Db::name("Role_backend_menus")
            ->where("role_id", $role_id)
            ->column("state", 'menu_id');

        return view("", [
            "menus" => $menus,
            "role_menus" => $role_menus,
        ]);
    }

    public function menugroup()
    {
        return view();
    }


    public function icon()
    {
        return view();
    }

}
