/* ckeditor5.css */
.ck-content * {
    margin: revert;
    padding: revert;
    border: revert;
    font: revert;
    list-style: revert;
	line-height: 1.6;
}

.ck-content ol {
    list-style-type: decimal;
    padding-left: 40px;
    margin: 1em 0;
}

.ck-content ul {
    list-style-type: disc;
    padding-left: 40px;
    margin: 1em 0;
}

.ck-content li {
    display: list-item;
    margin: 0.25em 0;
}

.ck-content p {
    margin: 1em 0;
}

.ck-content span {
    all: revert;
    margin-left: 0 !important;
    color: inherit;
    line-height: inherit !important;
}

.ck-content h1 { font-size: 2em; margin: 0.67em 0; }
.ck-content h2 { font-size: 1.5em; margin: 0.83em 0; }
.ck-content h3 { font-size: 1.17em; margin: 1em 0; }
.ck-content h4 { font-size: 1em; margin: 1.33em 0; }
.ck-content h5 { font-size: 0.83em; margin: 1.67em 0; }
.ck-content h6 { font-size: 0.67em; margin: 2.33em 0; }

/* 基础编辑器样式 */
.ck.ck-editor {
    max-width: 100%;
}

/* 工具栏样式 */
.ck.ck-toolbar {
    border: 1px solid #ddd;
    border-bottom: 0;
    border-radius: 4px 4px 0 0;
    background-color: #f5f5f5;
}

/* 标题样式 */
.ck.ck-content h1,
.ck.ck-content h2,
.ck.ck-content h3,
.ck.ck-content h4,
.ck.ck-content h5,
.ck.ck-content h6 {
    font-weight: bold;
    margin-top: 1.2em;
}

/* 链接样式 */
.ck.ck-content a {
    text-decoration: underline;
}

/* 表格样式 */
.ck.ck-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}

.ck.ck-content table td,
.ck.ck-content table th {
    border: 1px solid #ddd;
    padding: 8px;
}

.ck.ck-content table th {
    background-color: #f5f5f5;
}

/* 代码块样式 */
.ck.ck-content pre {
    background-color: #f5f5f5;
    padding: 1em;
    border-radius: 4px;
    overflow-x: auto;
}

/* 图片样式 */
.ck.ck-content .image {
    margin: 1em 0;
    text-align: center;
}

.ck.ck-content .image > img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.editor_container__word-count {
    border: 1px solid var(--ck-color-base-border);
    border-radius: var(--ck-border-radius);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top: none;
    text-align: right;
    color: #666;
    font-size: 12px;
    /* padding: 5px; */
    background: #f5f5f5;
    border-radius: 0 0 4px 4px;
}
.editor_container__word-count .ck-word-count {
    color: var(--ck-color-text);
    display: flex;
    height: 20px;
    gap: var(--ck-spacing-small);
    justify-content: flex-end;
    font-size: var(--ck-font-size-base);
    line-height: var(--ck-line-height-base);
    font-family: var(--ck-font-face);
    padding: var(--ck-spacing-small) var(--ck-spacing-standard);
}

:root {
    /* Make the mention background blue. */
    --ck-color-mention-background: hsla(220, 100%, 54%, 0.4);
    /* Make the mention text dark grey. */
    --ck-color-mention-text: hsl(0, 0%, 15%);
}


/* 确保编辑器容器和可编辑区域保持高度 */
.ck.ck-editor__editable:not(.ck-editor__nested-editable) {
    min-height: 300px !important;
}

/* 或者更精确的选择器 */
.ck-editor__main > .ck-editor__editable {
    min-height: 300px !important;
    max-height: 500px;
    overflow-y: auto;
}