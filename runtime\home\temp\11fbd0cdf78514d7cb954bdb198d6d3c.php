<?php /*a:1:{s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\email\index.html";i:1747191351;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>测试邮件</title>
</head>

<body>
    <div>
        <form>
            <input name="send_email" type="text" value="<EMAIL>" />
            <button type="button" id="send">发送邮箱验证码</button>
        </form>
    </div>

    <div style="margin-top: 50px;">
        <form>
            <input name="verify_email" type="text" value="<EMAIL>" />
            <input name="code" type="text" value="123456" />
            <button type="button" id="verify">验证邮箱验证码</button>
        </form>
    </div>

    <script src="/static/home/<USER>/jquery.min.js"></script>

    <script>
        //发送邮箱验证码
        $("#send").click(function(){
            let email = $("input[name=send_email]").val();

            // 前端调用示例
            fetch('/email/send_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => console.log(data));
        })

        //验证邮箱验证码
        $("#verify").click(function(){
            let email = $("input[name=verify_email]").val();
            let code = $("input[name=code]").val();

            // 前端调用示例
            fetch('/email/verify_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    code: code
                })
            })
            .then(response => response.json())
            .then(data => console.log(data));
        })

    </script>
</body>

</html>