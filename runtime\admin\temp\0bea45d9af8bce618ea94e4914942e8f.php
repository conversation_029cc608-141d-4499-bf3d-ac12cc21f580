<?php /*a:3:{s:70:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\order\edit_service.html";i:1751010089;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;s:69:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot_layer.html";i:1752808790;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>创建订单</title>

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>

<body>
    <div class="cnt-basic-list cnt-basic-layer order-info" style="display: block;">
        <form action="<?php echo url('edit_service'); ?>" method="post" enctype="multipart/form-data" class="layer-form" autocomplete="off">
            <input type="hidden" name="id" value="<?php echo htmlentities((string) $getone['id']); ?>" />
            <input type="hidden" name="info_id" value="<?php echo htmlentities((string) $getone['info_id']); ?>" />

            <div class="cnt-basic-item">
	        	<div class="cnt-basic-i class_con">
	                <label>选择服务：</label>
                    <select name="service_id" id="service_id" class="l_xiang">
                        <option value="">请选择</option>
                        <?php if(is_array($service) || $service instanceof \think\Collection || $service instanceof \think\Paginator): $i = 0; $__LIST__ = $service;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['service_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
	            </div>

                <div class="cnt-basic-i class_con">
                    <label>服务进度：</label>
                    <select name="service_progress_id" id="service_progress_id" class="l_xiang">
                        <option value="">请选择</option>
                        <?php if(is_array($service_progress) || $service_progress instanceof \think\Collection || $service_progress instanceof \think\Paginator): $i = 0; $__LIST__ = $service_progress;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $vo['id']); ?>" <?php if($getone['service_progress_id']==$vo['id']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="cnt-basic-i class_con">
                    <label>订单金额：</label>
                    <input type="text" name="money" value="<?php echo htmlentities((string) $getone['money']); ?>" style="width: 300px;" />
                    <select name="money_unit" class="l_xiang" style="width: 72px;background: url(/static/admin/images/icon_04.png) 67px 12px no-repeat;margin-left: 4px;">
                        <option value="USD" <?php if($getone['money_unit']=='USD'): ?>selected<?php endif; ?>>USD</option>
                        <option value="RMB" <?php if($getone['money_unit']=='RMB'): ?>selected<?php endif; ?>>RMB</option>
                        <option value="EUR" <?php if($getone['money_unit']=='EUR'): ?>selected<?php endif; ?>>EUR</option>
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="cnt-basic-i class_con">
                    <label>项目号：</label>
                    <input type="text" name="project_no" value="<?php echo htmlentities((string) $getone['project_no']); ?>" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>物流单号：</label>
                    <input type="text" name="tracking_no" value="<?php echo htmlentities((string) $getone['tracking_no']); ?>" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>物流公司：</label>
                    <input type="text" name="tracking_company" value="<?php echo htmlentities((string) $getone['tracking_company']); ?>" />
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    <script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script>
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
				const originalBtnText = $btn.text();
                $btn.prop('disabled', true).text('Submitting...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            parent.location.reload();
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text(originalBtnText);
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    parent.layer.close(window.layerIndex);
                } else {
                    parent.layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

    <script>
        //服务变化，服务进度列表变化
        $('#service_id').on('change', function() {
            var serviceId = $(this).val();
            if (!serviceId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: "/admin/Order/getServiceProgress",
                type: 'GET',
                data: {service_id: serviceId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        //更新服务进度列表
                        var $serviceSelect = $('#service_progress_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" disabled selected>请选择</option>');
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>