<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Order extends Common
{
    public function index()
    {
        $List = Db::name('Order')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['email'] || empty($data['email']) || empty($data['country']) || empty($data['first_name']) || empty($data['last_name']) || empty($data['phone']) || empty($data['organization']) || empty($data['shipping_address']) || empty($data['city']) || empty($data['postcode']) || empty($data['product_id'])){
                $this->error("必填项未填！");
            }

            //如果邮箱存在，则自动归入邮箱所在用户；如果邮箱不存在，则自动创建一个用户账号，密码初始化opd123456。
            $where = [
                "email" => $data['email'],
                "del_status" => 0,
            ];
            $user = Db::name("User")->where($where)->find();
            if(empty($user)){
                //自动创建用户账号
                $userData = [
                    "email" => $data['email'],
                    "first_name" => $data['first_name'],
                    "last_name" => $data['last_name'],
                    "country" => $data['country'],
                    "organization" => $data['organization'],
                    "title" => $data['title'],
                    "phone" => $data['phone'],
                    "create_time" => date("Y-m-d H:i:s"),
                ];

                // 生成salt
                $userData['salt'] = generateSalt();
                // 明码进行加盐hash解决
                $userData['password'] = generateHashPassword("opd123456", $data['salt']);

                $data['user_id'] = Db::name("User")->strict(false)->insertGetId($userData);
            } else {
                //自动归入user_id
                $data['user_id'] = $user['id'];
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Order")->strict(false)->insertGetId($data);
            if ($s) {
                $orderService = [
                    "order_id" => $s,
                    "type" => 0,  //本订单
                    "service_id" => $data['service_id'],
                    "create_time" => date("Y-m-d H:i:s"),
                ];
                Db::name("Order_service")->insert($orderService);

                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            //所有用户
            $users = Db::name("User")->where("del_status", 0)->column("id, email");

            $product = Db::name("Product")->column("id, name");

            return view("", [
                "users" => $users,
                'product' => $product,
            ]);
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['email'] || empty($data['email']) || empty($data['country']) || empty($data['first_name']) || empty($data['last_name']) || empty($data['phone']) || empty($data['organization']) || empty($data['shipping_address']) || empty($data['city']) || empty($data['postcode']) || empty($data['product_id'])){
                $this->error("必填项未填！");
            }

            //如果邮箱存在，则自动归入邮箱所在用户；如果邮箱不存在，则自动创建一个用户账号，密码初始化opd123456。
            $where = [
                "email" => $data['email'],
                "del_status" => 0,
            ];
            $user = Db::name("User")->where($where)->find();
            if(empty($user)){
                //自动创建用户账号
                $userData = [
                    "email" => $data['email'],
                    "first_name" => $data['first_name'],
                    "last_name" => $data['last_name'],
                    "country" => $data['country'],
                    "organization" => $data['organization'],
                    "title" => $data['title'],
                    "phone" => $data['phone'],
                    "create_time" => date("Y-m-d H:i:s"),
                ];

                // 生成salt
                $userData['salt'] = generateSalt();
                // 明码进行加盐hash解决
                $userData['password'] = generateHashPassword("opd123456", $data['salt']);

                $data['user_id'] = Db::name("User")->strict(false)->insertGetId($userData);
            } else {
                //自动归入user_id
                $data['user_id'] = $user['id'];
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Order")->strict(false)->save($data);
            if ($s) {
                $where = [
                    "order_id" => $data['id'],
                    "type" => 0,  //本订单
                ];
                $orderService = Db::name("Order_service")->where($where)->find();
                if($orderService){
                    //修改
                    $where['service_id'] = $data['service_id'];
                    $where['update_time'] = date("Y-m-d H:i:s");
                    Db::name("Order_service")->where("id", $orderService['id'])->save($where);
                } else {
                    //增加
                    $where['service_id'] = $data['service_id'];
                    $where['create_time'] = date("Y-m-d H:i:s");
                    Db::name("Order_service")->insert($where);
                }

                //产品保密文件
                if(isset($data["product_security_url"])) {
                    $product_security_file = $this->handleFiles("product_security_file");
                    foreach ($data['product_security_url'] as $index => $url) {
                        $filePath = $data['product_security_file_path'][$index] ?? ''; // 获取对应的文件路径
                        if ($product_security_file[$index]) {
                            $file = $this->uploadFile($product_security_file[$index], "security");
                            $filePath = $file['url'];
                            $fileName = $file['original_name'];
                        }

                        if($url || $filePath){
                            Db::name("Security_file")->insert([
                                "order_id" => $data['id'],
                                "main_id" => $data['product_id'],
                                "type" => 0,
                                "file" => $filePath,
                                "file_name" => $fileName?$fileName:'',
                                "url" => $url,
                                "create_time" => date("Y-m-d H:i:s")
                            ]);
                        }
                    }
                }

                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Order")->where("id", $id)->find();

            $where = [
                "order_id" => $id,
                "type" => 0,
            ];
            $getone['service_id'] = Db::name("Order_service")->where($where)->value("service_id");

            $service = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["pr.product_id"=>$getone['product_id'], "pr.type"=>1])
                ->order("pr.sort asc")
                ->select();

            //所有用户
            $users = Db::name("User")->where("del_status", 0)->column("id, email");

            $product = Db::name("Product")->column("id, name");

            return view("", [
                "getone" => $getone,
                "users" => $users,
                'product' => $product,
                "service" => $service,
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Order")->where("id", $id)->delete();
        if ($s) {
            Db::name("Order_service")->where("order_id", $id)->delete();

            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }


    public function quote()
    {
        $List = Db::name('Quote')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])->each(function ($item) {
                $item['phone'] = Db::name("User")->where("id", $item['user_id'])->value('phone');

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

    public function view_quote(){
        $id = input('id');
        $getone = Db::name("Quote")->where("id", $id)->find();
        $getone['interest_product'] = json_decode($getone['interest_product']);

        return view("", [
            "getone" => $getone,
        ]);
    }

    /**
     * 根据 user_id 获取 用户基本信息
     */
    public function getUserData()
    {
        $userId = $this->request->post('user_id');
        if (empty($userId)) {
            return json(['code' => 0, 'msg' => '请选择用户']);
        }

        $userData = Db::name("User")->where("id", $userId)->find();

        if ($userData) {
            return json(['code' => 1, 'msg' => '获取成功', 'data' => $userData]);
        } else {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }
    }
}
