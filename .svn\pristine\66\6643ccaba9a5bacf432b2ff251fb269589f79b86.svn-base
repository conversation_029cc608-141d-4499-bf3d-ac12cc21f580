<script src="__JS__/jquery.min.js"></script>
<script src="__STATIC__/layer/layer.js"></script>
<script src="__STATIC__/layer/laydate/laydate.js"></script>

<script>
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
				const originalBtnText = $btn.text();
                $btn.prop('disabled', true).text('Submitting...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            parent.location.reload();
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text(originalBtnText);
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    parent.layer.close(window.layerIndex);
                } else {
                    parent.layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>