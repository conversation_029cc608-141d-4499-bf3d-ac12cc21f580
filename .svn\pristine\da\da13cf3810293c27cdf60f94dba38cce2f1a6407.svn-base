<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改账号
                <a href="{:url('account')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit_account')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>主机：</label>
                    <input type="text" name="host" value="{$getone.host}" />
                    <span class="must-input">*</span>
                </div>
                <div class="class_con">
                    <label>端口：</label>
                    <input type="text" name="port" value="{$getone.port}" />
                    <span class="must-input">*</span>
                </div>
                <div class="class_con">
                    <label>安全协议：</label>
                    <input type="text" name="protocol" value="{$getone.protocol}" />
                </div>
                <div class="class_con">
                    <label>发件邮箱：</label>
                    <input type="text" name="email" value="{$getone.email}" />
                    <span class="must-input">*</span>
                </div>
                <div class="class_con">
                    <label>发件名称：</label>
                    <input type="text" name="from_name" value="{$getone.from_name}" />
                </div>
                <div class="class_con">
                    <label>密码：</label>
                    <input name="password" type="password" value="{$getone.password}" />
                    <span class="must-input">*</span>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('account')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>