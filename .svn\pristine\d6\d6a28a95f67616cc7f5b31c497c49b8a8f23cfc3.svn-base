<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

use app\admin\model\Product as ProductModel;
use app\admin\model\Service as ServiceModel;

class Product extends Common
{
    public function index()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["name|title|description|content", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('Product')
            ->where($where)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            Db::startTrans();
            try {
                // 获取主产品数据
                $data = input('post.');

                if(!$data['name']){
                    throw new \Exception("必填项未填！");
                }

                if(!trim($data['seo_url'])){
                    $data['seo_url'] = seoFriendlyUrl($data['name']);
                }
                if(Db::name('Product')->where("seo_url", $data['seo_url'])->find()){
                    throw new \Exception("URL已存在！");
                }

                if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));
                if($_FILES['image_detail']['name']) $data['image_detail'] = $this->upload(request()->file("image_detail"));
                if($_FILES['image_background']['name']) $data['image_background'] = $this->upload(request()->file("image_background"));
                if($_FILES['image_background_smt']['name']) $data['image_background_smt'] = $this->upload(request()->file("image_background_smt"));

                if(isset($data["testimonial_file_path"])) {
                    $testimonial_file = $this->handleFiles("testimonial_file");
                    foreach ($data["testimonial_file_path"] as $index => $value) {
                        $filePath = $value ?? ''; // 获取对应的文件路径
                        if ($testimonial_file[$index]) $filePath = $this->upload($testimonial_file[$index]);

                        $testimonial[] = [$filePath];
                    }
                    $data['testimonial'] = json_encode($testimonial);
                    unset($data['testimonial_file']);
                    unset($data['testimonial_file_path']);
                }

                if(isset($data["case_file_path"])) {
                    $case_file = $this->handleFiles("case_file");
                    foreach ($data["case_file_path"] as $index => $value) {
                        $filePath = $value ?? ''; // 获取对应的文件路径
                        if ($case_file[$index]) $filePath = $this->upload($case_file[$index]);

                        $case[] = [$filePath];
                    }
                    $data['case'] = json_encode($case);
                    unset($data['case_file']);
                    unset($data['case_file_path']);
                }

                $data['is_recommend'] = isset($data['is_recommend'])?1:0;
                $data['create_time'] = date("Y-m-d H:i:s");

                // 保存主产品
                if (!empty($data['id'])) {
                    $product = ProductModel::find($data['id']);
                    $product->save($data);
                } else {
                    $product = ProductModel::create($data);
                }

                $relatedIds = isset($data['services']) ? $data['services']: [];
                $this->saveProductRelations($product->id, $relatedIds, 1);

                // $relatedIds = isset($data['products']) ? $data['products']: [];
                // $this->saveProductRelations($product->id, $relatedIds, 2);

                $relatedIds = isset($data['resources']) ? $data['resources']: [];
                $this->saveProductRelations($product->id, $relatedIds, 3);

                Db::commit();

                return json(['code' => 1, 'msg' => '修改成功！']);
            } catch (\Throwable $e) {
                Db::rollback();

                return json(['code' => 0, 'msg' => "修改失败，请重试！".$e->getMessage()]);
            }

        } else {
            //服务
            $service = Db::name("Service")->column("id, name");

            //相关产品
            // $product = Db::name("Product")->select();

            //资源
            $resource = Db::name("Resource")->select();

            //产品经理
            $where = [
                "role_id" => 2,  //产品经理角色
                "status" => 1
            ];
            $product_manager = Db::name("User")->where($where)->column("id, email, first_name, last_name");
            // 转换为前端需要的格式
            $tagsOptions = [];
            foreach ($product_manager as $tag) {
                $tagsOptions[] = [
                    'value' => $tag['id'],  // 使用id作为value
                    'label' => $tag['first_name']." ".$tag['last_name'],  // 使用name作为label
                ];
            }

            return view("", [
                "service" => $service,
                // "product" => $product,
                "resource" => $resource,
                "tagsOptions" => json_encode($tagsOptions),
            ]);
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            Db::startTrans();
            try {
                // 获取主产品数据
                $data = input('post.');

                if(!$data['name']){
                    throw new \Exception("必填项未填！");
                }

                if(!trim($data['seo_url'])){
                    $data['seo_url'] = seoFriendlyUrl($data['name']);
                }
                $where = [
                    ['id', '<>', $data['id']],
                    ['seo_url', '=', $data['seo_url']],
                ];
                if(Db::name('Product')->where($where)->find()){
                    throw new \Exception("URL已存在！");
                }

                if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));
                if($_FILES['image_detail']['name']) $data['image_detail'] = $this->upload(request()->file("image_detail"));
                if($_FILES['image_background']['name']) $data['image_background'] = $this->upload(request()->file("image_background"));
                if($_FILES['image_background_smt']['name']) $data['image_background_smt'] = $this->upload(request()->file("image_background_smt"));

                if(isset($data["testimonial_file_path"])) {
                    $testimonial_file = $this->handleFiles("testimonial_file");
                    foreach ($data["testimonial_file_path"] as $index => $value) {
                        $filePath = $value ?? ''; // 获取对应的文件路径
                        if ($testimonial_file[$index]) $filePath = $this->upload($testimonial_file[$index]);

                        if($filePath) $testimonial[] = [$filePath];
                    }
                    $data['testimonial'] = json_encode($testimonial);
                    unset($data['testimonial_file']);
                    unset($data['testimonial_file_path']);
                }

                if(isset($data["case_file_path"])) {
                    $case_file = $this->handleFiles("case_file");
                    foreach ($data["case_file_path"] as $index => $value) {
                        $filePath = $value ?? ''; // 获取对应的文件路径
                        if ($case_file[$index]) $filePath = $this->upload($case_file[$index]);

                        if($filePath) $case[] = [$filePath];
                    }
                    $data['case'] = json_encode($case);
                    unset($data['case_file']);
                    unset($data['case_file_path']);
                }

                $data['is_recommend'] = isset($data['is_recommend'])?1:0;
                $data['update_time'] = date("Y-m-d H:i:s");

                // 保存主产品
                if (!empty($data['id'])) {
                    $product = ProductModel::find($data['id']);
                    $product->save($data);
                } else {
                    $product = ProductModel::create($data);
                }

                $relatedIds = isset($data['services']) ? $data['services']: [];
                $this->saveProductRelations($product->id, $relatedIds, 1);

                // $relatedIds = isset($data['products']) ? $data['products']: [];
                // $this->saveProductRelations($product->id, $relatedIds, 2);

                $relatedIds = isset($data['resources']) ? $data['resources']: [];
                $this->saveProductRelations($product->id, $relatedIds, 3);

                Db::commit();

                return json(['code' => 1, 'msg' => '修改成功！']);
            } catch (\Throwable $e) {
                Db::rollback();

                return json(['code' => 0, 'msg' => "修改失败，请重试！".$e->getMessage()]);
            }

        } else {
            $id = input('id');
            $getone = Db::name("Product")->where("id", $id)->find();

            //服务
            $productService = Db::name("Product_relation")
            ->where(["product_id"=>$id, "type"=>1])
            ->order("sort asc")
            ->select()
            ->each(function ($item) {
                $item['name'] = Db::name("Service")->where('id', $item['related_id'])->value('name');

                return $item;
            });

            //相关产品
            // $productRelation = Db::name("Product_relation")
            // ->where(["product_id"=>$id, "type"=>2])
            // ->order("sort asc")
            // ->select()
            // ->each(function ($item) {
            //     $item['name'] = Db::name("Product")->where('id', $item['related_id'])->value('name');

            //     return $item;
            // });

            //资源
            $productResource = Db::name("Product_relation")
            ->where(["product_id"=>$id, "type"=>3])
            ->order("sort asc")
            ->select()
            ->each(function ($item) {
                $item['name'] = Db::name("Resource")->where('id', $item['related_id'])->value('name');

                return $item;
            });

            //所有服务
            $service = Db::name("Service")->column("id, name");

            //所有产品
            $product = Db::name("Product")->where("id", "<>", $id)->select();

            //所有资源
            $resource = Db::name("Resource")->select();

            $getone["testimonial"] = $getone["testimonial"] ?? '{}'; // 如果为空，默认设置为空对象
            $testimonial = json_decode($getone["testimonial"], true);

            $getone["case"] = $getone["case"] ?? '{}'; // 如果为空，默认设置为空对象
            $case = json_decode($getone["case"], true);


            $selectedTags = $getone['manager_ids']?explode(",", $getone['manager_ids']):[];
            //产品经理
            $where = [
                "role_id" => 2,  //产品经理角色
                "status" => 1
            ];
            $product_manager = Db::name("User")->where($where)->column("id, email, first_name, last_name");
            // 转换为前端需要的格式
            $tagsOptions = [];
            foreach ($product_manager as $tag) {
                $tagsOptions[] = [
                    'value' => $tag['id'],  // 使用id作为value
                    'label' => $tag['first_name']." ".$tag['last_name'],  // 使用name作为label
                ];
            }

            return view("", [
                "service" => $service,
                "getone" => $getone,
                "productService" => json_encode($productService),
                // "productRelation" => json_encode($productRelation),
                "productResource" => json_encode($productResource),
                "product" => $product,
                "resource" => $resource,
                "testimonial" => $testimonial,
                "case" => $case,
                "tagsOptions" => json_encode($tagsOptions),
                "selectedTags" => json_encode($selectedTags),
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Product")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }


    /**
     * 保存产品关联关系（支持多种类型）
     * @param int $productId 主产品ID
     * @param array $relatedIds 关联ID数组
     * @param int $type 关联类型（1产品-服务 2产品-产品 3产品-资源 4服务-资源）
     */
    protected function saveProductRelations($productId, $relatedIds, $type)
    {
        // 验证type是否合法
        if (!in_array($type, [1, 2, 3, 4])) {
            throw new \InvalidArgumentException('Invalid relation type');
        }

        // 去重并移除自身（如果是产品类型才需要排除自身）
        $relatedIds = array_unique($relatedIds);
        if ($type == 2) { // 如果是产品关联，排除自己
            $relatedIds = array_diff($relatedIds, [$productId]);
        }

        // 获取现有关联（限定当前类型）
        $existingRelations = Db::name('Product_relation')
            ->where('product_id', $productId)
            ->where('type', $type)
            ->column('related_id,sort', 'related_id');

        // 需要删除的关联（当前类型下）
        $toDelete = array_diff(array_keys($existingRelations), $relatedIds);
        if ($toDelete) {
            Db::name('Product_relation')
                ->where('product_id', $productId)
                ->where('type', $type)
                ->where('related_id', 'in', $toDelete)
                ->delete();

            // 如果是产品关联，同时删除反向关联
            if ($type == 2) {
                Db::name('Product_relation')
                    ->where('product_id', 'in', $toDelete)
                    ->where('related_id', $productId)
                    ->where('type', $type)
                    ->delete();
            }
        }

        // 处理新增或更新的关联
        $insertData = [];
        $updateData = [];

        foreach ($relatedIds as $index => $relatedId) {
            $sortValue = $index + 1; // 从1开始递增

            // 如果是新增关联
            if (!isset($existingRelations[$relatedId])) {
                $insertData[] = [
                    'product_id' => $productId,
                    'related_id' => $relatedId,
                    'type' => $type,
                    'sort' => $sortValue
                ];

                // 如果是产品关联，添加反向关联
                if ($type == 2) {
                    $insertData[] = [
                        'product_id' => $relatedId,
                        'related_id' => $productId,
                        'type' => $type,
                        'sort' => $sortValue
                    ];
                }
            }
            // 如果是已有关联但排序值变化
            elseif ($existingRelations[$relatedId]['sort'] != $sortValue) {
                $updateData[] = [
                    'product_id' => $productId,
                    'related_id' => $relatedId,
                    'type' => $type,
                    'sort' => $sortValue
                ];

                // 如果是产品关联，更新反向关联
                if ($type == 2) {
                    $updateData[] = [
                        'product_id' => $relatedId,
                        'related_id' => $productId,
                        'type' => $type,
                        'sort' => $sortValue
                    ];
                }
            }
        }

        // 批量新增
        if (!empty($insertData)) {
            Db::name('Product_relation')->insertAll($insertData);
        }

        // 批量更新
        foreach ($updateData as $data) {
            Db::name('Product_relation')
                ->where('product_id', $data['product_id'])
                ->where('related_id', $data['related_id'])
                ->where('type', $type)
                ->update(['sort' => $data['sort']]);
        }
    }


    public function service()
    {
        $List = Db::name('Service')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add_service()
    {
        if ($this->request->isPost()) {
            Db::startTrans();
            try {
                // 获取主产品数据
                $data = input('post.');

                if(!$data['name']){
                    throw new \Exception("必填项未填！");
                }

                if(!trim($data['seo_url'])){
                    $data['seo_url'] = seoFriendlyUrl($data['name']);
                }
                if(Db::name('Service')->where("seo_url", $data['seo_url'])->find()){
                    throw new \Exception("URL已存在！");
                }

                if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));
                if($_FILES['image_background']['name']) $data['image_background'] = $this->upload(request()->file("image_background"));
                if($_FILES['image_background_smt']['name']) $data['image_background_smt'] = $this->upload(request()->file("image_background_smt"));

                $data['create_time'] = date("Y-m-d H:i:s");
                // 保存主产品
                if (!empty($data['id'])) {
                    $service = ServiceModel::find($data['id']);
                    $service->save($data);
                } else {
                    $service = ServiceModel::create($data);
                }

                $relatedIds = isset($data['resources']) ? $data['resources']: [];
                $this->saveProductRelations($service->id, $relatedIds, 4);

                Db::commit();

                return json(['code' => 1, 'msg' => '添加成功！']);
            } catch (\Throwable $e) {
                Db::rollback();

                return json(['code' => 0, 'msg' => "添加失败，请重试！".$e->getMessage()]);
            }

        } else {
            //所有资源
            $resource = Db::name("Resource")->select();

            //产品经理
            $where = [
                "role_id" => 2,  //产品经理角色
                "status" => 1
            ];
            $product_manager = Db::name("User")->where($where)->column("id, email, first_name, last_name");
            // 转换为前端需要的格式
            $tagsOptions = [];
            foreach ($product_manager as $tag) {
                $tagsOptions[] = [
                    'value' => $tag['id'],  // 使用id作为value
                    'label' => $tag['first_name']." ".$tag['last_name'],  // 使用name作为label
                ];
            }

            return view("", [
                "resource" => $resource,
                "tagsOptions" => json_encode($tagsOptions),
            ]);
        }
    }

    public function edit_service()
    {
        if ($this->request->isPost()) {
            Db::startTrans();
            try {
                // 获取主产品数据
                $data = input('post.');

                if(!$data['name']){
                    throw new \Exception("必填项未填！");
                }

                if(!trim($data['seo_url'])){
                    $data['seo_url'] = seoFriendlyUrl($data['name']);
                }
                $where = [
                    ['id', '<>', $data['id']],
                    ['seo_url', '=', $data['seo_url']],
                ];
                if(Db::name('Service')->where($where)->find()){
                    throw new \Exception("URL已存在！");
                }

                if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));
                if($_FILES['image_background']['name']) $data['image_background'] = $this->upload(request()->file("image_background"));
                if($_FILES['image_background_smt']['name']) $data['image_background_smt'] = $this->upload(request()->file("image_background_smt"));

                $data['update_time'] = date("Y-m-d H:i:s");
                // 保存主产品
                if (!empty($data['id'])) {
                    $service = ServiceModel::find($data['id']);
                    $service->save($data);
                } else {
                    $service = ServiceModel::create($data);
                }

                $relatedIds = isset($data['resources']) ? $data['resources']: [];
                $this->saveProductRelations($service->id, $relatedIds, 4);

                Db::commit();

                return json(['code' => 1, 'msg' => '修改成功！']);
            } catch (\Throwable $e) {
                Db::rollback();

                return json(['code' => 0, 'msg' => "修改失败，请重试！".$e->getMessage()]);
            }

        } else {
            $id = input('id');
            $getone = Db::name("Service")->where("id", $id)->find();

            //资源
            $productResource = Db::name("Product_relation")
            ->where(["product_id"=>$id, "type"=>4])
            ->order("sort asc")
            ->select()
            ->each(function ($item) {
                $item['name'] = Db::name("Resource")->where('id', $item['related_id'])->value('name');

                return $item;
            });

            //所有资源
            $resource = Db::name("Resource")->select();

            $selectedTags = $getone['manager_ids']?explode(",", $getone['manager_ids']):[];
            //产品经理
            $where = [
                "role_id" => 2,  //产品经理角色
                "status" => 1
            ];
            $product_manager = Db::name("User")->where($where)->column("id, email, first_name, last_name");
            // 转换为前端需要的格式
            $tagsOptions = [];
            foreach ($product_manager as $tag) {
                $tagsOptions[] = [
                    'value' => $tag['id'],  // 使用id作为value
                    'label' => $tag['first_name']." ".$tag['last_name'],  // 使用name作为label
                ];
            }

            return view("", [
                "getone" => $getone,
                "productResource" => $productResource,
                "resource" => $resource,
                "tagsOptions" => json_encode($tagsOptions),
                "selectedTags" => json_encode($selectedTags),
            ]);
        }
    }

    public function del_service()
    {
        $id = input('id');
        $s = Db::name("Service")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    //状态列表
    public function progress(){
        $id = input('id');
        $type = input('type');

        if($type == 0){
            $main_name = Db::name("Product")->where("id", $id)->value("name");
        } else {
            $main_name = Db::name("Service")->where("id", $id)->value("name");
        }

        $where = [
            "main_id" => $id,
            "type" => $type,
        ];
        $List = Db::name('Progress')
            ->where($where)
            ->order("sort asc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "id" => $id,
            "type" => $type,
            "main_name" => $main_name,
            "List" => $List
        ]);
    }

    public function add_progress()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!['name']){
                $this->error("必填项未填！");
            }

            $s = Db::name("Progress")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            $main_id = input('main_id');
            $type = input('type');

            return view("", [
                "main_id" => $main_id,
                "type" => $type,
            ]);
        }
    }

    public function edit_progress()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!['name']){
                $this->error("必填项未填！");
            }

            $s = Db::name("Progress")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Progress")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_progress()
    {
        $id = input('id');
        $s = Db::name("Progress")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

}
