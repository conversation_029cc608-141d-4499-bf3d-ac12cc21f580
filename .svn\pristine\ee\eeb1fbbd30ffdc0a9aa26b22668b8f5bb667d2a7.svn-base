<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Message - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                {include file="user:left"}

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div
                        class="grid grid-cols-2 text-sm md:text-[1.5625rem] md:px-[2.5rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link">
                        <span class="user-tab-item active">
                            System message
                        </span>
                    </div>
                    <div class="user-profile">
                        <div class="profile-item">
                            <div class="p-4 md:p-[3.125rem] md:pt-[1.875rem]">
                                <div class="message-list">
                                    <ul role="list">
                                        {volist name="user_all_message" id="vo"}
                                        <li>
                                            <div class="message-img relative">
                                                {if $vo.is_read==0}
                                                <span class="absolute w-2 h-2 md:w-3 md:h-3 rounded-full bg-[#ff1919]"></span>
                                                {/if}
                                                <img src="{$vo.sender_avatar ?? '__IMG__/robot.png'}" alt=""
                                                    class="object-cover rounded-full w-full h-full">
                                            </div>
                                            <div class="message-user flex flex-col">
                                                <div class="message-info mb-2">
                                                    <strong class="text-[#999] text-base md:text-xl md:pr-7">
                                                        System
                                                    </strong>
                                                    <span class="text-[#999] text-sm md:text-base">
                                                        {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                                    </span>
                                                </div>
                                                <div class="message-content text-wrap text-sm md:text-xl">
                                                    <p>
                                                        {$vo.content}
                                                    </p>

                                                    {if $vo.link_url}
                                                    <p>
                                                        Click the link to view：<a href="{$vo.link_url}" rel="noopener noreferrer">{$Request.domain}{$vo.link_url}</a>
                                                    </p>
                                                    {/if}
                                                </div>
                                            </div>
                                        </li>
                                        {/volist}
                                    </ul>
                                    <div class="text-sm p-3 md:py-[2.5rem] md:px-[3.5625rem] md:text-xl">
                                        <button type="button"
                                            class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[2.8125rem]">
                                            SHOW MORE ACTIVITY
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        //显示更多
        setupShowMoreActivity(
            '.message-list',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            10                             // 默认显示4个
        );
    </script>
</body>

</html>