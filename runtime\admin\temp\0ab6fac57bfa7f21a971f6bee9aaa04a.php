<?php /*a:3:{s:64:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\product\edit.html";i:1752642607;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\head.html";i:1751613654;s:63:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\common\foot.html";i:1751613673;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.1/themes/base/jquery-ui.css">
    <link rel="stylesheet" href="/static/dist/bootstrap-5.3.0/css/bootstrap.min.css">

    <link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>

<link rel="stylesheet" href="/static/dist/ckeditor5/ckeditor5.css" crossorigin>
<link rel="stylesheet" href="/static/ckeditor5/ckeditor5.css">
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改产品
                <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="tab1">基础信息</button>
                <button class="tab-button" data-tab="tab2">资源</button>
                <button class="tab-button" data-tab="tab3">客户感言</button>
                <button class="tab-button" data-tab="tab4">案例研究</button>
                <button class="tab-button" data-tab="tab5">产品经理</button>
            </div>

            <div class="tab-content">
                <form action="<?php echo url('edit'); ?>" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                    <input type="hidden" name="id" value="<?php echo htmlentities((string) $getone['id']); ?>" />

                    <div id="tab1" class="tab-pane active">
                        <div class="class_con">
                            <label>产品名称：</label>
                            <input type="text" name="name" value="<?php echo htmlentities((string) $getone['name']); ?>" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>SEO链接：</label>
                            <input type="text" name="seo_url" value="<?php echo htmlentities((string) $getone['seo_url']); ?>" />
                        </div>

                        <div class="relation-selector">
                            <div class="selector-group class_con">
                                <label>选择服务</label>
                                <select class="relation-select form-control l_xiang" multiple data-target="#selected-service" data-name="services[]">
                                    <?php if(is_array($service) || $service instanceof \think\Collection || $service instanceof \think\Paginator): $i = 0; $__LIST__ = $service;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <button type="button" class="add-btn btn btn-primary mt-2" data-target="#selected-service">添加</button>
                            </div>
                            <div class="selected-list class_con">
                                <ul id="selected-service" class="list-group selected-items"></ul>
                            </div>
                        </div>

                        <div class="class_con">
                            <label>标题：</label>
                            <input type="text" name="title" value="<?php echo htmlentities((string) $getone['title']); ?>" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con editor-container">
                            <label>产品描述：</label>
                            <textarea name="description" class="tiny-editor" style="height: 300px;"><?php echo htmlentities((string) $getone['description']); ?></textarea>
                        </div>

                        <div class="class_con editor-container">
                            <label>产品详情：</label>
                            <textarea name="content" class="tiny-editor" style="height: 300px;"><?php echo htmlentities((string) $getone['content']); ?></textarea>
                        </div>

                        <div class="class_con">
                            <label>图片：</label>
                            <input type="file" name="image" />
                        </div>
                        <div class="class_con layout_tip_css">
                            （建议图片 宽680px 高680px。格式：png、jpg、jpeg）
                        </div>
                        <?php if($getone['image']): ?>
                        <div class="class_con">
                            <img src="<?php echo htmlentities((string) $getone['image']); ?>">
                        </div>
                        <?php endif; ?>

                        <div class="class_con">
                            <label>详情图片：</label>
                            <input type="file" name="image_detail" />
                        </div>
                        <div class="class_con layout_tip_css">
                            （建议图片 宽638px 高453px。格式：png、jpg、jpeg）
                        </div>
                        <?php if($getone['image_detail']): ?>
                        <div class="class_con">
                            <img src="<?php echo htmlentities((string) $getone['image_detail']); ?>">
                        </div>
                        <?php endif; ?>

                        <div class="class_con">
                            <label>背景图片：</label>
                            <input type="file" name="image_background" />
                        </div>
                        <div class="class_con layout_tip_css">
                            （建议图片 宽1920px 高1000px。格式：png、jpg、jpeg）
                        </div>
                        <?php if($getone['image_background']): ?>
                        <div class="class_con">
                            <img src="<?php echo htmlentities((string) $getone['image_background']); ?>">
                        </div>
                        <?php endif; ?>

                        <div class="class_con">
                            <label>移动端背景图片：</label>
                            <input type="file" name="image_background_smt" />
                        </div>
                        <div class="class_con layout_tip_css">
                            （建议图片 宽750px 高1600px。格式：png、jpg、jpeg）
                        </div>
                        <?php if($getone['image_background_smt']): ?>
                        <div class="class_con">
                            <img src="<?php echo htmlentities((string) $getone['image_background_smt']); ?>">
                        </div>
                        <?php endif; ?>

                        <div class="class_con">
                            <label>产品价格：</label>
                            <input type="text" name="price" value="<?php echo htmlentities((string) $getone['price']); ?>" />
                        </div>

                        <div class="class_con">
                            <label>SEO页面标题：</label>
                            <textarea name="seo_title"><?php echo htmlentities((string) $getone['seo_title']); ?></textarea>
                        </div>
                        <div class="class_con">
                            <label>SEO页面描述：</label>
                            <textarea name="seo_description"><?php echo htmlentities((string) $getone['seo_description']); ?></textarea>
                        </div>
                        <div class="class_con">
                            <label>SEO页面关键词：</label>
                            <textarea name="seo_keywords"><?php echo htmlentities((string) $getone['seo_keywords']); ?></textarea>
                        </div>

                        <div class="class_con">
                            <label>推荐：</label>
                            <input type="checkbox" name="is_recommend" <?php if($getone['is_recommend']==1): ?>checked<?php endif; ?> />
                        </div>
                    </div>

                    <div id="tab2" class="tab-pane">
                        <div class="relation-selector">
                            <div class="selector-group class_con">
                                <label>选择资源</label>
                                <select class="relation-select form-control l_xiang" multiple data-target="#selected-resource" data-name="resources[]">
                                    <?php if(is_array($resource) || $resource instanceof \think\Collection || $resource instanceof \think\Paginator): $i = 0; $__LIST__ = $resource;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                        <option value="<?php echo htmlentities((string) $vo['id']); ?>"><?php echo htmlentities((string) $vo['name']); ?></option>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </select>
                                <button type="button" class="add-btn btn btn-primary mt-2" data-target="#selected-resource">添加</button>
                            </div>
                            <div class="selected-list class_con">
                                <ul id="selected-resource" class="list-group selected-items"></ul>
                            </div>
                        </div>
                    </div>

                    <div id="tab3" class="tab-pane">
                        <div class="class_con">
                            <label>客户感言：
                                <button type="button" class="add-item" data-container="testimonial-list" data-file-name="testimonial_file" title="点击添加">+</button>
                            </label>
                            <div id="testimonial-list">
                                <?php if(is_array($testimonial) || $testimonial instanceof \think\Collection || $testimonial instanceof \think\Paginator): $i = 0; $__LIST__ = $testimonial;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$ad): $mod = ($i % 2 );++$i;?>
                                <div class="list-item">
                                    <input type="hidden" name="testimonial_file_path[]" value="<?php echo htmlentities((string) $ad[0]); ?>">
                                    <input type="file" name="testimonial_file[]">
                                    <img src="<?php echo htmlentities((string) $ad[0]); ?>">
                                    <button type="button">删除</button></div>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </div>
                        </div>
                    </div>

                    <div id="tab4" class="tab-pane">
                        <div class="class_con">
                            <label>案例研究：
                                <button type="button" class="add-item" data-container="case-list" data-file-name="case_file" title="点击添加">+</button>
                            </label>
                            <div id="case-list">
                                <?php if(is_array($case) || $case instanceof \think\Collection || $case instanceof \think\Paginator): $i = 0; $__LIST__ = $case;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$ad): $mod = ($i % 2 );++$i;?>
                                <div class="list-item">
                                    <input type="hidden" name="case_file_path[]" value="<?php echo htmlentities((string) $ad[0]); ?>">
                                    <input type="file" name="case_file[]">
                                    <img src="<?php echo htmlentities((string) $ad[0]); ?>">
                                    <button type="button">删除</button></div>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </div>
                        </div>
                    </div>

                    <div id="tab5" class="tab-pane">
                        <div class="class_con">
                            <label>产品经理：</label>
                            <input type="hidden" name="manager_ids" id="tags-input" value="">
                            <div id="multiselect"></div>
                        </div>
                    </div>

                    <div class="de_y">
                        <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                        <a href="<?php echo url('index'); ?>" class="de_y_r" >返回</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <span class="by">Powered by <a target="_blank" href="http://www.ggseo.cn/ezweb/"><?php echo htmlentities((string) config('app.WebConfig_VersionName')); ?></a></span>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>
<script src="/static/layer/laydate/laydate.js"></script>

<script src="/static/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
<script src="/static/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
<script src="/static/ckeditor5/ckeditor5.js"></script>

<script>
    var customEditor = new CKEditorManager();
    customEditor.initAll('.tiny-editor');
</script>

<script>
    // 获取表单和提交按钮
    const $form = $('#formId');
    const $submitBtn = $('#submitBtn');
    // 初始绑定提交事件
    if ($form.length) {
        $form.on('submit', handleSubmit);
    }
    // 提交处理函数
    async function handleSubmit(event) {
        event.preventDefault(); // 阻止默认表单提交

        // 确保所有编辑器内容已同步到 textarea
        customEditor.editors.forEach((editor, index) => {
            editor.updateSourceElement();
        });

        // 解绑提交事件（避免重复提交）
        $form.off('submit', handleSubmit);
        // 禁用提交按钮（防止重复点击）
        $submitBtn.prop('disabled', true);
        try {
            const formData = new FormData($form[0]);
            const response = await fetch($form.attr('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                },
            });
            const data = await response.json();
            if (data.code === 1) {
                // 提交成功
                layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                    location.reload();
                });
            } else {
                // 提交失败
                layer.msg(data.msg, { icon: 2 });
            }
        } catch (error) {
            console.error('Error:', error);
            layer.msg("提交失败，请重试", { icon: 2 });
        } finally {
            // 无论成功或失败，重新绑定事件并恢复按钮状态
            $submitBtn.prop('disabled', false);
            $form.on('submit', handleSubmit);
        }
    }
</script>

<script>
    //弹窗
    $(document).on('click', '.layer-trigger', function() {
        const $btn = $(this);
        const contentId = $btn.data('layer');
        const title = $btn.data('title');
        const width = $btn.data('width');
        const height = $btn.data('height');

        layer.open({
            type: 1,
            title: [title, 'font-size:18px; background-color: #0f2950; color: #fff;'],
            closeBtn: 1,
            shadeClose: true,
            area: [width, height],
            content: $("#" + contentId),
            resize: false,
            move: false
        });
    });

    //弹窗"提交"和"取消"事件
    // 表单初始化函数
    function initLayerForms() {
        $('.layer-form').each(function() {
            const $form = $(this);
            const $submitBtn = $form.find('.layer-submit-btn');
            const $closeBtn = $form.find('.layer-close-btn');

            // 提交事件
            $submitBtn.on('click', async function(e) {
                e.preventDefault();
                const $btn = $(this);
                $btn.prop('disabled', true).text('提交中...');
                try {
                    const response = await $.ajax({
                        url: $form.attr('action'),
                        method: 'POST',
                        data: new FormData($form[0]),
                        processData: false,
                        contentType: false,
                        dataType: 'json'
                    });

                    if(response.code === 1) {
                        layer.msg(response.msg, {icon: 1, time: 1500}, function() {
                            location.reload();

                            // 优先使用保存的索引
                            // if(window.layerIndex !== undefined) {
                            //     layer.close(window.layerIndex);
                            // }
                            // // 其次尝试获取当前索引
                            // else if(layer.getFrameIndex && layer.getFrameIndex(window.name)) {
                            //     layer.close(layer.getFrameIndex(window.name));
                            // }
                            // // 最后全部关闭
                            // else {
                            //     layer.closeAll();
                            // }

                            // // 如果需要刷新
                            // if(response.reload) {
                            //     location.reload();
                            // }
                        });
                    } else {
                        layer.msg(response.msg, {icon: 2});
                    }
                } catch(error) {
                    layer.msg('提交失败', {icon: 2});
                } finally {
                    $btn.prop('disabled', false).text('提交');
                }
            });

            // 取消按钮
            $closeBtn.on('click', function() {
                if(window.layerIndex !== undefined) {
                    layer.close(window.layerIndex);
                } else {
                    layer.closeAll();
                }
            });
        });
    }

    // 页面加载后初始化
    $(function() {
        initLayerForms();
    });
</script>

<script>
    //无限添加按钮
    $(document).ready(function () {
        // 为已有的删除按钮绑定点击事件
        $('.class_con .list-item button').on('click', function () {
            $(this).closest('.list-item').remove(); // 删除当前列表项
        });

        // 为所有 .add-item 按钮绑定点击事件
        $('.add-item').on('click', function () {
            const containerId = $(this).data('container'); // 获取目标容器的 ID
            const titleName = $(this).data('title-name'); // 获取标题字段名称
            const urlName = $(this).data('url-name');
            const contentName = $(this).data('content-name'); // 获取内容字段名称
            const fileName = $(this).data('file-name'); // 获取文件字段名称（可选）
            const hiddenFields = $(this).data('hidden-fields');  // 隐藏字段，可以是字符串或数组

            addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields); // 调用封装好的函数
        });

        // 封装添加列表项的函数
        function addListItem(containerId, titleName, urlName, contentName, fileName, hiddenFields="") {
            const listItem = $('<div>').addClass('list-item');

            // 将隐藏字段转换为数组（支持逗号分隔的字符串或数组）
            const hiddenFieldsArray = typeof hiddenFields === 'string'
                ? hiddenFields.split(',')
                : (Array.isArray(hiddenFields) ? hiddenFields : []);

            // 标题输入框
            if (titleName) {
                const titleInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入标题',
                    name: titleName+"[]"
                });
                if (hiddenFieldsArray.includes(titleName)) {
                    titleInput.hide(); // 使用jQuery的hide()方法设置display:none
                }
                listItem.append(titleInput);
            }

            if (urlName) {
                const urlInput = $('<input>').attr({
                    type: 'text',
                    placeholder: '请输入网址',
                    name: urlName+"[]"
                });
                if (hiddenFieldsArray.includes(urlName)) {
                    urlInput.hide();
                }
                listItem.append(urlInput);
            }

            // 内容输入框
            if(contentName){
                const contentTextarea = $('<textarea>').attr({
                    placeholder: '请输入内容',
                    name: contentName+"[]"
                });
                if (hiddenFieldsArray.includes(contentName)) {
                    contentTextarea.hide();
                }
                listItem.append(contentTextarea);
            }

            // 如果传入了 fileName，则添加文件上传字段
            if (fileName) {
                const filePathInput = $('<input>').attr({
                    type: 'hidden',
                    name: fileName+"_path[]"
                });
                listItem.append(filePathInput);

                const fileInput = $('<input>').attr({
                    type: 'file',
                    name: fileName+"[]"
                });
                if (hiddenFieldsArray.includes(fileName)) {
                    fileInput.hide();
                }
                listItem.append(fileInput);
            }

            // 删除按钮
            const deleteButton = $('<button>').text('删除').attr('type', 'button');
            deleteButton.on('click', function () {
                listItem.remove();
            });

            // 将标题、内容和删除按钮添加到列表项中
            listItem.append(deleteButton);

            // 将列表项添加到目标容器中
            $(`#${containerId}`).append(listItem);
        }
    });
</script>

<script>
    //tab标签列表
    $('.tab-button').on('click', function () {
        // 切换 Tab 按钮状态
        $('.tab-button').removeClass('active');
        $(this).addClass('active');

        // 切换 Tab 内容
        const targetTab = $(this).data('tab');
        $('.tab-pane').removeClass('active');
        $('#' + targetTab).addClass('active');
    });
</script>

<script>
    //列表选择，全选，多选
    $(document).ready(function() {
        // 遍历每一个多选框容器
        $(".multi-select-container").each(function() {
            const $container = $(this);
            const $select = $container.find(".user-multi-select");
            const $selectAllBtn = $container.find(".select-all-btn");
            const $deselectAllBtn = $container.find(".deselect-all-btn");
            const $countDisplay = $container.find(".count");

            // 初始化计数
            updateSelectedCount();

            // 全选
            $selectAllBtn.on("click", function() {
                $select.find("option").prop("selected", true);
                updateSelectedCount();
            });

            // 取消全选
            $deselectAllBtn.on("click", function() {
                $select.find("option").prop("selected", false);
                updateSelectedCount();
            });

            // 点击选项（无需按Ctrl）
            $select.on("mousedown", "option", function(e) {
                e.preventDefault();
                $(this).prop("selected", !$(this).prop("selected"));
                $select.trigger("change"); // 触发change事件更新计数
            });

            // 选择变化时更新计数
            $select.on("change", updateSelectedCount);

            // 更新已选项数量
            function updateSelectedCount() {
                const selectedCount = $select.find("option:selected").length;
                $countDisplay.text(selectedCount);
            }
        });
    });
</script>

<script>
    //开关按钮控制
    document.querySelectorAll('.toggle-switch').forEach(switchEl => {
        switchEl.addEventListener('change', function() {
            // 获取对应的 hidden 输入字段
            const targetName = this.getAttribute('data-target');
            const hiddenInput = document.querySelector(`input[name="${targetName}"]`);

            // 更新值
            hiddenInput.value = this.checked ? '1' : '0';
        });
    });
</script>

    <script src="https://code.jquery.com/ui/1.13.1/jquery-ui.min.js"></script>
    <script src="/static/dist/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></script>

    <script src="/static/home/<USER>/MultiSelect.js"></script>

    <script>
        const options = JSON.parse('<?php echo $tagsOptions; ?>');
        const selectedTags = JSON.parse('<?php echo $selectedTags; ?>').map(Number);
        const multi = new MultiSelect({
            container: '#multiselect',
            options,
            max: 8, // 最多能选n个
            defaultSelected: selectedTags,
        });

        // 覆盖原方法，直接更新 input
        multi.renderTags = function() {
            MultiSelect.prototype.renderTags.call(this); // 调用父类方法
            MultiSelect.prototype.renderDropdown.call(this); // 调用父类方法
            document.getElementById('tags-input').value = this.selected.join(',');
        };
    </script>

    <script>
        // 通用的初始化已选项函数
        function initSelectedItems(items, listId, namePrefix) {
            if (!items || !Array.isArray(items)) return;

            const $list = $(`#${listId}`);
            $list.empty(); // 清空现有项

            items.forEach(function(item) {
                const id = item.id || item.service_id || item.related_id; // 兼容不同属性名
                const name = item.name;

                const newItem = $(`
                    <li class="list-group-item" data-id="${id}">
                        <span class="handle" style="cursor: move;">☰</span> ${name}
                        <button type="button" class="btn btn-sm btn-danger float-right remove-item">删除</button>
                        <input type="hidden" name="${namePrefix}[]" value="${id}">
                    </li>
                `);

                $list.append(newItem);
            });
        }

        $(document).ready(function() {
            // 初始化所有可排序列表
            $(".selected-items").sortable({
                handle: ".handle",
                update: function() {
                    // 排序更新时的逻辑（如果需要可以添加）
                }
            });

            // 添加项目处理
            $(".add-btn").click(function() {
                const targetList = $(this).data("target");
                const selectElement = $(this).siblings(".relation-select");
                const inputName = selectElement.data("name");

                selectElement.find("option:selected").each(function() {
                    const id = $(this).val();
                    const name = $(this).text();
                    const itemExists = $(`${targetList} li[data-id="${id}"]`).length > 0;

                    if (!itemExists) {
                        const newItem = $(`
                            <li class="list-group-item" data-id="${id}">
                                <span class="handle" style="cursor: move;">☰</span> ${name}
                                <button type="button" class="btn btn-sm btn-danger float-right remove-item">删除</button>
                                <input type="hidden" name="${inputName}" value="${id}">
                            </li>
                        `);
                        $(targetList).append(newItem);
                    }
                });
            });

            // 删除项目事件委托
            $(document).on("click", ".remove-item", function() {
                $(this).closest("li").remove();
            });

            // 初始化已选项
            initSelectedItems(JSON.parse('<?php echo $productService; ?>'), 'selected-service', 'services');
            initSelectedItems(JSON.parse('<?php echo $productResource; ?>'), 'selected-resource', 'resources');
        });
    </script>
</body>
</html>