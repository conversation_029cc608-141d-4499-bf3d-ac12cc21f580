<?php /*a:4:{s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\faq\results.html";i:1747978106;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1747886470;s:64:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1747904616;s:62:"D:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1747971236;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>FAQ - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(/static/home/<USER>/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%_100%] md:bg-[url(/static/home/<USER>/backgrounds/pc_bj.jpg)] md:bg-size-[100%_100%] md:pb-20">

        <div id="header" class="relative">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[50px] md:h-24 top-0" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-10 md:h-full">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[135px]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-50px)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white
                md:flex md:gap-x-16 md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/" class="text-[#000] text-base flex-1 leading-[50px] px-5 border-b border-[#e0eaff]
                        md:border-0
                        md:leading-none
                        md:flex
                        md:items-center
                        md:text-xl
                        ">Home</a>
                    </li>
                    <li class="relative group cursor-pointer md:h-full flex flex-col navigation">
                        <a
                            class="text-[#000] text-base cursor-pointer flex-1 h-full leading-[50px] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0 md:text-xl">
                            <span>DELHunter</span>
                            <i
                                class="w-[16px] h-[9px] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all"></i>
                        </a>
                        <ul class="secondary hidden static group-hover:block bg-[#f8fdff] z-50
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[312px]">
                            <li class="grid grid-cols-1">
                                <a href=""
                                    class="text-[#000] flex items-center text-base Roboto_Bold md:py-5 px-8 h-[50px] border-b border-[#e0eaff] md:h-auto md:text-xl">
                                    Latest developments
                                </a>
                            </li>
                            <li class="grid grid-cols-1">
                                <a href=""
                                    class="text-[#000] flex items-center text-base Roboto_Bold md:py-5 px-8 h-[50px] border-b border-[#e0eaff] md:h-auto md:text-xl">
                                    Major news
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/product" class="text-[#000] text-base border-b border-[#e0eaff] leading-[50px] px-5
                        md:border-0
                        md:leading-none
                        md:flex
                        md:items-center
                        md:text-xl
                        ">Services</a>
                    </li>
                    <li class="relative group cursor-pointer md:h-full flex flex-col navigation">
                        <a
                            class="text-[#000] text-base cursor-pointer flex-1 h-full leading-[50px] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0 md:text-xl">
                            <span>DELHunter</span>
                            <i
                                class="w-[16px] h-[9px] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all"></i>
                        </a>
                        <ul class="
                        secondary static group-hover:block hidden bg-[#f8fdff] z-50
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[312px] ">
                            <li class="grid grid-cols-1 items-center">
                                <a href=""
                                    class="text-[#000] flex items-center text-base Roboto_Bold md:py-5 px-8 h-[50px] border-b border-[#e0eaff] md:h-auto md:text-xl">
                                    Color page
                                </a>
                            </li>
                            <li class="grid grid-cols-1">
                                <a href=""
                                    class="text-[#000] flex items-center text-base Roboto_Bold md:py-5 px-8 h-[50px] border-b border-[#e0eaff] md:h-auto md:text-xl">
                                    Open data platform
                                </a>
                            </li>
                            <li class="grid grid-cols-1">
                                <a href=""
                                    class="text-[#000] flex items-center text-base Roboto_Bold md:py-5 px-8 h-[50px] border-b border-[#e0eaff] md:h-auto md:text-xl">
                                    Operation video
                                </a>
                            </li>
                            <li class="grid grid-cols-1">
                                <a href=""
                                    class="text-[#000] flex items-center text-base Roboto_Bold md:py-5 px-8 h-[50px] border-b border-[#e0eaff] md:h-auto md:text-xl">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/" class="text-[#000] text-base border-b border-[#e0eaff] leading-[50px] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center md:text-xl">Services</a>
                    </li>
                </ul>
            </div>
            <div class="flex items-center gap-3.5 md:gap-8">
                <div class="md:flex md:items-center">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[16px] rounded-full bg-no-repeat bg-center md:bg-auto cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="" method="post" class="hidden absolute w-full left-0 top-full md:relative z-10"
                        id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[300px] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[38px] md:h-[38px] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[16px] md:bg-auto z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="w-8 h-8 rounded-full">
                    <a href="">
                        <img src="/static/home/<USER>/user-1.jpg" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                        <a href="/logout">Logout</a>
                    </a>
                </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[13px] bg-[12px_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-auto md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[26px] cursor-pointer"
                        id="menu_btn"></button>
                </div>
            </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-7 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        FAQ
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="flex flex-col gap-5" data-aos="fade-up">
                <div class="">
                    <h1 class="text-xl Roboto_Bold mb-2 md:text-4xl md:mb-5">
                        FAQ
                    </h1>
                    <div class="h-[12.5rem] bg-[url(/static/home/<USER>/faq/bg_m.jpg)] bg-no-repeat bg-cover rounded-tl-xl rounded-tr-xl shadow-lg
                        md:h-[31.25rem] md:bg-[url(/static/home/<USER>/faq/bg_pc.jpg)">
                        <div class="h-full w-10/12 mx-auto flex flex-col items-center justify-center">
                            <div class="text-white text-2xl mb-5
                            md:text-5xl md:mb-10
                            ">
                                Find the answers you want anytime, anywhere.
                            </div>
                            <form action="/faq/result" method="get" class="relative w-full">
                                <div class="relative w-full">
                                    <input type="text" name="q" value="<?php echo htmlentities((string) $keyword); ?>" placeholder="Search frequently asked questions" class="bg-white rounded-lg h-[3.125rem] w-full py-2 pl-3 pr-10
                                        md:h-[5.625rem] md:text-xl md:pl-[2.5rem] md:pr-[6.25rem]" />
                                    <button type="submit" class="absolute right-[.425rem] top-1/2 -translate-y-1/2 w-8 h-8 bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-no-repeat bg-center bg-size-[1rem] md:bg-auto cursor-pointer
                                        md:w-10 md:h-10 md:right-10" aria-label="搜索"> </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="w-full">
        <div class="w-11/12 mx-auto py-6
        md:w-10/12 md:py-20
        " data-aos="fade-up">
            <div class="mb-8
            md:mb-10
            ">
                <?php if($keyword): ?>
                <div class="result pb-3 border-b border-[#e6eeff]">
                    <h2 class="text-xl">search results：<?php echo htmlentities((string) count($results)); ?></h2>
                </div>

                <?php if(empty($results)): ?>
                <p>No results found.</p>
                <?php else: ?>
                <div class="mt-4">
                    <ul class="flex flex-col gap-y-2 mb-2 faq-list md:gap-y-5 md:mb-5">
                        <?php if(is_array($results) || $results instanceof \think\Collection || $results instanceof \think\Paginator): $i = 0; $__LIST__ = $results;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <li class="underline underline-offset-4 md:underline-offset-8">
                            <a href="/faq/<?php echo htmlentities((string) $vo['seo_url']); ?>" class="text-[#155797]">
                                <span><?php echo htmlentities((string) $vo['question']); ?></span>
                                <img src="/static/home/<USER>/icons/changjiantou-r.png" class="w-5 inline-block ml-2.5 md:w-auto md:ml-3.5" alt="" />
                            </a>
                        </li>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </ul>
                </div>
                <?php endif; else: ?>
                <h1 class="text-2xl font-bold mb-6">Search FAQ</h1>
                <p>Please enter a search term.</p>
                <?php endif; ?>
            </div>

            <div class="bg-[#f8fdff] rounded-2xl border border-[#e6eeff] p-[1.875rem]">
                <div class="text-center text-sm mb-5">
                    For any unanswered questions, please submit your inquiry here, and we’ll assist you promptly
                </div>
                <div class="cursor-pointer text-[#111] border border-[#155797] w-[13rem] rounded-xl h-[3.125rem] bg-white flex
                                                            justify-center items-center gap-x-3 md:h-[6.25rem] text-sm mx-auto
                                                            md:text-2xl " id="ask_here">
                    <img src="/static/home/<USER>/faq/add.png" class="w-4 md:w-auto md:mr-3.5" alt="" />
                    <span>Need help? Ask here</span>
                </div>
            </div>
        </div>

        <section class="flex items-center justify-center py-6 md:py-10">
            <figure>
                <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[150px] md:w-auto" />
                <figcaption class="sr-only">logo</figcaption>
            </figure>
        </section>
    </main>

    <footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="">
            Open DEL
        </a>
        <a href="">
            about
        </a>
        <a href="">
            contact Us
        </a>
        <a href="">
            terms of Service
        </a>
        <a href="">
            privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] py-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-28 md:h-28">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="" _target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[120%] bottom-0 min-w-3xs rounded-xl rounded-br-none p-4 text-sm md:min-w-[415px] md:p-7 md:text-xl">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-20 md:h-20 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-auto" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <!-- 添加咨询弹窗 -->
    <div id="ask_modal" class="modal-container fixed top-0 left-0 w-full h-full bg-[rgba(21,87,151,.5)] z-50 hidden">
        <form id="questionForm">
            <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10 px-5
            md:min-w-[43.75rem] md:min-h-[28.125rem]
            md:px-10
            ">
                    <div class="mb-6">
                        <h3 class="text-2xl Roboto_bold md:text-3xl">
                            Submit new questions
                        </h3>
                    </div>
                    <div class="mb-7 flex flex-col gap-y-4 w-full">
                        <textarea name="question" id="ask_textarea" placeholder="Please leave a message..." class="w-full h-[7.75rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/wenti.png)] bg-no-repeat bg-[.75rem_.75rem] bg-size-[1.25rem] py-[.75rem] pl-10 pr-6
                        md:pl-16 md:bg-auto md:bg-[.75rem_.75rem]
                        " autocomplete="off"></textarea>
                        <p class="text-red-500 hidden error">
                            Information cannot be empty
                        </p>
                    </div>
                    <button type="submit" class="bg-[#f08411] text-[#fff] text-lg py-3 w-full rounded-md cursor-pointer"
                        id="ask_submit">
                        Submit
                    </button>
                <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 cursor-pointer bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-cover"  data-close-modal></div>
            </section>
        </form>
    </div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup() {
        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>


    <script>
        //添加问题
        var $askHere = $('#ask_here');
        var $askModal = $('#ask_modal');
        if ($askHere.length && $askModal.length) {
            $askHere.on('click', function() {
                $askModal.removeClass('hidden').addClass('block');
            });
        }

        // 提交问题
        $('#questionForm').on('submit', function(e) {
            //阻止表单提交
            e.preventDefault();

            var $textarea = $('#ask_textarea');
            var $error = $textarea.parent().find('.error');

            if ($.trim($textarea.val()) === '') {
                if ($error.length) $error.show();
                return false
            } else {
                if ($error.length) $error.hide();
            }

            var $submitBtn = $('#ask_submit');
            $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

            // 发送AJAX请求
            $.ajax({
                url: '/ask-question',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(data) {
                    if (data.code === 1) {
                        //提交成功
                        layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                            $('#questionForm')[0].reset(); // 重置表单
                            $askModal.removeClass('block').addClass('hidden');
                        });
                    } else {
                        //提交失败
                        layer.msg('Error: ' + data.msg, { icon: 2 });
                    }
                },
                error: function(xhr, status, error) {
                    layer.msg('An error occurred: ' + error, { icon: 2 });
                },
                complete: function() {
                    // 无论成功失败，都重新启用按钮
                    $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                }
            });
        });
    </script>

</body>

</html>