<?php
// +----------------------------------------------------------------------
// | 全局Session配置（默认配置）
// | 注意：各应用可以通过app/{应用名}/config/session.php覆盖此配置
// +----------------------------------------------------------------------

return [
    // session name
    'name'           => 'PHPSESSID',
    // SESSION_ID的提交变量,解决flash上传跨域
    'var_session_id' => '',
    // 驱动方式 支持file cache
    'type'           => 'file',
    // 存储连接标识 当type使用cache的时候有效
    'store'          => null,
    // 过期时间
    'expire'         => 4*3600,  // 4小时，绝对过期时间，固定生命周期，不会因用户有点击活动而续期
    // 前缀
    'prefix'         => '',
];
