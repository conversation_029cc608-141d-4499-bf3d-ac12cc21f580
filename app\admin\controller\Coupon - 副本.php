<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Coupon extends Common
{
    public function index()
    {
        $List = Db::name('Coupon')
            ->where("del_status", 0)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                if($item['type'] == 0){
                    //产品
                    $item['main_name'] = Db::name("Product")->where("id", $item['main_id'])->value('name');
                } else {
                    //服务
                    $item['main_name'] = Db::name("Service")->where("id", $item['main_id'])->value('name');
                }

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name'] || !$data['description'] || !$data['start_time'] || !$data['end_time']){
                $this->error("必填项未填！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Coupon")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            $type = input('type');

            if($type == 0){
                //产品列表
                $list = Db::name("Product")->column("id, name");
            } else {
                //服务列表
                $list = Db::name("Service")->column("id, name");
            }

            return view("", [
                "type" => $type,
                "list" => $list,
            ]);
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name'] || !$data['description'] || !$data['start_time'] || !$data['end_time']){
                $this->error("必填项未填！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Coupon")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Coupon")->where("id", $id)->find();

            if($getone['type'] == 0){
                //产品列表
                $list = Db::name("Product")->column("id, name");
            } else {
                //服务列表
                $list = Db::name("Service")->column("id, name");
            }

            return view("", [
                "getone" => $getone,
                "list" => $list,
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Coupon")->where("id", $id)->save(["del_status"=>1]);
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }


    //赠送试用券
    public function add_gift(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(empty($data['user_ids'])){
                $this->error("必填项未填！");
            }

            foreach($data['user_ids'] as $val){
                $where = [
                    "coupon_id" => $data['id'],
                    "user_id" => $val,
                ];
                if(empty(Db::name("User_coupon")->where($where)->find())){
                    $coupon_data = [
                        "coupon_id" => $data['id'],
                        "user_id" => $val,
                        "create_time" => date("Y-m-d H:i:s"),
                    ];
                    Db::name("User_coupon")->insert($coupon_data);
                }
            }

            $this->success("赠送成功！");
        } else {
            $id = input('id');
            $getone = Db::name("Coupon")->where("id", $id)->find();

            if($getone['type'] == 0){
                //产品
                $getone['main_name'] = Db::name("Product")->value("name");
            } else {
                //服务
                $getone['main_name'] = Db::name("Service")->value("name");
            }

            //未获得优惠券的用户
            $users = Db::name('User')->alias("u")
                ->leftJoin('User_coupon uc', 'u.id = uc.user_id and uc.coupon_id = '.$id)
                ->whereNull('uc.id')
                ->field('u.*')
                ->select();

            return view("", [
                "getone" => $getone,
                "users" => $users,
            ]);
        }
    }

    //赠送列表
    public function gift(){
        $List = Db::name('User_coupon')->alias("uc")
            ->field("c.name, c.description, c.start_time, c.end_time, c.main_id, c.type, u.email, uc.create_time")
            ->where("c.del_status", 0)
            ->leftJoin('Coupon c', 'c.id = uc.coupon_id')
            ->leftJoin('User u', 'u.id = uc.user_id')
            ->order("uc.id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                if($item['type'] == 0){
                    //产品
                    $item['main_name'] = Db::name("Product")->where("id", $item['main_id'])->value('name');
                } else {
                    //服务
                    $item['main_name'] = Db::name("Service")->where("id", $item['main_id'])->value('name');
                }

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

}
