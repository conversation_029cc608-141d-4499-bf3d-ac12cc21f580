class MultiSelect {
    constructor({container, options, max = Infinity,defaultSelected = []}) {
      this.container = typeof container === 'string' ? document.querySelector(container) : container;
      this.options = options;
      this.max = max;
      this.selected = [...defaultSelected]; // 这里赋值默认已选
      this._init();
    }
  
    _init() {
      // 结构
      this.container.innerHTML = `
        <div class="multi-select-display w-full min-h-[3.125rem] border border-[#bdcbe9] rounded-md bg-transparent px-5 py-2.5 pr-12 appearance-none text-[#ddd] cursor-pointer relative md:min-h-[5rem] gap-2">
          <input class="multi-select-input placeholder:text-[#999]"  placeholder="Add tags" readonly />

          <button type="button" class="rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full z-0 flex items-center justify-center">
            <svg t="1751443158415" class="w-4 md:w-6" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1472"><path d="M483.072 714.496l30.165333 30.208 415.957334-415.829333a42.837333 42.837333 0 0 0 0-60.288 42.538667 42.538667 0 0 0-60.330667-0.042667l-355.541333 355.413333-355.242667-355.413333a42.496 42.496 0 0 0-60.288 0 42.837333 42.837333 0 0 0-0.085333 60.330667l383.701333 383.872 1.706667 1.749333z" fill="#999999" p-id="1473"></path></svg>
        </button>
        </div>
        <div class="multi-select-dropdown" style="display:none;"></div>
      `;
      this.display = this.container.querySelector('.multi-select-display');
      this.input = this.container.querySelector('.multi-select-input');
    //   this.arrow = this.container.querySelector('.multi-select-arrow');
      this.dropdown = this.container.querySelector('.multi-select-dropdown');
  
      // 事件
      this.display.onclick = (e) => {
        if (e.target.classList.contains('close')) {
          const val = e.target.getAttribute('data-value');
          this.selected = this.selected.filter(v => v !== val);
          this.renderTags();
          this.renderDropdown();
          e.stopPropagation();
          return;
        }
        e.stopPropagation();
        this.toggleDropdown(this.dropdown.style.display !== 'block');
        this.renderDropdown();
      };
    
      document.addEventListener('click', (e) => {
        if (!this.container.contains(e.target)) {
          this.toggleDropdown(false);
        }
      });
    
      this.renderTags();
      this.renderDropdown();
      this.toggleDropdown(false);
    }
  
    renderTags() {
      // 清空已有tag
      this.display.querySelectorAll('.multi-select-tag').forEach(tag => tag.remove());
      // 插入tag
      this.selected.forEach(val => {
        const tag = document.createElement('span');
        tag.className = 'multi-select-tag';
        tag.innerHTML = this.options.find(o => o.value === val).label +
          `<span class="close" data-value="${val}">&times;</span>`;
        this.display.insertBefore(tag, this.input);
      });
      if (this.selected.length > 0) {
        this.input.style.display = 'none';
      } else {
        this.input.style.display = '';
      }
    }
  
    renderDropdown() {
      this.dropdown.innerHTML = '';
      this.options.forEach(opt => {
        const div = document.createElement('div');
        div.className = 'multi-select-option' + (this.selected.includes(opt.value) ? ' selected' : '');
        div.textContent = opt.label;
        div.onclick = (e) => {
          e.stopPropagation();
          if (this.selected.includes(opt.value)) {
            this.selected = this.selected.filter(v => v !== opt.value);
          } else {
            if (this.selected.length >= this.max) {
              alert(`最多只能选择${this.max}项`);
              return;
            }
            this.selected.push(opt.value);
          }
          this.renderTags();
          this.renderDropdown();
        };
        this.dropdown.appendChild(div);
      });
    }
  
    toggleDropdown(show) {
      this.dropdown.style.display = show ? 'block' : 'none';
    //   this.arrow.innerHTML = show ? '&#9650;' : '&#9660;';
    }
  
    // 可选：获取已选值
    getValue() {
      return this.selected;
    }
  }