<?php
declare (strict_types = 1);

namespace app\validate;

use think\Validate;

class Quote extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'email'  => 'require|email',
        'captcha' => 'require',
        'country' => 'require',
        'first_name' => 'require|min:2|max:20',
        'last_name' => 'require|min:2|max:10',
        'organization' => 'require',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'email.require' => 'Please fill in the email information',
        'email.email'     => 'Please enter a valid email address',
        'captcha.require' => 'Please enter email verification code',
        'country.require'   => 'Country must',
        'first_name.require'   => 'First Name must',
        'first_name.min'   => 'First Name at least 2 characters',
        'first_name.max'   => 'First Name no more than 20 characters',
        'last_name.require'   => 'Last Name must',
        'last_name.min'   => 'Last Name at least 2 characters',
        'last_name.max'   => 'Last Name no more than 10 characters',
        'organization.require'   => 'Organization must',
    ];

    protected $scene = [
        'front-quote'  =>  ['email', 'captcha', 'password', 'password_confirm', 'country', 'first_name', 'last_name', 'organization'],
    ];

}
