<?php /*a:1:{s:64:"D:\phpstudy_pro\WWW\opendelclub\app\admin\view\publics\menu.html";i:1752636010;}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
	<title>Document</title>

    <script>
        // 从父窗口获取 CSS，并插入到当前子框架
        if (window.parent && window.parent.getParentCSS) {
            document.write(window.parent.getParentCSS());
        }
    </script>
</head>

<body style="background:#191f26 none repeat scroll;">
    <div class="menus">
     	<div class="menus_l" id="notemenu">
            <ul>
                <?php if(is_array($menus) || $menus instanceof \think\Collection || $menus instanceof \think\Paginator): $i = 0; $__LIST__ = $menus;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;if(isset($role_menus[$vo['id']]) && $role_menus[$vo['id']]!==0): ?>
                <li class="cur">
                    <div class="list_ca list_back <?php echo htmlentities((string) $vo['icon']); ?>"><i></i><a href="javascript:void(0)"><?php echo htmlentities((string) $vo['title']); ?></a></div>
                </li>

                    <?php if(is_array($vo['son']) || $vo['son'] instanceof \think\Collection || $vo['son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo2): $mod = ($i % 2 );++$i;if(isset($role_menus[$vo2['id']]) && $role_menus[$vo2['id']]!==0): ?>
                    <li class="tab">
                        <div class="list_cg list_back">
                            <i class="icon-new icon-<?php echo htmlentities((string) $vo2['icon']); ?>"></i>
                            <a href="<?php if($vo2['url']): ?>/admin/<?php echo htmlentities((string) $vo2['url']); else: ?>javascript:void(0)<?php endif; ?>" target="mainframe"><?php echo htmlentities((string) $vo2['title']); ?> </a>
                            <?php if(!$vo2['url']): ?><em></em><?php endif; ?>
                        </div>

                        <?php if(count($vo2['son']) !== 0): ?>
                        <ul class="menu-child" style="display:none;">
                            <?php if(is_array($vo2['son']) || $vo2['son'] instanceof \think\Collection || $vo2['son'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo2['son'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo3): $mod = ($i % 2 );++$i;if(isset($role_menus[$vo3['id']]) && $role_menus[$vo3['id']]!==0): ?>
                            <li><a href="/admin/<?php echo htmlentities((string) $vo3['url']); ?>" target="mainframe"><?php echo htmlentities((string) $vo3['title']); ?></a></li>
                            <?php endif; ?>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                        <hr>
                        <?php endif; ?>
                    </li>
                    <?php endif; ?>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                <?php endif; ?>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </ul>
        </div>
    </div>

    <script src="/static/admin/js/jquery.min.js"></script>
    <script type="text/javascript" src="/static/admin/js/menu.js"></script>

</body>
</html>