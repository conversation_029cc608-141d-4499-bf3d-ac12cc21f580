<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>创建订单</title>

    {include file="common:head"}
</head>

<style>
    .class_con label{
        width: 220px;
    }
    .class_con span{
        color: #464df4;
        margin-left: 0;
    }
</style>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;创建订单
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="tab1">用户信息</button>
                <button class="tab-button" data-tab="tab2">产品/服务</button>
            </div>

            <div class="tab-content">
                <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                    <div id="tab1" class="tab-pane active">
                        <div class="class_con">
                            <label>用户：</label>
                            <select name="user_id" class="l_xiang">
                                <option value="">请选择</option>
                                {volist name="users" id="vo"}
                                    <option value="{$vo.id}">{$vo.email}</option>
                                {/volist}
                            </select>
                        </div>

                        <div class="class_con">
                            <label>Email：</label>
                            <input type="text" name="email" />
                            <span class="must-input">*</span>
                            <span class="input-tips"> 如果系统中不存在该Email账号，则自动创建用户账号，初始密码“opd123456”；如账号已存在，则自动归入该账号</span>
                        </div>

                        <div class="class_con">
                            <label>Country/Region：</label>
                            <input type="text" name="country" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>First Name：</label>
                            <input type="text" name="first_name" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>Last Name：</label>
                            <input type="text" name="last_name" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>Title:</label>
                            <input type="text" name="title" />
                        </div>

                        <div class="class_con">
                            <label>Phone:</label>
                            <input type="text" name="phone" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>Organization/Institution/Corporation：</label>
                            <input type="text" name="organization" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>Shipping Address:</label>
                            <input type="text" name="shipping_address" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>City:</label>
                            <input type="text" name="city" />
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>Postcode:</label>
                            <input type="text" name="postcode" />
                            <span class="must-input">*</span>
                        </div>
                    </div>

                    <div id="tab2" class="tab-pane">
                        <div class="class_con">
                            <label>products：</label>
                            <select name="product_id" id="product" class="l_xiang">
                                <option value="">product</option>
                                {volist name="product" id="vo"}
                                    <option value="{$vo.id}">{$vo.name}</option>
                                {/volist}
                            </select>
                            <span class="must-input">*</span>
                        </div>

                        <div class="class_con">
                            <label>service：</label>
                            <select name="service_id" id="service" class="l_xiang">
                                <option value="">service</option>
                            </select>
                        </div>
                    </div>

                    <div class="de_y">
                        <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                        <a href="{:url('index')}" class="de_y_r" >返回</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {include file="common:foot"}

    <script>
        // 监听用户选择变化
        $('select[name="user_id"]').change(function() {
            var userId = $(this).val();
            if (userId) {
                // 发送AJAX请求获取用户信息
                $.ajax({
                    url: "/admin/Order/getUserData",
                    type: "POST",
                    data: {user_id: userId},
                    dataType: "json",
                    success: function(response) {
                        if (response.code == 1) {
                            // 填充表单字段
                            $('input[name="email"]').val(response.data.email);
                            $('input[name="country"]').val(response.data.country);
                            $('input[name="first_name"]').val(response.data.first_name);
                            $('input[name="last_name"]').val(response.data.last_name);
                            $('input[name="title"]').val(response.data.title);
                            $('input[name="phone"]').val(response.data.phone);
                            $('input[name="organization"]').val(response.data.organization);
                        } else {
                            layer.msg(response.msg);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请重试');
                    }
                });
            } else {
                // 清空表单
                $('input[name="email"], input[name="country"], input[name="first_name"], input[name="last_name"], input[name="title"], input[name="phone"], input[name="organization"]').val('');
            }
        });

        // 监听 product 变化
        $('#product').on('change', function() {
            var productId = $(this).val();
            if (!productId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: '/getServices/' + productId,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        var $serviceSelect = $('#service');
                        $serviceSelect.empty(); // 清空现有选项

                        // 添加默认选项
                        $serviceSelect.append('<option value="" disabled selected>service</option>');

                        // 动态添加 service 选项
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });

                        // 更新 UI 颜色（如果之前有样式逻辑）
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>